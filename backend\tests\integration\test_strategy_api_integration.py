"""
策略管理API集成测试
测试前后端API集成、数据库操作、完整工作流程
"""

import unittest
import requests
import json
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

class TestStrategyAPIIntegration(unittest.TestCase):
    """策略管理API集成测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.base_url = "http://localhost:8000/api/v1"
        cls.strategy_id = None
        
        # 等待服务启动
        cls.wait_for_service()
    
    @classmethod
    def wait_for_service(cls, timeout=30):
        """等待服务启动"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{cls.base_url}/health", timeout=5)
                if response.status_code == 200:
                    print("✅ 服务已启动")
                    return
            except requests.exceptions.RequestException:
                pass
            time.sleep(1)
        
        raise Exception("❌ 服务启动超时")
    
    def test_01_get_strategy_types(self):
        """测试获取策略类型列表"""
        response = requests.get(f"{self.base_url}/strategy-types")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        self.assertTrue(data["success"])
        self.assertIn("data", data)
        self.assertIsInstance(data["data"], list)
        self.assertGreater(len(data["data"]), 0)
        
        # 验证策略类型结构
        strategy_type = data["data"][0]
        self.assertIn("value", strategy_type)
        self.assertIn("label", strategy_type)
        self.assertIn("categories", strategy_type)
    
    def test_02_get_strategy_templates(self):
        """测试获取策略模板列表"""
        response = requests.get(f"{self.base_url}/strategy-templates")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        self.assertTrue(data["success"])
        self.assertIn("data", data)
        self.assertIsInstance(data["data"], list)
    
    def test_03_generate_code_from_template(self):
        """测试从模板生成代码"""
        template_id = 1  # 假设存在模板ID为1
        parameters = {
            "short_period": 5,
            "long_period": 20,
            "price_type": "close",
            "use_stop_loss": True,
            "stop_loss_rate": 0.02
        }
        
        response = requests.post(
            f"{self.base_url}/strategy-templates/{template_id}/generate",
            json={"parameters": parameters}
        )
        
        if response.status_code == 200:
            data = response.json()
            self.assertTrue(data["success"])
            self.assertIn("code", data)
            self.assertIsInstance(data["code"], str)
            self.assertGreater(len(data["code"]), 0)
        else:
            # 如果模板不存在，跳过测试
            self.skipTest("模板不存在，跳过代码生成测试")
    
    def test_04_validate_strategy_code(self):
        """测试策略代码验证"""
        valid_code = '''
def strategy_logic(data, params):
    """简单的策略逻辑"""
    signals = []
    
    # 计算移动平均线
    short_ma = data['close'].rolling(5).mean()
    long_ma = data['close'].rolling(20).mean()
    
    # 生成交易信号
    for i in range(1, len(data)):
        if short_ma.iloc[i] > long_ma.iloc[i] and short_ma.iloc[i-1] <= long_ma.iloc[i-1]:
            signals.append({
                'action': 'BUY',
                'price': data['close'].iloc[i],
                'timestamp': i
            })
        elif short_ma.iloc[i] < long_ma.iloc[i] and short_ma.iloc[i-1] >= long_ma.iloc[i-1]:
            signals.append({
                'action': 'SELL',
                'price': data['close'].iloc[i],
                'timestamp': i
            })
    
    return signals
'''
        
        response = requests.post(
            f"{self.base_url}/strategies/validate-code",
            json={
                "code": valid_code,
                "code_type": "python"
            }
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        self.assertTrue(data["valid"])
        self.assertEqual(len(data.get("errors", [])), 0)
    
    def test_05_convert_pinescript(self):
        """测试Pine Script转换"""
        pine_code = '''
//@version=5
strategy("Simple MA Strategy", overlay=true)

length = input.int(20, title="Length")
ma = ta.sma(close, length)

if ta.crossover(close, ma)
    strategy.entry("Long", strategy.long)

if ta.crossunder(close, ma)
    strategy.close("Long")
'''
        
        response = requests.post(
            f"{self.base_url}/strategies/convert-pinescript",
            json={"pine_code": pine_code}
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        self.assertTrue(data["success"])
        self.assertIn("converted_code", data)
        self.assertIsInstance(data["converted_code"], str)
        self.assertIn("def strategy_logic", data["converted_code"])
    
    def test_06_check_pinescript_compatibility(self):
        """测试Pine Script兼容性检查"""
        pine_code = '''
//@version=5
strategy("Test Strategy", overlay=true)

length = input.int(20)
ma = ta.sma(close, length)

if ta.crossover(close, ma)
    strategy.entry("Long", strategy.long)
'''
        
        response = requests.post(
            f"{self.base_url}/strategies/check-pinescript-compatibility",
            json={"pine_code": pine_code}
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        self.assertTrue(data["success"])
        self.assertIn("compatibility_score", data)
        self.assertIn("warnings", data)
        self.assertIn("recommendations", data)
        self.assertGreaterEqual(data["compatibility_score"], 0.0)
        self.assertLessEqual(data["compatibility_score"], 1.0)
    
    def test_07_create_strategy(self):
        """测试创建策略"""
        strategy_data = {
            "name": "集成测试策略",
            "type": "trend_following",
            "category": "dual_ma_cross",
            "description": "用于集成测试的双均线策略",
            "code_type": "python",
            "code_content": '''
def strategy_logic(data, params):
    signals = []
    short_ma = data['close'].rolling(5).mean()
    long_ma = data['close'].rolling(20).mean()
    
    for i in range(1, len(data)):
        if short_ma.iloc[i] > long_ma.iloc[i] and short_ma.iloc[i-1] <= long_ma.iloc[i-1]:
            signals.append({'action': 'BUY', 'price': data['close'].iloc[i]})
        elif short_ma.iloc[i] < long_ma.iloc[i] and short_ma.iloc[i-1] >= long_ma.iloc[i-1]:
            signals.append({'action': 'SELL', 'price': data['close'].iloc[i]})
    
    return signals
''',
            "parameters": {
                "short_period": 5,
                "long_period": 20
            },
            "symbol": "BTC/USDT",
            "timeframe": "1h"
        }
        
        response = requests.post(
            f"{self.base_url}/strategies",
            json=strategy_data
        )
        
        if response.status_code == 201:
            data = response.json()
            self.assertTrue(data["success"])
            self.assertIn("data", data)
            self.assertIn("id", data["data"])
            
            # 保存策略ID用于后续测试
            self.__class__.strategy_id = data["data"]["id"]
        else:
            # 如果创建失败，可能是因为认证问题，跳过测试
            self.skipTest("策略创建失败，可能需要认证")
    
    def test_08_get_strategies(self):
        """测试获取策略列表"""
        response = requests.get(f"{self.base_url}/strategies")
        
        if response.status_code == 200:
            data = response.json()
            self.assertTrue(data["success"])
            self.assertIn("data", data)
            self.assertIsInstance(data["data"], list)
        else:
            # 如果获取失败，可能是因为认证问题，跳过测试
            self.skipTest("获取策略列表失败，可能需要认证")
    
    def test_09_get_strategy_detail(self):
        """测试获取策略详情"""
        if not self.strategy_id:
            self.skipTest("没有可用的策略ID")
        
        response = requests.get(f"{self.base_url}/strategies/{self.strategy_id}")
        
        if response.status_code == 200:
            data = response.json()
            self.assertTrue(data["success"])
            self.assertIn("data", data)
            self.assertEqual(data["data"]["id"], self.strategy_id)
        else:
            self.skipTest("获取策略详情失败，可能需要认证")
    
    def test_10_update_strategy(self):
        """测试更新策略"""
        if not self.strategy_id:
            self.skipTest("没有可用的策略ID")
        
        update_data = {
            "name": "更新后的集成测试策略",
            "description": "已更新的策略描述"
        }
        
        response = requests.put(
            f"{self.base_url}/strategies/{self.strategy_id}",
            json=update_data
        )
        
        if response.status_code == 200:
            data = response.json()
            self.assertTrue(data["success"])
        else:
            self.skipTest("更新策略失败，可能需要认证")
    
    def test_11_delete_strategy(self):
        """测试删除策略"""
        if not self.strategy_id:
            self.skipTest("没有可用的策略ID")
        
        response = requests.delete(f"{self.base_url}/strategies/{self.strategy_id}")
        
        if response.status_code == 200:
            data = response.json()
            self.assertTrue(data["success"])
        else:
            self.skipTest("删除策略失败，可能需要认证")

class TestStrategyWorkflow(unittest.TestCase):
    """测试策略管理完整工作流程"""
    
    def setUp(self):
        """测试前准备"""
        self.base_url = "http://localhost:8000/api/v1"
    
    def test_complete_strategy_workflow(self):
        """测试完整的策略创建工作流程"""
        # 1. 获取策略类型
        types_response = requests.get(f"{self.base_url}/strategy-types")
        self.assertEqual(types_response.status_code, 200)
        
        # 2. 获取策略模板
        templates_response = requests.get(f"{self.base_url}/strategy-templates")
        self.assertEqual(templates_response.status_code, 200)
        
        # 3. 验证Pine Script代码
        pine_code = '''
//@version=5
strategy("Workflow Test", overlay=true)
ma = ta.sma(close, 20)
if ta.crossover(close, ma)
    strategy.entry("Long", strategy.long)
'''
        
        compatibility_response = requests.post(
            f"{self.base_url}/strategies/check-pinescript-compatibility",
            json={"pine_code": pine_code}
        )
        self.assertEqual(compatibility_response.status_code, 200)
        
        # 4. 转换Pine Script代码
        convert_response = requests.post(
            f"{self.base_url}/strategies/convert-pinescript",
            json={"pine_code": pine_code}
        )
        self.assertEqual(convert_response.status_code, 200)
        
        # 5. 验证转换后的Python代码
        converted_data = convert_response.json()
        if converted_data["success"]:
            validate_response = requests.post(
                f"{self.base_url}/strategies/validate-code",
                json={
                    "code": converted_data["converted_code"],
                    "code_type": "python"
                }
            )
            self.assertEqual(validate_response.status_code, 200)

if __name__ == '__main__':
    # 运行集成测试
    unittest.main(verbosity=2)
