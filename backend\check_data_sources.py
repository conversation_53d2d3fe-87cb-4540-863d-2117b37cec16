#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查数据源表
"""

import os
import sqlite3
import json

# 数据库路径
DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app.db')

def check_data_sources():
    """检查数据源表"""
    print(f"检查数据库: {DB_PATH}")

    if not os.path.exists(DB_PATH):
        print(f"数据库文件不存在: {DB_PATH}")
        return

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # 获取所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()

    print("数据库中的表:")
    for table in tables:
        print(f"  - {table[0]}")

    # 检查data_sources表是否存在
    if ('data_sources',) in tables:
        print("\n数据源表存在")
        cursor.execute("PRAGMA table_info(data_sources)")
        columns = cursor.fetchall()
        print("  列:")
        for column in columns:
            print(f"    - {column[1]} ({column[2]})")

        # 获取数据源数量
        cursor.execute("SELECT COUNT(*) FROM data_sources")
        count = cursor.fetchone()[0]
        print(f"  数据源数量: {count}")

        # 获取所有数据源
        if count > 0:
            cursor.execute("SELECT * FROM data_sources")
            sources = cursor.fetchall()
            print("\n数据源列表:")
            for source in sources:
                print(f"  ID: {source[0]}")
                print(f"  名称: {source[1]}")
                print(f"  符号: {source[2] if len(source) > 2 else 'N/A'}")
                print(f"  时间级别: {source[3] if len(source) > 3 else 'N/A'}")
                print("  ---")
    else:
        print("\n数据源表不存在")

    conn.close()

if __name__ == "__main__":
    check_data_sources()
