#!/usr/bin/env python3
"""
测试axios响应格式问题
验证后端返回的实际数据格式
"""

import requests
import json

def test_axios_response_format():
    """测试axios响应格式"""
    
    print("🔧 测试axios响应格式问题...")
    
    # 1. 登录获取token
    print("\n1. 登录获取认证token...")
    login_url = "http://localhost:8000/api/v1/auth/token"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. 测试API趋势数据的实际响应格式
    print("\n2. 测试API趋势数据的实际响应格式...")
    trend_url = "http://localhost:8000/api/v1/performance/metrics/api?limit=5"
    
    try:
        response = requests.get(trend_url, headers=headers)
        if response.status_code == 200:
            trend_data = response.json()
            print(f"✅ API请求成功")
            print(f"   - 响应类型: {type(trend_data)}")
            print(f"   - 响应结构: {json.dumps(trend_data, indent=2, ensure_ascii=False)}")
            
            # 检查响应格式
            if isinstance(trend_data, dict):
                print(f"\n📋 响应是字典对象:")
                print(f"   - 包含success字段: {'success' in trend_data}")
                print(f"   - 包含data字段: {'data' in trend_data}")
                print(f"   - 包含message字段: {'message' in trend_data}")
                
                if trend_data.get("success") and trend_data.get("data"):
                    data_array = trend_data["data"]
                    print(f"   - data字段类型: {type(data_array)}")
                    print(f"   - data数组长度: {len(data_array) if isinstance(data_array, list) else 'N/A'}")
                    
                    if isinstance(data_array, list) and len(data_array) > 0:
                        sample = data_array[0]
                        print(f"   - 示例数据点: {sample}")
                        
                        # 检查必需字段
                        required_fields = ['timestamp', 'avg_time', 'max_time']
                        missing_fields = [f for f in required_fields if f not in sample]
                        
                        if missing_fields:
                            print(f"   ❌ 缺少必需字段: {missing_fields}")
                            return False
                        else:
                            print(f"   ✅ 数据格式正确")
                            return True
                else:
                    print(f"   ❌ 响应格式错误：success或data字段缺失")
                    return False
            elif isinstance(trend_data, list):
                print(f"\n📋 响应是数组对象:")
                print(f"   - 数组长度: {len(trend_data)}")
                if len(trend_data) > 0:
                    sample = trend_data[0]
                    print(f"   - 示例数据点: {sample}")
                    print(f"   ⚠️ 这种格式说明axios拦截器直接返回了data字段")
                return True
            else:
                print(f"   ❌ 未知响应格式: {type(trend_data)}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 axios响应格式问题诊断测试")
    print("=" * 60)
    
    success = test_axios_response_format()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 响应格式测试完成！")
        print("✅ 后端API返回格式正确")
        print("✅ 前端应该能正确处理响应数据")
        print("\n📋 修复说明:")
        print("  - 前端代码已适配axios响应拦截器的处理方式")
        print("  - 支持标准格式和数组格式两种响应")
        print("  - 增强了错误处理和调试信息")
    else:
        print("❌ 响应格式测试失败！")
        print("需要进一步检查问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
