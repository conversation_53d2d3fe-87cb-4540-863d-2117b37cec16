# 前端服务启动脚本 - 确保启动在8080端口
# 检查并关闭占用8080端口的进程，然后启动前端服务

Write-Host "=== 前端服务启动脚本 ===" -ForegroundColor Green
Write-Host "目标端口: 8080" -ForegroundColor Yellow

# 检查8080端口占用情况
Write-Host "`n检查8080端口占用情况..." -ForegroundColor Cyan
$port8080 = netstat -ano | findstr ":8080"

if ($port8080) {
    Write-Host "发现8080端口被占用:" -ForegroundColor Yellow
    Write-Host $port8080 -ForegroundColor White
    
    # 提取占用8080端口的进程ID
    $processes = netstat -ano | findstr ":8080" | findstr "LISTENING"
    
    if ($processes) {
        foreach ($line in $processes) {
            # 提取PID (最后一列)
            $parts = $line -split '\s+' | Where-Object { $_ -ne '' }
            $pid = $parts[-1]
            
            if ($pid -and $pid -match '^\d+$') {
                Write-Host "尝试关闭占用8080端口的进程 PID: $pid" -ForegroundColor Yellow
                
                try {
                    # 获取进程信息
                    $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                    if ($process) {
                        Write-Host "进程名称: $($process.ProcessName)" -ForegroundColor White
                        
                        # 如果是Node.js进程，很可能是之前的前端服务
                        if ($process.ProcessName -eq "node") {
                            Write-Host "检测到Node.js进程，正在关闭..." -ForegroundColor Yellow
                            Stop-Process -Id $pid -Force
                            Write-Host "已关闭进程 PID: $pid" -ForegroundColor Green
                        } else {
                            Write-Host "警告: 进程 $($process.ProcessName) 不是Node.js进程，跳过关闭" -ForegroundColor Red
                        }
                    }
                } catch {
                    Write-Host "无法关闭进程 PID: $pid - $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
        
        # 等待端口释放
        Write-Host "等待端口释放..." -ForegroundColor Cyan
        Start-Sleep -Seconds 3
        
        # 再次检查端口
        $port8080_after = netstat -ano | findstr ":8080" | findstr "LISTENING"
        if ($port8080_after) {
            Write-Host "警告: 8080端口仍被占用，前端可能会启动到其他端口" -ForegroundColor Red
        } else {
            Write-Host "8080端口已释放" -ForegroundColor Green
        }
    }
} else {
    Write-Host "8080端口未被占用" -ForegroundColor Green
}

# 检查前端目录是否存在
if (-not (Test-Path "frontend")) {
    Write-Host "错误: 未找到frontend目录" -ForegroundColor Red
    exit 1
}

# 进入前端目录
Set-Location frontend

# 检查package.json是否存在
if (-not (Test-Path "package.json")) {
    Write-Host "错误: 未找到package.json文件" -ForegroundColor Red
    exit 1
}

# 检查node_modules是否存在
if (-not (Test-Path "node_modules")) {
    Write-Host "node_modules不存在，正在安装依赖..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "依赖安装失败" -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n启动前端开发服务器..." -ForegroundColor Green
Write-Host "目标地址: http://localhost:8080" -ForegroundColor Yellow

# 启动前端服务，明确指定端口
npm run serve -- --port 8080

# 如果启动失败，尝试其他方法
if ($LASTEXITCODE -ne 0) {
    Write-Host "使用npm run serve失败，尝试使用vue-cli-service..." -ForegroundColor Yellow
    npx vue-cli-service serve --port 8080
}
