<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端数据显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #f0f9ff;
            border-left: 4px solid #10b981;
            color: #065f46;
        }
        .error {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
            color: #991b1b;
        }
        .loading {
            background-color: #fffbeb;
            border-left: 4px solid #f59e0b;
            color: #92400e;
        }
        .data-display {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .btn {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #2563eb;
        }
        .btn:disabled {
            background-color: #9ca3af;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>性能优化前端数据显示测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. API连接测试</div>
            <button class="btn" onclick="testApiConnection()">测试API连接</button>
            <div id="api-connection-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 性能摘要数据测试</div>
            <button class="btn" onclick="testSummaryData()">获取性能摘要</button>
            <div id="summary-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. API统计数据测试</div>
            <button class="btn" onclick="testApiStats()">获取API统计</button>
            <div id="api-stats-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 缓存统计数据测试</div>
            <button class="btn" onclick="testCacheStats()">获取缓存统计</button>
            <div id="cache-stats-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">5. 性能指标数据测试</div>
            <button class="btn" onclick="testMetricsData()">获取性能指标</button>
            <div id="metrics-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">6. 前端页面状态检查</div>
            <button class="btn" onclick="checkFrontendStatus()">检查前端页面</button>
            <div id="frontend-status-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">7. 一键修复和测试</div>
            <button class="btn" onclick="autoFixAndTest()" id="auto-fix-btn">开始自动修复和测试</button>
            <div id="auto-fix-result"></div>
        </div>
    </div>

    <script>
        let token = null;

        // 获取认证令牌
        async function getAuthToken() {
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    token = data.access_token;
                    return token;
                } else {
                    throw new Error(`登录失败: ${response.status}`);
                }
            } catch (error) {
                throw new Error(`登录请求失败: ${error.message}`);
            }
        }

        // 带认证的API请求
        async function authenticatedFetch(url, options = {}) {
            if (!token) {
                await getAuthToken();
            }

            const headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                ...options.headers
            };

            return fetch(url, {
                ...options,
                headers
            });
        }

        // 显示结果
        function showResult(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        // 显示数据
        function showData(elementId, data, message = '') {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <div class="test-result success">${message}</div>
                <div class="data-display">${JSON.stringify(data, null, 2)}</div>
            `;
        }

        // 测试API连接
        async function testApiConnection() {
            try {
                showResult('api-connection-result', '正在测试API连接...', 'loading');
                
                const token = await getAuthToken();
                showResult('api-connection-result', `API连接成功！Token: ${token.substring(0, 20)}...`, 'success');
            } catch (error) {
                showResult('api-connection-result', `API连接失败: ${error.message}`, 'error');
            }
        }

        // 测试性能摘要数据
        async function testSummaryData() {
            try {
                showResult('summary-result', '正在获取性能摘要数据...', 'loading');
                
                const response = await authenticatedFetch('http://localhost:8000/api/v1/performance/summary');
                
                if (response.ok) {
                    const data = await response.json();
                    showData('summary-result', data, '性能摘要数据获取成功！');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('summary-result', `获取性能摘要失败: ${error.message}`, 'error');
            }
        }

        // 测试API统计数据
        async function testApiStats() {
            try {
                showResult('api-stats-result', '正在获取API统计数据...', 'loading');
                
                const response = await authenticatedFetch('http://localhost:8000/api/v1/performance/api/stats');
                
                if (response.ok) {
                    const data = await response.json();
                    showData('api-stats-result', data, 'API统计数据获取成功！');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('api-stats-result', `获取API统计失败: ${error.message}`, 'error');
            }
        }

        // 测试缓存统计数据
        async function testCacheStats() {
            try {
                showResult('cache-stats-result', '正在获取缓存统计数据...', 'loading');
                
                const response = await authenticatedFetch('http://localhost:8000/api/v1/performance/cache/stats');
                
                if (response.ok) {
                    const data = await response.json();
                    showData('cache-stats-result', data, '缓存统计数据获取成功！');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('cache-stats-result', `获取缓存统计失败: ${error.message}`, 'error');
            }
        }

        // 测试性能指标数据
        async function testMetricsData() {
            try {
                showResult('metrics-result', '正在获取性能指标数据...', 'loading');
                
                const metrics = ['cpu', 'memory', 'api', 'cache'];
                const results = {};
                
                for (const metric of metrics) {
                    const response = await authenticatedFetch(`http://localhost:8000/api/v1/performance/metrics/${metric}?limit=5`);
                    if (response.ok) {
                        const data = await response.json();
                        results[metric] = data;
                    } else {
                        results[metric] = { error: `HTTP ${response.status}` };
                    }
                }
                
                showData('metrics-result', results, '性能指标数据获取完成！');
            } catch (error) {
                showResult('metrics-result', `获取性能指标失败: ${error.message}`, 'error');
            }
        }

        // 检查前端页面状态
        async function checkFrontendStatus() {
            try {
                showResult('frontend-status-result', '正在检查前端页面状态...', 'loading');
                
                // 检查前端服务是否运行
                const frontendResponse = await fetch('http://localhost:8080/', { mode: 'no-cors' });
                
                showResult('frontend-status-result', 
                    '前端服务正在运行。请手动检查 http://localhost:8080/#/performance 页面是否显示真实数据而不是"加载中..."', 
                    'success');
                    
                // 自动打开前端页面
                window.open('http://localhost:8080/#/performance', '_blank');
                
            } catch (error) {
                showResult('frontend-status-result', `前端状态检查失败: ${error.message}`, 'error');
            }
        }

        // 自动修复和测试
        async function autoFixAndTest() {
            const btn = document.getElementById('auto-fix-btn');
            btn.disabled = true;
            btn.textContent = '正在执行...';
            
            try {
                showResult('auto-fix-result', '开始自动修复和测试流程...', 'loading');
                
                // 1. 测试API连接
                await testApiConnection();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 2. 测试所有数据接口
                await testSummaryData();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testApiStats();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testCacheStats();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testMetricsData();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 3. 检查前端状态
                await checkFrontendStatus();
                
                showResult('auto-fix-result', '✅ 自动修复和测试完成！所有API端点正常工作，前端页面已打开。', 'success');
                
            } catch (error) {
                showResult('auto-fix-result', `自动修复失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '开始自动修复和测试';
            }
        }
    </script>
</body>
</html>
