#!/usr/bin/env python3
"""
测试缓存活动
"""

import requests
import json
import time

def test_cache_activity():
    """测试缓存活动"""
    
    print("🔍 测试缓存活动...")
    print("=" * 60)
    
    # 获取认证token
    print("\n1. 🔐 获取认证令牌...")
    try:
        auth_response = requests.post('http://localhost:8000/api/v1/auth/token', 
                                     json={'username': 'admin', 'password': 'admin123'})
        
        if auth_response.status_code != 200:
            print(f"❌ 认证失败: {auth_response.status_code}")
            return
        
        token = auth_response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print(f"✅ 认证成功")
        
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return
    
    # 检查初始缓存状态
    print("\n2. 📊 检查初始缓存状态...")
    response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
    if response.status_code == 200:
        initial_stats = response.json()['data']
        print(f"   初始缓存统计: {json.dumps(initial_stats, ensure_ascii=False)}")
    else:
        print(f"   ❌ 获取缓存统计失败: {response.status_code}")
        return
    
    # 多次调用有缓存装饰器的API
    print("\n3. 🔄 多次调用API来触发缓存...")
    
    # 调用性能摘要API（有缓存装饰器）
    for i in range(5):
        print(f"   第{i+1}次调用性能摘要API...")
        response = requests.get('http://localhost:8000/api/v1/performance/summary', headers=headers)
        if response.status_code == 200:
            print(f"      ✅ 调用成功")
        else:
            print(f"      ❌ 调用失败: {response.status_code}")
        time.sleep(0.5)
    
    # 调用性能分析API（有缓存装饰器）
    for i in range(3):
        print(f"   第{i+1}次调用性能分析API...")
        response = requests.get('http://localhost:8000/api/v1/performance/analysis', headers=headers)
        if response.status_code == 200:
            print(f"      ✅ 调用成功")
        else:
            print(f"      ❌ 调用失败: {response.status_code}")
        time.sleep(0.5)
    
    # 调用API统计API（有缓存装饰器）
    for i in range(3):
        print(f"   第{i+1}次调用API统计API...")
        response = requests.get('http://localhost:8000/api/v1/performance/api/stats', headers=headers)
        if response.status_code == 200:
            print(f"      ✅ 调用成功")
        else:
            print(f"      ❌ 调用失败: {response.status_code}")
        time.sleep(0.5)
    
    # 检查缓存状态变化
    print("\n4. 📈 检查缓存状态变化...")
    response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
    if response.status_code == 200:
        final_stats = response.json()['data']
        print(f"   最终缓存统计: {json.dumps(final_stats, ensure_ascii=False)}")
        
        # 比较变化
        print(f"\n   📊 缓存活动分析:")
        print(f"      缓存大小: {initial_stats['size']} → {final_stats['size']} (变化: +{final_stats['size'] - initial_stats['size']})")
        print(f"      命中次数: {initial_stats['hits']} → {final_stats['hits']} (变化: +{final_stats['hits'] - initial_stats['hits']})")
        print(f"      未命中次数: {initial_stats['misses']} → {final_stats['misses']} (变化: +{final_stats['misses'] - initial_stats['misses']})")
        print(f"      命中率: {initial_stats['hit_rate']:.2%} → {final_stats['hit_rate']:.2%}")
        
        if final_stats['size'] > initial_stats['size'] or final_stats['hits'] > initial_stats['hits']:
            print(f"   ✅ 缓存系统正在工作！")
        else:
            print(f"   ❌ 缓存系统可能没有被使用")
            
    else:
        print(f"   ❌ 获取最终缓存统计失败: {response.status_code}")
    
    # 测试直接调用缓存管理器
    print("\n5. 🧪 测试直接调用缓存管理器...")
    try:
        # 这里我们需要直接导入并测试缓存管理器
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
        
        from app.utils.performance_utils import global_cache
        
        print(f"   缓存管理器实例: {global_cache}")
        print(f"   缓存管理器类型: {type(global_cache)}")
        
        # 手动添加一些缓存项
        global_cache.set("test_key_1", "test_value_1", 60)
        global_cache.set("test_key_2", "test_value_2", 60)
        
        # 获取缓存项
        value1 = global_cache.get("test_key_1")
        value2 = global_cache.get("test_key_2")
        value3 = global_cache.get("non_existent_key")
        
        print(f"   手动测试结果:")
        print(f"      test_key_1: {value1}")
        print(f"      test_key_2: {value2}")
        print(f"      non_existent_key: {value3}")
        
        # 获取统计
        manual_stats = global_cache.get_stats()
        print(f"   手动测试后的缓存统计: {json.dumps(manual_stats, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"   ❌ 直接测试缓存管理器失败: {e}")
    
    print("\n" + "=" * 60)
    print("🔍 缓存活动测试完成")

if __name__ == "__main__":
    test_cache_activity()
