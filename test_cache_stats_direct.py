#!/usr/bin/env python3
"""
直接测试缓存统计API
"""

import requests
import json

def test_cache_stats():
    """直接测试缓存统计API"""
    
    print("🔍 直接测试缓存统计API...")
    print("=" * 60)
    
    # 获取认证token
    print("\n1. 🔐 获取认证令牌...")
    try:
        auth_response = requests.post('http://localhost:8000/api/v1/auth/token', 
                                     json={'username': 'admin', 'password': 'admin123'})
        
        print(f"   认证响应状态: {auth_response.status_code}")
        print(f"   认证响应内容: {auth_response.text}")
        
        if auth_response.status_code != 200:
            print(f"❌ 认证失败: {auth_response.status_code}")
            return
        
        token = auth_response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print(f"✅ 认证成功")
        
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return
    
    # 测试缓存统计API
    print("\n2. 📊 测试缓存统计API...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
        
        print(f"   响应状态: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n   解析后的JSON:")
            print(f"   {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('success'):
                cache_data = data.get('data', {})
                print(f"\n   ✅ 缓存统计数据:")
                print(f"      缓存大小: {cache_data.get('size', 'N/A')}")
                print(f"      最大大小: {cache_data.get('max_size', 'N/A')}")
                print(f"      命中次数: {cache_data.get('hits', 'N/A')}")
                print(f"      未命中次数: {cache_data.get('misses', 'N/A')}")
                print(f"      命中率: {cache_data.get('hit_rate', 'N/A')}")
                print(f"      项目数量: {cache_data.get('items_count', 'N/A')}")
            else:
                print(f"   ❌ API返回失败: {data.get('message', '未知错误')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 再次调用以触发缓存
    print("\n3. 🔄 再次调用以触发缓存...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                cache_data = data.get('data', {})
                print(f"   ✅ 第二次调用缓存统计:")
                print(f"      缓存大小: {cache_data.get('size', 'N/A')}")
                print(f"      最大大小: {cache_data.get('max_size', 'N/A')}")
                print(f"      命中次数: {cache_data.get('hits', 'N/A')}")
                print(f"      未命中次数: {cache_data.get('misses', 'N/A')}")
                print(f"      命中率: {cache_data.get('hit_rate', 'N/A')}")
                print(f"      项目数量: {cache_data.get('items_count', 'N/A')}")
            else:
                print(f"   ❌ API返回失败: {data.get('message', '未知错误')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print("\n" + "=" * 60)
    print("🔍 缓存统计API测试完成")

if __name__ == "__main__":
    test_cache_stats()
