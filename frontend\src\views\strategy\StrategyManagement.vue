<template>
  <div class="strategy-management">
    <!-- 量子背景 -->
    <div class="quantum-background">
      <div class="quantum-particles"></div>
      <div class="quantum-grid"></div>
    </div>

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="quantum-title">
          <span class="title-glow">策略管理中心</span>
          <div class="title-subtitle">管理和监控您的量化交易策略</div>
        </h1>
      </div>
      <div class="header-right">
        <button class="quantum-button primary" @click="createStrategy">
          <i class="el-icon-plus"></i>
          <span>创建策略</span>
          <div class="button-glow"></div>
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <i class="el-icon-document"></i>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.total || 0 }}</div>
            <div class="stat-label">总策略数</div>
          </div>
        </div>
        <div class="stat-glow"></div>
      </div>

      <div class="stat-card active">
        <div class="stat-content">
          <div class="stat-icon">
            <i class="el-icon-video-play"></i>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.active || 0 }}</div>
            <div class="stat-label">运行中</div>
          </div>
        </div>
        <div class="stat-glow"></div>
      </div>

      <div class="stat-card inactive">
        <div class="stat-content">
          <div class="stat-icon">
            <i class="el-icon-video-pause"></i>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.inactive || 0 }}</div>
            <div class="stat-label">已停止</div>
          </div>
        </div>
        <div class="stat-glow"></div>
      </div>

      <div class="stat-card new">
        <div class="stat-content">
          <div class="stat-icon">
            <i class="el-icon-plus"></i>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ todayCreated }}</div>
            <div class="stat-label">今日新增</div>
          </div>
        </div>
        <div class="stat-glow"></div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="quantum-card">
        <div class="card-header">
          <h3>筛选条件</h3>
        </div>
        <div class="filter-content">
          <div class="filter-group">
            <label>策略类型</label>
            <select v-model="filterForm.type" @change="loadStrategies" class="quantum-select">
              <option value="">全部类型</option>
              <option
                v-for="type in strategyTypes"
                :key="type.value"
                :value="type.value"
              >
                {{ type.label }}
              </option>
            </select>
          </div>

          <div class="filter-group">
            <label>状态</label>
            <select v-model="filterForm.status" @change="loadStrategies" class="quantum-select">
              <option value="">全部状态</option>
              <option value="created">已创建</option>
              <option value="validated">已验证</option>
              <option value="active">运行中</option>
              <option value="inactive">已停止</option>
            </select>
          </div>

          <div class="filter-group search-group">
            <label>搜索</label>
            <div class="search-input-wrapper">
              <input
                v-model="filterForm.keyword"
                @input="debounceSearch"
                placeholder="搜索策略名称..."
                class="quantum-input"
              />
              <i class="el-icon-search search-icon"></i>
            </div>
          </div>

          <div class="filter-actions">
            <button @click="resetFilter" class="quantum-button secondary">
              <i class="el-icon-refresh"></i>
              重置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 策略列表 -->
    <div class="table-section">
      <div class="quantum-card">
        <div class="card-header">
          <h3>策略列表</h3>
          <div class="table-actions">
            <span class="total-count">共 {{ pagination.total }} 个策略</span>
          </div>
        </div>

        <div class="quantum-table-wrapper" v-loading="loading">
          <table class="quantum-table">
            <thead>
              <tr>
                <th width="50">
                  <input type="checkbox" @change="selectAll" class="quantum-checkbox" />
                </th>
                <th>策略名称</th>
                <th>类型</th>
                <th>分类</th>
                <th>代码类型</th>
                <th>状态</th>
                <th>交易对</th>
                <th>时间周期</th>
                <th>创建时间</th>
                <th width="200">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="strategy in strategies" :key="strategy.id" class="table-row">
                <td>
                  <input
                    type="checkbox"
                    :value="strategy"
                    v-model="selectedStrategies"
                    class="quantum-checkbox"
                  />
                </td>
                <td>
                  <div class="strategy-name">
                    <span class="name">{{ strategy.name }}</span>
                    <span v-if="strategy.template_id" class="template-badge">模板</span>
                  </div>
                </td>
                <td>
                  <span :class="['type-tag', getTypeTagType(strategy.type)]">
                    {{ getTypeLabel(strategy.type) }}
                  </span>
                </td>
                <td>{{ getCategoryLabel(strategy.category) }}</td>
                <td>
                  <span :class="['code-tag', strategy.code_type === 'python' ? 'python' : 'pine']">
                    {{ strategy.code_type === 'python' ? 'Python' : 'Pine Script' }}
                  </span>
                </td>
                <td>
                  <span :class="['status-tag', getStatusTagType(strategy.status)]">
                    {{ getStatusLabel(strategy.status) }}
                  </span>
                </td>
                <td>{{ strategy.symbol }}</td>
                <td>{{ strategy.timeframe }}</td>
                <td>{{ formatDate(strategy.created_at) }}</td>
                <td>
                  <div class="action-buttons">
                    <button @click="viewStrategy(strategy)" class="action-btn view" title="查看">
                      <i class="el-icon-view"></i>
                    </button>
                    <button @click="editStrategy(strategy)" class="action-btn edit" title="编辑">
                      <i class="el-icon-edit"></i>
                    </button>
                    <button @click="copyStrategy(strategy)" class="action-btn copy" title="复制">
                      <i class="el-icon-copy-document"></i>
                    </button>
                    <button @click="deleteStrategy(strategy)" class="action-btn delete" title="删除">
                      <i class="el-icon-delete"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <div class="pagination-info">
            显示第 {{ (pagination.current - 1) * pagination.pageSize + 1 }} -
            {{ Math.min(pagination.current * pagination.pageSize, pagination.total) }} 条，
            共 {{ pagination.total }} 条
          </div>
          <div class="pagination-controls">
            <select v-model="pagination.pageSize" @change="handleSizeChange" class="page-size-select">
              <option :value="10">10 条/页</option>
              <option :value="20">20 条/页</option>
              <option :value="50">50 条/页</option>
              <option :value="100">100 条/页</option>
            </select>
            <div class="page-controls">
              <button
                @click="handleCurrentChange(pagination.current - 1)"
                :disabled="pagination.current <= 1"
                class="page-btn"
              >
                <i class="el-icon-arrow-left"></i>
              </button>
              <span class="page-info">
                {{ pagination.current }} / {{ Math.ceil(pagination.total / pagination.pageSize) }}
              </span>
              <button
                @click="handleCurrentChange(pagination.current + 1)"
                :disabled="pagination.current >= Math.ceil(pagination.total / pagination.pageSize)"
                class="page-btn"
              >
                <i class="el-icon-arrow-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedStrategies.length > 0" class="batch-actions">
      <div class="quantum-card batch-card">
        <div class="batch-content">
          <div class="batch-info">
            <i class="el-icon-check"></i>
            <span>已选择 {{ selectedStrategies.length }} 个策略</span>
          </div>
          <div class="batch-buttons">
            <button @click="batchActivate" class="quantum-button success">
              <i class="el-icon-video-play"></i>
              批量激活
            </button>
            <button @click="batchDeactivate" class="quantum-button warning">
              <i class="el-icon-video-pause"></i>
              批量停止
            </button>
            <button @click="batchDelete" class="quantum-button danger">
              <i class="el-icon-delete"></i>
              批量删除
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Message, MessageBox } from 'element-ui'
import strategyApi from '@/api/strategy'

export default {
  name: 'StrategyManagement',
  data() {
    return {
      // 策略列表数据
      strategies: [],
      loading: false,

      // 防抖定时器
      debounceTimer: null,

      // 统计数据
      stats: {
        total: 0,
        active: 0,
        inactive: 0,
        created: 0,
        validated: 0,
        error: 0
      },

      // 分页数据
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },

      // 筛选表单
      filterForm: {
        type: '',
        status: '',
        keyword: ''
      },

      // 策略类型选项
      strategyTypes: [],

      // 选中的策略
      selectedStrategies: [],

      // 创建策略对话框
      createDialogVisible: false
    }
  },
  computed: {
    // 今日创建的策略数量
    todayCreated() {
      const today = new Date().toDateString()
      return this.strategies.filter(strategy => {
        const createdDate = new Date(strategy.created_at).toDateString()
        return createdDate === today
      }).length
    },

    // 筛选后的策略列表
    filteredStrategies() {
      let filtered = this.strategies

      if (this.filterForm.type) {
        filtered = filtered.filter(strategy => strategy.type === this.filterForm.type)
      }

      if (this.filterForm.status) {
        filtered = filtered.filter(strategy => strategy.status === this.filterForm.status)
      }

      if (this.filterForm.keyword) {
        const keyword = this.filterForm.keyword.toLowerCase()
        filtered = filtered.filter(strategy =>
          strategy.name.toLowerCase().includes(keyword) ||
          strategy.description.toLowerCase().includes(keyword)
        )
      }

      return filtered
    }
  },
  mounted() {
    this.loadStrategies()
    this.loadStats()
    this.loadStrategyTypes()
  },
  methods: {
    // 加载策略列表
    async loadStrategies() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.current,
          per_page: this.pagination.pageSize,
          ...this.filterForm
        }

        const response = await strategyApi.getStrategies(params)
        if (response.success) {
          this.strategies = response.data || []
          this.pagination.total = response.total || 0
        }
      } catch (error) {
        Message.error('加载策略列表失败')
      } finally {
        this.loading = false
      }
    },

    // 加载统计数据
    async loadStats() {
      try {
        const response = await strategyApi.getStrategyStats()
        if (response.success) {
          this.stats = response.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 加载策略类型
    async loadStrategyTypes() {
      try {
        const response = await strategyApi.getStrategyTypes()
        if (response.success) {
          this.strategyTypes = response.data
        }
      } catch (error) {
        console.error('加载策略类型失败:', error)
      }
    },

    // 创建策略
    createStrategy() {
      this.$router.push('/strategy/wizard')
    },

    // 查看策略
    viewStrategy(strategy) {
      this.$router.push(`/strategy/detail/${strategy.id}`)
    },

    // 编辑策略
    editStrategy(strategy) {
      this.$router.push(`/strategy/edit/${strategy.id}`)
    },

    // 复制策略
    async copyStrategy(strategy) {
      try {
        const response = await strategyApi.getStrategy(strategy.id)
        if (response.success) {
          const copyData = {
            ...response.data,
            name: `${response.data.name} - 副本`,
            id: undefined,
            created_at: undefined,
            updated_at: undefined
          }

          const createResponse = await strategyApi.createStrategy(copyData)
          if (createResponse.success) {
            Message.success('策略复制成功')
            this.loadStrategies()
          }
        }
      } catch (error) {
        Message.error('策略复制失败')
      }
    },

    // 删除策略
    async deleteStrategy(strategy) {
      try {
        await MessageBox.confirm(
          `确定要删除策略 "${strategy.name}" 吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        const response = await strategyApi.deleteStrategy(strategy.id)
        if (response.success) {
          Message.success('策略删除成功')
          this.loadStrategies()
          this.loadStats()
        }
      } catch (error) {
        if (error !== 'cancel') {
          Message.error('策略删除失败')
        }
      }
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedStrategies = selection
    },

    // 批量激活
    async batchActivate() {
      Message.info('批量激活功能开发中')
    },

    // 批量停止
    async batchDeactivate() {
      Message.info('批量停止功能开发中')
    },

    // 批量删除
    async batchDelete() {
      try {
        await MessageBox.confirm(
          `确定要删除选中的 ${this.selectedStrategies.length} 个策略吗？`,
          '批量删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 批量删除逻辑
        const deletePromises = this.selectedStrategies.map(strategy =>
          strategyApi.deleteStrategy(strategy.id)
        )

        await Promise.all(deletePromises)
        Message.success('批量删除成功')
        this.selectedStrategies = []
        this.loadStrategies()
        this.loadStats()
      } catch (error) {
        if (error !== 'cancel') {
          Message.error('批量删除失败')
        }
      }
    },

    // 重置筛选
    resetFilter() {
      this.filterForm.type = ''
      this.filterForm.status = ''
      this.filterForm.keyword = ''
      this.pagination.current = 1
      this.loadStrategies()
    },

    // 辅助方法
    getTypeLabel(type) {
      return strategyApi.getTypeLabel(type)
    },

    getCategoryLabel(category) {
      return strategyApi.getCategoryLabel(category)
    },

    getStatusLabel(status) {
      return strategyApi.getStatusInfo(status).label
    },

    getTypeTagType(type) {
      const typeMap = {
        'trend_following': 'primary',
        'mean_reversion': 'success',
        'momentum': 'warning',
        'arbitrage': 'info',
        'grid': 'danger',
        'custom': ''
      }
      return typeMap[type] || ''
    },

    getStatusTagType(status) {
      const statusMap = {
        'created': 'info',
        'validated': 'success',
        'active': 'success',
        'inactive': 'warning',
        'error': 'danger',
        'deleted': 'info'
      }
      return statusMap[status] || 'info'
    },

    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    },

    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.debounceTimer)
      this.debounceTimer = setTimeout(() => {
        this.pagination.current = 1
        this.loadStrategies()
      }, 500)
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.current = 1
      this.loadStrategies()
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.pagination.current = page
      this.loadStrategies()
    },

    // 全选/取消全选
    selectAll(event) {
      if (event.target.checked) {
        this.selectedStrategies = [...this.strategies]
      } else {
        this.selectedStrategies = []
      }
    }
  }
}
</script>

<style scoped>
/* 量子科技美学样式 */
.strategy-management {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
  position: relative;
  overflow-x: hidden;
  padding: 20px;
}

/* 量子背景效果 */
.quantum-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.quantum-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #00ffff, transparent),
    radial-gradient(2px 2px at 40px 70px, #ff00ff, transparent),
    radial-gradient(1px 1px at 90px 40px, #ffff00, transparent),
    radial-gradient(1px 1px at 130px 80px, #00ff00, transparent);
  background-repeat: repeat;
  background-size: 200px 200px;
  animation: quantumFloat 20s linear infinite;
  opacity: 0.3;
}

.quantum-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridPulse 4s ease-in-out infinite;
}

@keyframes quantumFloat {
  0% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(10px) rotate(240deg); }
  100% { transform: translateY(0px) rotate(360deg); }
}

@keyframes gridPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* 页面头部 */
.page-header {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px 0;
}

.quantum-title {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease-in-out infinite;
}

.title-glow {
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.title-subtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 8px;
  font-weight: 400;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 量子按钮 */
.quantum-button {
  position: relative;
  padding: 12px 24px;
  background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
  border: 2px solid transparent;
  border-radius: 8px;
  color: #ffffff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantum-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ff00);
  background-size: 400% 400%;
  border-radius: 8px;
  z-index: -1;
  animation: borderGlow 3s ease-in-out infinite;
}

.quantum-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 255, 255, 0.3);
}

.quantum-button.primary {
  background: linear-gradient(45deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 255, 0.2));
}

.quantum-button.secondary {
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(128, 128, 128, 0.1));
}

.quantum-button.success {
  background: linear-gradient(45deg, rgba(0, 255, 0, 0.2), rgba(0, 255, 255, 0.2));
}

.quantum-button.warning {
  background: linear-gradient(45deg, rgba(255, 255, 0, 0.2), rgba(255, 128, 0, 0.2));
}

.quantum-button.danger {
  background: linear-gradient(45deg, rgba(255, 0, 0, 0.2), rgba(255, 0, 255, 0.2));
}

@keyframes borderGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 统计卡片网格 */
.stats-grid {
  position: relative;
  z-index: 10;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  position: relative;
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  padding: 24px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 255, 255, 0.2);
}

.stat-card.active {
  border-color: rgba(0, 255, 0, 0.5);
  background: linear-gradient(135deg, rgba(0, 255, 0, 0.1), rgba(0, 255, 255, 0.1));
}

.stat-card.inactive {
  border-color: rgba(255, 255, 0, 0.5);
  background: linear-gradient(135deg, rgba(255, 255, 0, 0.1), rgba(255, 128, 0, 0.1));
}

.stat-card.new {
  border-color: rgba(255, 0, 255, 0.5);
  background: linear-gradient(135deg, rgba(255, 0, 255, 0.1), rgba(128, 0, 255, 0.1));
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 2;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(45deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 255, 0.2));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #00ffff;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

.stat-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(0, 255, 255, 0.1), transparent);
  border-radius: 12px;
  z-index: 1;
}

/* 量子卡片 */
.quantum-card {
  position: relative;
  z-index: 10;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(26, 26, 46, 0.8));
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  overflow: hidden;
  margin-bottom: 20px;
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
}

.total-count {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* 筛选区域 */
.filter-section {
  position: relative;
  z-index: 10;
}

.filter-content {
  padding: 24px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.quantum-select,
.quantum-input {
  padding: 10px 12px;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 6px;
  color: #ffffff;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.quantum-select:focus,
.quantum-input:focus {
  outline: none;
  border-color: #00ffff;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.search-input-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.5);
}

.filter-actions {
  display: flex;
  align-items: end;
}

/* 表格区域 */
.table-section {
  position: relative;
  z-index: 10;
}

.quantum-table-wrapper {
  padding: 0 24px 24px;
  overflow-x: auto;
}

.quantum-table {
  width: 100%;
  border-collapse: collapse;
  background: transparent;
}

.quantum-table th {
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #00ffff;
  border-bottom: 2px solid rgba(0, 255, 255, 0.3);
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
}

.quantum-table td {
  padding: 16px 12px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  color: #ffffff;
  transition: all 0.3s ease;
}

.table-row:hover td {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.05), rgba(255, 0, 255, 0.05));
}

/* 复选框样式 */
.quantum-checkbox {
  width: 18px;
  height: 18px;
  background: rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(0, 255, 255, 0.5);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quantum-checkbox:checked {
  background: linear-gradient(45deg, #00ffff, #ff00ff);
  border-color: #00ffff;
}

/* 策略名称 */
.strategy-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.strategy-name .name {
  font-weight: 600;
  color: #ffffff;
}

.template-badge {
  padding: 2px 8px;
  background: linear-gradient(45deg, rgba(255, 0, 255, 0.3), rgba(128, 0, 255, 0.3));
  border: 1px solid rgba(255, 0, 255, 0.5);
  border-radius: 12px;
  font-size: 0.7rem;
  color: #ff00ff;
}

/* 标签样式 */
.type-tag,
.code-tag,
.status-tag {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid;
}

.type-tag.primary {
  background: linear-gradient(45deg, rgba(0, 255, 255, 0.2), rgba(0, 128, 255, 0.2));
  border-color: rgba(0, 255, 255, 0.5);
  color: #00ffff;
}

.type-tag.success {
  background: linear-gradient(45deg, rgba(0, 255, 0, 0.2), rgba(0, 255, 128, 0.2));
  border-color: rgba(0, 255, 0, 0.5);
  color: #00ff00;
}

.type-tag.warning {
  background: linear-gradient(45deg, rgba(255, 255, 0, 0.2), rgba(255, 128, 0, 0.2));
  border-color: rgba(255, 255, 0, 0.5);
  color: #ffff00;
}

.type-tag.info {
  background: linear-gradient(45deg, rgba(128, 128, 255, 0.2), rgba(255, 128, 255, 0.2));
  border-color: rgba(128, 128, 255, 0.5);
  color: #8080ff;
}

.type-tag.danger {
  background: linear-gradient(45deg, rgba(255, 0, 0, 0.2), rgba(255, 0, 128, 0.2));
  border-color: rgba(255, 0, 0, 0.5);
  color: #ff0000;
}

.code-tag.python {
  background: linear-gradient(45deg, rgba(0, 255, 0, 0.2), rgba(0, 255, 255, 0.2));
  border-color: rgba(0, 255, 0, 0.5);
  color: #00ff00;
}

.code-tag.pine {
  background: linear-gradient(45deg, rgba(255, 255, 0, 0.2), rgba(255, 128, 0, 0.2));
  border-color: rgba(255, 255, 0, 0.5);
  color: #ffff00;
}

.status-tag.info {
  background: linear-gradient(45deg, rgba(128, 128, 255, 0.2), rgba(255, 128, 255, 0.2));
  border-color: rgba(128, 128, 255, 0.5);
  color: #8080ff;
}

.status-tag.success {
  background: linear-gradient(45deg, rgba(0, 255, 0, 0.2), rgba(0, 255, 128, 0.2));
  border-color: rgba(0, 255, 0, 0.5);
  color: #00ff00;
}

.status-tag.warning {
  background: linear-gradient(45deg, rgba(255, 255, 0, 0.2), rgba(255, 128, 0, 0.2));
  border-color: rgba(255, 255, 0, 0.5);
  color: #ffff00;
}

.status-tag.danger {
  background: linear-gradient(45deg, rgba(255, 0, 0, 0.2), rgba(255, 0, 128, 0.2));
  border-color: rgba(255, 0, 0, 0.5);
  color: #ff0000;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 6px;
  background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
}

.action-btn.view:hover {
  border-color: rgba(0, 255, 255, 0.8);
  background: linear-gradient(45deg, rgba(0, 255, 255, 0.3), rgba(0, 128, 255, 0.3));
}

.action-btn.edit:hover {
  border-color: rgba(255, 255, 0, 0.8);
  background: linear-gradient(45deg, rgba(255, 255, 0, 0.3), rgba(255, 128, 0, 0.3));
}

.action-btn.copy:hover {
  border-color: rgba(0, 255, 0, 0.8);
  background: linear-gradient(45deg, rgba(0, 255, 0, 0.3), rgba(0, 255, 128, 0.3));
}

.action-btn.delete:hover {
  border-color: rgba(255, 0, 0, 0.8);
  background: linear-gradient(45deg, rgba(255, 0, 0, 0.3), rgba(255, 0, 128, 0.3));
}

/* 分页 */
.pagination-wrapper {
  padding: 20px 24px;
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.pagination-info {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-size-select {
  padding: 6px 10px;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 4px;
  color: #ffffff;
  font-size: 0.8rem;
}

.page-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-btn {
  width: 32px;
  height: 32px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 4px;
  background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-btn:hover:not(:disabled) {
  border-color: #00ffff;
  background: linear-gradient(45deg, rgba(0, 255, 255, 0.3), rgba(255, 0, 255, 0.3));
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  min-width: 60px;
  text-align: center;
}

/* 批量操作 */
.batch-actions {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateX(-50%) translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

.batch-card {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(26, 26, 46, 0.9));
  border: 2px solid rgba(0, 255, 255, 0.5);
  box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.batch-content {
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-weight: 500;
}

.batch-info i {
  color: #00ffff;
}

.batch-buttons {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .strategy-management {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .quantum-title {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .filter-content {
    grid-template-columns: 1fr;
  }

  .quantum-table-wrapper {
    overflow-x: scroll;
  }

  .pagination-wrapper {
    flex-direction: column;
    gap: 12px;
  }

  .batch-content {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
