#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask, jsonify, request

app = Flask(__name__)

@app.route('/api/v1/notifications/recent', methods=['GET', 'OPTIONS'])
def get_recent_notifications():
    """获取最近的通知"""
    print("获取最近的通知请求")
    
    # 处理OPTIONS请求
    if request.method == 'OPTIONS':
        response = app.make_default_options_response()
        return response
    
    # 返回最近的通知
    return jsonify([])

@app.route('/api/v1/alert-rules', methods=['GET', 'OPTIONS'])
def get_alert_rules():
    """获取告警规则列表"""
    print("获取告警规则列表请求")
    
    # 处理OPTIONS请求
    if request.method == 'OPTIONS':
        response = app.make_default_options_response()
        return response
    
    # 返回告警规则列表
    rules = [
        {
            "id": "rule-001",
            "name": "BTC价格异常波动监控",
            "type": "price",
            "description": "监控BTC价格短时间内的异常波动",
            "level": "warning",
            "notify_channels": ["app", "email", "sound"],
            "conditions": [
                {
                    "field": "price.change.hourly",
                    "operator": ">",
                    "value": "3",
                    "logic": "OR"
                },
                {
                    "field": "price.change.hourly",
                    "operator": "<",
                    "value": "-3",
                    "logic": "AND"
                }
            ],
            "enabled": True,
            "created_at": "2023-05-20T08:30:00Z",
            "updated_at": "2023-05-25T14:20:00Z"
        }
    ]
    
    return jsonify(rules)

if __name__ == '__main__':
    app.run(debug=True, port=8001)
