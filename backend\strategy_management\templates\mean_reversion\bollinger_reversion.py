"""
布林带回归策略模板
适用于均值回归交易

策略逻辑：
- 计算价格在布林带中的相对位置
- 当价格接近下轨时买入，期待价格回归到均值
- 当价格接近上轨时卖出，期待价格回归到均值
- 当价格回归到中轨附近时平仓获利
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List

def initialize(context):
    """策略初始化"""
    context.period = {{ period | default(20) }}
    context.std_dev = {{ std_dev | default(2.0) }}
    context.reversion_threshold = {{ reversion_threshold | default(0.8) }}
    context.price_type = "{{ price_type | default('close') }}"
    
    # 内部状态
    context.position = 0  # 当前仓位
    context.entry_price = 0  # 入场价格
    context.signals = []  # 信号历史

def calculate_bollinger_bands(prices, period=20, std_dev=2.0):
    """计算布林带指标"""
    sma = prices.rolling(period).mean()
    std = prices.rolling(period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return upper_band, sma, lower_band

def calculate_bb_position(price, upper, lower, middle):
    """计算价格在布林带中的相对位置 (0-1之间)"""
    if upper == lower:
        return 0.5
    return (price - lower) / (upper - lower)

def handle_bar(context, data):
    """处理每个K线数据"""
    # 获取价格数据
    price = data[context.price_type]
    
    # 检查数据长度是否足够
    if len(price) < context.period:
        return {'action': 'HOLD', 'reason': '数据不足'}
    
    # 计算布林带
    upper_band, middle_band, lower_band = calculate_bollinger_bands(
        price, context.period, context.std_dev
    )
    
    # 获取当前值
    current_price = price.iloc[-1]
    current_upper = upper_band.iloc[-1]
    current_lower = lower_band.iloc[-1]
    current_middle = middle_band.iloc[-1]
    
    # 检查布林带数据有效性
    if pd.isna(current_upper) or pd.isna(current_lower) or pd.isna(current_middle):
        return {'action': 'HOLD', 'reason': '布林带数据无效'}
    
    # 计算价格在布林带中的位置
    bb_position = calculate_bb_position(current_price, current_upper, current_lower, current_middle)
    
    # 价格接近下轨时买入 (均值回归)
    if bb_position <= (1 - context.reversion_threshold) and context.position <= 0:
        context.position = 1
        context.entry_price = current_price
        signal = {
            'action': 'BUY',
            'price': current_price,
            'reason': f'布林带回归买入 (位置:{bb_position:.2f} 价格:{current_price:.2f} 下轨:{current_lower:.2f})',
            'bb_position': bb_position,
            'upper_band': current_upper,
            'lower_band': current_lower,
            'middle_band': current_middle,
            'timestamp': data.index[-1] if hasattr(data, 'index') else None
        }
        context.signals.append(signal)
        return signal
    
    # 价格接近上轨时卖出 (均值回归)
    elif bb_position >= context.reversion_threshold and context.position >= 0:
        context.position = -1
        signal = {
            'action': 'SELL',
            'price': current_price,
            'reason': f'布林带回归卖出 (位置:{bb_position:.2f} 价格:{current_price:.2f} 上轨:{current_upper:.2f})',
            'bb_position': bb_position,
            'upper_band': current_upper,
            'lower_band': current_lower,
            'middle_band': current_middle,
            'timestamp': data.index[-1] if hasattr(data, 'index') else None
        }
        if context.position > 0 and context.entry_price > 0:
            signal['profit_loss'] = current_price - context.entry_price
            signal['profit_loss_pct'] = (current_price - context.entry_price) / context.entry_price * 100
        context.signals.append(signal)
        return signal
    
    # 价格回归到中轨附近时平仓
    elif 0.4 <= bb_position <= 0.6 and context.position != 0:
        profit_loss = 0
        profit_loss_pct = 0
        if context.entry_price > 0:
            if context.position > 0:
                profit_loss = current_price - context.entry_price
            else:
                profit_loss = context.entry_price - current_price
            profit_loss_pct = profit_loss / context.entry_price * 100
        
        context.position = 0
        signal = {
            'action': 'CLOSE',
            'price': current_price,
            'reason': f'布林带中轨平仓 (位置:{bb_position:.2f} 价格:{current_price:.2f} 中轨:{current_middle:.2f})',
            'bb_position': bb_position,
            'upper_band': current_upper,
            'lower_band': current_lower,
            'middle_band': current_middle,
            'profit_loss': profit_loss,
            'profit_loss_pct': profit_loss_pct,
            'timestamp': data.index[-1] if hasattr(data, 'index') else None
        }
        context.entry_price = 0
        context.signals.append(signal)
        return signal
    
    return {'action': 'HOLD'}

def get_indicators(context, data):
    """获取技术指标数据（用于图表显示）"""
    price = data[context.price_type]
    
    if len(price) < context.period:
        return {}
    
    upper_band, middle_band, lower_band = calculate_bollinger_bands(
        price, context.period, context.std_dev
    )
    
    # 计算布林带位置
    bb_positions = []
    for i in range(len(price)):
        if i >= context.period - 1:
            pos = calculate_bb_position(price.iloc[i], upper_band.iloc[i], 
                                      lower_band.iloc[i], middle_band.iloc[i])
            bb_positions.append(pos)
        else:
            bb_positions.append(np.nan)
    
    return {
        'upper_band': upper_band,
        'middle_band': middle_band,
        'lower_band': lower_band,
        'bb_position': pd.Series(bb_positions, index=price.index),
        'price': price
    }

def get_performance_metrics(context):
    """获取策略绩效指标"""
    if not context.signals:
        return {}
    
    # 计算交易统计
    buy_signals = [s for s in context.signals if s['action'] == 'BUY']
    sell_signals = [s for s in context.signals if s['action'] in ['SELL', 'CLOSE'] and 'profit_loss' in s]
    
    total_trades = len(sell_signals)
    if total_trades == 0:
        return {'total_trades': 0}
    
    # 盈亏统计
    profits = [s['profit_loss'] for s in sell_signals if s['profit_loss'] > 0]
    losses = [s['profit_loss'] for s in sell_signals if s['profit_loss'] < 0]
    
    win_rate = len(profits) / total_trades * 100 if total_trades > 0 else 0
    avg_profit = np.mean(profits) if profits else 0
    avg_loss = np.mean(losses) if losses else 0
    profit_factor = abs(sum(profits) / sum(losses)) if losses and sum(losses) != 0 else float('inf')
    
    # 布林带特定指标
    avg_bb_position_entry = np.mean([s.get('bb_position', 0.5) for s in buy_signals])
    avg_bb_position_exit = np.mean([s.get('bb_position', 0.5) for s in sell_signals])
    
    return {
        'total_trades': total_trades,
        'win_rate': win_rate,
        'avg_profit': avg_profit,
        'avg_loss': avg_loss,
        'profit_factor': profit_factor,
        'total_profit_loss': sum(s['profit_loss'] for s in sell_signals),
        'avg_bb_position_entry': avg_bb_position_entry,
        'avg_bb_position_exit': avg_bb_position_exit
    }

# 策略元数据
STRATEGY_METADATA = {
    'name': '布林带回归策略',
    'category': 'mean_reversion',
    'description': '基于布林带的均值回归策略，价格偏离均值时进行反向交易',
    'parameters': [
        'period', 'std_dev', 'reversion_threshold', 'price_type'
    ],
    'indicators': ['upper_band', 'middle_band', 'lower_band', 'bb_position'],
    'timeframes': ['15m', '1h', '4h', '1d'],
    'markets': ['crypto', 'forex', 'stocks']
}
