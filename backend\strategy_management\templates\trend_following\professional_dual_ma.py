"""
专业双均线交叉策略 - 包含完整风险管理系统

这是一个专业级的趋势跟踪策略，集成了完整的风险管理机制：
- 动态止损止盈系统
- 移动止损功能
- 多种仓位管理模式
- 风险控制机制
- 交易时间管理

策略参数:
- short_period: 短期均线周期 (默认: 5)
- long_period: 长期均线周期 (默认: 20)

风险管理参数:
- enable_stop_loss: 启用止损 (默认: True)
- stop_loss_pct: 止损百分比 (默认: 2.0)
- enable_take_profit: 启用止盈 (默认: True)
- take_profit_pct: 止盈百分比 (默认: 5.0)
- enable_trailing_stop: 启用移动止损 (默认: False)
- trailing_stop_pct: 移动止损距离 (默认: 1.5)
- position_sizing_mode: 仓位管理模式 (默认: 'fixed')
- max_position_pct: 最大仓位百分比 (默认: 20.0)
- risk_per_trade_pct: 单笔风险百分比 (默认: 1.0)
- max_drawdown_pct: 最大回撤限制 (默认: 15.0)
- max_trades_per_day: 日交易次数限制 (默认: 10)
- max_consecutive_losses: 连续亏损限制 (默认: 5)
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List
from datetime import datetime, time

def initialize(context):
    """策略初始化"""
    # 基础策略参数
    context.short_period = {{ short_period | default(5) }}
    context.long_period = {{ long_period | default(20) }}
    context.price_type = "{{ price_type | default('close') }}"
    
    # 风险管理参数
    context.enable_stop_loss = {{ enable_stop_loss | default(True) }}
    context.stop_loss_pct = {{ stop_loss_pct | default(2.0) }}
    context.enable_take_profit = {{ enable_take_profit | default(True) }}
    context.take_profit_pct = {{ take_profit_pct | default(5.0) }}
    context.enable_trailing_stop = {{ enable_trailing_stop | default(False) }}
    context.trailing_stop_pct = {{ trailing_stop_pct | default(1.5) }}
    context.position_sizing_mode = "{{ position_sizing_mode | default('fixed') }}"
    context.max_position_pct = {{ max_position_pct | default(20.0) }}
    context.risk_per_trade_pct = {{ risk_per_trade_pct | default(1.0) }}
    context.max_drawdown_pct = {{ max_drawdown_pct | default(15.0) }}
    context.max_trades_per_day = {{ max_trades_per_day | default(10) }}
    context.max_consecutive_losses = {{ max_consecutive_losses | default(5) }}
    
    # 交易时间设置
    context.enable_schedule = {{ enable_schedule | default(False) }}
    context.trading_days = {{ trading_days | default(['monday', 'tuesday', 'wednesday', 'thursday', 'friday']) }}
    context.start_time = "{{ start_time | default('09:00') }}"
    context.end_time = "{{ end_time | default('17:00') }}"
    context.avoid_news_events = {{ avoid_news_events | default(True) }}
    
    # 策略状态
    context.position = 0  # 当前仓位
    context.entry_price = 0  # 入场价格
    context.stop_loss_price = 0  # 止损价格
    context.take_profit_price = 0  # 止盈价格
    context.trailing_stop_price = 0  # 移动止损价格
    context.signals = []  # 信号历史
    
    # 风险控制状态
    context.account_balance = 100000  # 初始资金
    context.peak_balance = context.account_balance
    context.current_drawdown = 0
    context.consecutive_losses = 0
    context.daily_trades = 0
    context.last_trade_date = None
    context.strategy_active = True

def handle_bar(context, data):
    """处理每个K线数据"""
    # 检查策略是否激活
    if not context.strategy_active:
        return {'action': 'HOLD', 'reason': '策略已暂停'}
    
    # 获取价格数据
    price = data[context.price_type]
    current_price = price.iloc[-1]
    current_time = data.index[-1] if hasattr(data, 'index') else datetime.now()
    
    # 检查交易时间
    if not is_trading_time(context, current_time):
        return {'action': 'HOLD', 'reason': '非交易时间'}
    
    # 检查风险限制
    if not check_risk_limits(context):
        return {'action': 'HOLD', 'reason': '触发风险限制'}
    
    # 检查数据长度是否足够
    if len(price) < context.long_period:
        return {'action': 'HOLD', 'reason': '数据不足'}
    
    # 更新移动止损
    if context.enable_trailing_stop and context.position != 0:
        update_trailing_stop(context, current_price)
    
    # 检查止损止盈
    exit_signal = check_exit_conditions(context, current_price, current_time)
    if exit_signal:
        return exit_signal
    
    # 计算均线
    short_ma = price.rolling(window=context.short_period).mean()
    long_ma = price.rolling(window=context.long_period).mean()
    
    # 获取当前和前一个值
    current_short = short_ma.iloc[-1]
    current_long = long_ma.iloc[-1]
    prev_short = short_ma.iloc[-2]
    prev_long = long_ma.iloc[-2]
    
    # 检查均线数据有效性
    if pd.isna(current_short) or pd.isna(current_long) or pd.isna(prev_short) or pd.isna(prev_long):
        return {'action': 'HOLD', 'reason': '均线数据无效'}
    
    # 金叉信号 - 买入
    if prev_short <= prev_long and current_short > current_long and context.position <= 0:
        quantity = calculate_position_size(context, current_price)
        
        # 设置止损止盈价格
        if context.enable_stop_loss:
            context.stop_loss_price = current_price * (1 - context.stop_loss_pct / 100)
        if context.enable_take_profit:
            context.take_profit_price = current_price * (1 + context.take_profit_pct / 100)
        if context.enable_trailing_stop:
            context.trailing_stop_price = current_price * (1 - context.trailing_stop_pct / 100)
        
        context.position = quantity
        context.entry_price = current_price
        context.daily_trades += 1
        
        signal = {
            'action': 'BUY',
            'price': current_price,
            'quantity': quantity,
            'reason': f'金叉买入信号 (短期MA: {current_short:.2f}, 长期MA: {current_long:.2f})',
            'stop_loss': context.stop_loss_price if context.enable_stop_loss else None,
            'take_profit': context.take_profit_price if context.enable_take_profit else None,
            'timestamp': current_time
        }
        context.signals.append(signal)
        return signal
    
    # 死叉信号 - 卖出
    if prev_short >= prev_long and current_short < current_long and context.position >= 0:
        quantity = abs(context.position) if context.position != 0 else calculate_position_size(context, current_price)
        
        profit_loss = 0
        if context.position > 0 and context.entry_price > 0:
            profit_loss = (current_price - context.entry_price) * context.position
            profit_loss_pct = (current_price - context.entry_price) / context.entry_price * 100
            
            # 更新连续亏损计数
            if profit_loss < 0:
                context.consecutive_losses += 1
            else:
                context.consecutive_losses = 0
        
        context.position = 0
        context.entry_price = 0
        context.stop_loss_price = 0
        context.take_profit_price = 0
        context.trailing_stop_price = 0
        context.daily_trades += 1
        
        signal = {
            'action': 'SELL',
            'price': current_price,
            'quantity': quantity,
            'reason': f'死叉卖出信号 (短期MA: {current_short:.2f}, 长期MA: {current_long:.2f})',
            'profit_loss': profit_loss,
            'profit_loss_pct': profit_loss_pct if 'profit_loss_pct' in locals() else 0,
            'timestamp': current_time
        }
        context.signals.append(signal)
        return signal
    
    return {'action': 'HOLD'}

def is_trading_time(context, timestamp):
    """检查是否在交易时间内"""
    if not context.enable_schedule:
        return True
    
    # 检查交易日
    weekday = timestamp.strftime('%A').lower()
    if weekday not in context.trading_days:
        return False
    
    # 检查交易时间
    current_time = timestamp.time()
    start_time = time.fromisoformat(context.start_time)
    end_time = time.fromisoformat(context.end_time)
    
    return start_time <= current_time <= end_time

def check_risk_limits(context):
    """检查风险限制"""
    # 检查最大回撤
    if context.current_drawdown >= context.max_drawdown_pct:
        context.strategy_active = False
        return False
    
    # 检查连续亏损
    if context.consecutive_losses >= context.max_consecutive_losses:
        context.strategy_active = False
        return False
    
    # 检查日交易次数
    if context.daily_trades >= context.max_trades_per_day:
        return False
    
    return True

def calculate_position_size(context, price):
    """计算仓位大小"""
    if context.position_sizing_mode == 'fixed':
        # 固定仓位模式
        max_position_value = context.account_balance * (context.max_position_pct / 100)
        return max_position_value / price
    
    elif context.position_sizing_mode == 'risk_parity':
        # 风险平价模式
        risk_amount = context.account_balance * (context.risk_per_trade_pct / 100)
        stop_distance = context.stop_loss_pct / 100 * price
        position_size = risk_amount / stop_distance
        
        # 限制最大仓位
        max_size = context.account_balance * (context.max_position_pct / 100) / price
        return min(position_size, max_size)
    
    else:  # 其他模式回退到固定模式
        max_position_value = context.account_balance * (context.max_position_pct / 100)
        return max_position_value / price

def update_trailing_stop(context, current_price):
    """更新移动止损"""
    if context.position > 0:  # 多头仓位
        new_trailing_stop = current_price * (1 - context.trailing_stop_pct / 100)
        if new_trailing_stop > context.trailing_stop_price:
            context.trailing_stop_price = new_trailing_stop

def check_exit_conditions(context, current_price, current_time):
    """检查退出条件"""
    if context.position == 0:
        return None
    
    # 检查止损
    if context.enable_stop_loss and context.stop_loss_price > 0:
        if (context.position > 0 and current_price <= context.stop_loss_price) or \
           (context.position < 0 and current_price >= context.stop_loss_price):
            return create_exit_signal(context, current_price, current_time, '止损触发')
    
    # 检查止盈
    if context.enable_take_profit and context.take_profit_price > 0:
        if (context.position > 0 and current_price >= context.take_profit_price) or \
           (context.position < 0 and current_price <= context.take_profit_price):
            return create_exit_signal(context, current_price, current_time, '止盈触发')
    
    # 检查移动止损
    if context.enable_trailing_stop and context.trailing_stop_price > 0:
        if context.position > 0 and current_price <= context.trailing_stop_price:
            return create_exit_signal(context, current_price, current_time, '移动止损触发')
    
    return None

def create_exit_signal(context, current_price, current_time, reason):
    """创建退出信号"""
    profit_loss = 0
    profit_loss_pct = 0
    
    if context.entry_price > 0:
        profit_loss = (current_price - context.entry_price) * context.position
        profit_loss_pct = (current_price - context.entry_price) / context.entry_price * 100
        
        # 更新连续亏损计数
        if profit_loss < 0:
            context.consecutive_losses += 1
        else:
            context.consecutive_losses = 0
    
    quantity = abs(context.position)
    context.position = 0
    context.entry_price = 0
    context.stop_loss_price = 0
    context.take_profit_price = 0
    context.trailing_stop_price = 0
    context.daily_trades += 1
    
    signal = {
        'action': 'SELL',
        'price': current_price,
        'quantity': quantity,
        'reason': reason,
        'profit_loss': profit_loss,
        'profit_loss_pct': profit_loss_pct,
        'timestamp': current_time
    }
    context.signals.append(signal)
    return signal

# 策略元数据
STRATEGY_METADATA = {
    'name': '专业双均线交叉策略',
    'category': 'trend_following',
    'description': '包含完整风险管理系统的专业级双均线交叉策略',
    'parameters': [
        'short_period', 'long_period', 'price_type',
        'enable_stop_loss', 'stop_loss_pct', 'enable_take_profit', 'take_profit_pct',
        'enable_trailing_stop', 'trailing_stop_pct', 'position_sizing_mode',
        'max_position_pct', 'risk_per_trade_pct', 'max_drawdown_pct',
        'max_trades_per_day', 'max_consecutive_losses'
    ],
    'indicators': ['short_ma', 'long_ma', 'atr'],
    'timeframes': ['1h', '4h', '1d'],
    'markets': ['crypto', 'forex', 'stocks'],
    'risk_management': True,
    'professional': True
}
