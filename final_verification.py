#!/usr/bin/env python3
"""
通知管理功能最终验证脚本
验证前后端完整集成和数据持久化
"""

import requests
import time
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API基础URL
API_BASE_URL = "http://localhost:8000"

# 测试用户凭据
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

class FinalVerification:
    def __init__(self):
        self.token = None
        self.created_rules = []
        
    def authenticate(self):
        """用户认证"""
        logger.info("🔐 开始用户认证...")
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/api/v1/auth/token",
                data=TEST_USER,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                logger.info("✅ 用户认证成功")
                return True
            else:
                logger.error(f"❌ 用户认证失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 用户认证异常: {str(e)}")
            return False
    
    def get_headers(self):
        """获取请求头"""
        return {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
    
    def create_test_rules(self):
        """创建多个测试规则"""
        logger.info("📝 创建测试规则...")
        
        test_rules = [
            {
                "name": "BTC价格上涨告警",
                "type": "price_change",
                "level": "info",
                "description": "BTC价格上涨5%时触发",
                "notify_channels": ["app", "email"],
                "conditions": {"direction": "up", "percentage": 5, "time_frame": 30},
                "enabled": True
            },
            {
                "name": "ETH价格下跌告警",
                "type": "price_change", 
                "level": "warning",
                "description": "ETH价格下跌10%时触发",
                "notify_channels": ["app"],
                "conditions": {"direction": "down", "percentage": 10, "time_frame": 60},
                "enabled": True
            },
            {
                "name": "交易量异常告警",
                "type": "volume_change",
                "level": "error",
                "description": "交易量异常增长时触发",
                "notify_channels": ["app", "email", "sms"],
                "conditions": {"threshold": 200, "time_frame": 15},
                "enabled": False
            }
        ]
        
        success_count = 0
        for i, rule in enumerate(test_rules, 1):
            try:
                response = requests.post(
                    f"{API_BASE_URL}/api/v1/notifications/alert-rules",
                    headers=self.get_headers(),
                    json=rule,
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        rule_id = data.get("rule_id")
                        self.created_rules.append(rule_id)
                        logger.info(f"✅ 规则 {i} 创建成功: {rule['name']} (ID: {rule_id})")
                        success_count += 1
                    else:
                        logger.error(f"❌ 规则 {i} 创建失败: {data}")
                else:
                    logger.error(f"❌ 规则 {i} 创建失败: HTTP {response.status_code}")
                    
            except Exception as e:
                logger.error(f"❌ 规则 {i} 创建异常: {str(e)}")
        
        logger.info(f"📊 创建结果: {success_count}/{len(test_rules)} 个规则创建成功")
        return success_count == len(test_rules)
    
    def verify_rules_list(self):
        """验证规则列表"""
        logger.info("📋 验证规则列表...")
        
        try:
            response = requests.get(
                f"{API_BASE_URL}/api/v1/notifications/alert-rules",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                rules = data.get("items", [])
                total = data.get("total", 0)
                
                logger.info(f"✅ 获取到 {len(rules)} 条规则，总数: {total}")
                
                # 验证创建的规则是否都在列表中
                found_rules = 0
                for rule_id in self.created_rules:
                    if any(rule.get("id") == rule_id for rule in rules):
                        found_rules += 1
                
                logger.info(f"📊 验证结果: {found_rules}/{len(self.created_rules)} 个创建的规则在列表中")
                return found_rules == len(self.created_rules)
            else:
                logger.error(f"❌ 获取规则列表失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 获取规则列表异常: {str(e)}")
            return False
    
    def test_rule_operations(self):
        """测试规则操作"""
        if not self.created_rules:
            logger.warning("⚠️  没有创建的规则，跳过操作测试")
            return True
            
        logger.info("🔧 测试规则操作...")
        
        # 测试更新第一个规则
        rule_id = self.created_rules[0]
        updated_rule = {
            "id": rule_id,
            "name": "BTC价格上涨告警-已更新",
            "type": "price_change",
            "level": "warning",
            "description": "更新后的BTC价格告警规则",
            "notify_channels": ["app"],
            "conditions": {"direction": "up", "percentage": 3, "time_frame": 15},
            "enabled": True
        }
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/api/v1/notifications/alert-rules/update",
                headers=self.get_headers(),
                json=updated_rule,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    logger.info(f"✅ 规则更新成功: {rule_id}")
                else:
                    logger.error(f"❌ 规则更新失败: {data}")
                    return False
            else:
                logger.error(f"❌ 规则更新失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 规则更新异常: {str(e)}")
            return False
        
        return True
    
    def test_persistence(self):
        """测试数据持久化"""
        logger.info("💾 测试数据持久化...")
        
        # 等待一段时间
        time.sleep(2)
        
        # 重新获取规则列表
        return self.verify_rules_list()
    
    def cleanup(self):
        """清理测试数据"""
        logger.info("🧹 清理测试数据...")
        
        success_count = 0
        for rule_id in self.created_rules:
            try:
                response = requests.post(
                    f"{API_BASE_URL}/api/v1/notifications/alert-rules/delete",
                    headers=self.get_headers(),
                    json={"id": rule_id},
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        logger.info(f"✅ 规则删除成功: {rule_id}")
                        success_count += 1
                    else:
                        logger.error(f"❌ 规则删除失败: {data}")
                else:
                    logger.error(f"❌ 规则删除失败: HTTP {response.status_code}")
                    
            except Exception as e:
                logger.error(f"❌ 规则删除异常: {str(e)}")
        
        logger.info(f"📊 清理结果: {success_count}/{len(self.created_rules)} 个规则删除成功")
        return success_count == len(self.created_rules)
    
    def run_verification(self):
        """运行完整验证"""
        logger.info("🚀 开始通知管理功能最终验证...")
        logger.info("=" * 60)
        
        # 1. 用户认证
        if not self.authenticate():
            logger.error("❌ 用户认证失败，终止验证")
            return False
        
        # 2. 创建测试规则
        if not self.create_test_rules():
            logger.error("❌ 创建测试规则失败")
            return False
        
        # 3. 验证规则列表
        if not self.verify_rules_list():
            logger.error("❌ 验证规则列表失败")
            return False
        
        # 4. 测试规则操作
        if not self.test_rule_operations():
            logger.error("❌ 测试规则操作失败")
            return False
        
        # 5. 测试数据持久化
        if not self.test_persistence():
            logger.error("❌ 测试数据持久化失败")
            return False
        
        # 6. 清理测试数据
        if not self.cleanup():
            logger.warning("⚠️  清理测试数据部分失败")
        
        logger.info("=" * 60)
        logger.info("🎉 通知管理功能最终验证完成！")
        logger.info("✅ 所有核心功能正常工作")
        logger.info("✅ 数据持久化正常")
        logger.info("✅ CRUD操作正常")
        logger.info("✅ 前后端集成正常")
        
        return True

def main():
    """主函数"""
    verification = FinalVerification()
    
    try:
        success = verification.run_verification()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("验证被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"验证过程中发生异常: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
