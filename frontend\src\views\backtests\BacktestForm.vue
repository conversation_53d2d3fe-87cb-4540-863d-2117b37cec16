<template>
  <div class="backtest-form-container">
    <div class="page-header">
      <h2>创建回测任务</h2>
      <el-button @click="backToList">
        <i class="el-icon-back"></i> 返回列表
      </el-button>
    </div>

    <el-form ref="form" :model="backtestForm" :rules="rules" label-width="120px" v-loading="loading">
      <el-card class="basic-info-card">
        <div slot="header" class="card-header">
          <span>基本配置</span>
        </div>

        <!-- 策略选择 -->
        <el-form-item label="选择策略" prop="strategy_id">
          <el-select v-model="backtestForm.strategy_id" placeholder="请选择要回测的策略" style="width: 100%">
            <el-option
              v-for="strategy in strategies"
              :key="strategy.id"
              :label="strategy.name"
              :value="strategy.id">
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 回测名称 -->
        <el-form-item label="回测名称" prop="name">
          <el-input v-model="backtestForm.name" placeholder="请输入回测名称"></el-input>
        </el-form-item>

        <!-- 回测描述 -->
        <el-form-item label="回测描述" prop="description">
          <el-input
            type="textarea"
            v-model="backtestForm.description"
            placeholder="请输入对此次回测的描述（可选）"
            :rows="3">
          </el-input>
        </el-form-item>
      </el-card>

      <el-card class="time-range-card mt-20">
        <div slot="header" class="card-header">
          <span>时间范围</span>
        </div>

        <!-- 开始日期和结束日期 -->
        <el-form-item label="回测周期" prop="date_range" required>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
      </el-card>

      <el-card class="capital-config-card mt-20">
        <div slot="header" class="card-header">
          <span>资金配置</span>
        </div>

        <!-- 初始资金 -->
        <el-form-item label="初始资金" prop="initial_capital">
          <el-input-number
            v-model="backtestForm.initial_capital"
            :min="1"
            :step="1000"
            :precision="2"
            :controls-position="'right'"
            style="width: 100%">
          </el-input-number>
        </el-form-item>

        <!-- 手续费率 -->
        <el-form-item label="手续费率(%)" prop="commission_rate">
          <el-input-number
            v-model="backtestForm.commission_rate"
            :min="0"
            :max="100"
            :step="0.05"
            :precision="2"
            :controls-position="'right'"
            style="width: 100%">
          </el-input-number>
        </el-form-item>

        <!-- 滑点设置 -->
        <el-form-item label="滑点设置(%)" prop="slippage">
          <el-input-number
            v-model="backtestForm.slippage"
            :min="0"
            :max="10"
            :step="0.01"
            :precision="2"
            :controls-position="'right'"
            style="width: 100%">
          </el-input-number>
        </el-form-item>
      </el-card>

      <el-card class="advanced-config-card mt-20">
        <div slot="header" class="card-header">
          <span>高级配置</span>
          <el-switch
            v-model="showAdvancedOptions"
            active-text="显示高级选项"
            inactive-text="隐藏高级选项">
          </el-switch>
        </div>

        <div v-if="showAdvancedOptions">
          <!-- 杠杆倍数 -->
          <el-form-item label="杠杆倍数" prop="leverage">
            <el-input-number
              v-model="backtestForm.leverage"
              :min="1"
              :max="100"
              :step="1"
              :controls-position="'right'"
              style="width: 100%">
            </el-input-number>
          </el-form-item>

          <!-- 最小交易量 -->
          <el-form-item label="最小交易量" prop="min_trade_amount">
            <el-input-number
              v-model="backtestForm.min_trade_amount"
              :min="0"
              :step="0.001"
              :precision="3"
              :controls-position="'right'"
              style="width: 100%">
            </el-input-number>
          </el-form-item>

          <!-- 数据频率 -->
          <el-form-item label="数据频率" prop="data_frequency">
            <el-select v-model="backtestForm.data_frequency" placeholder="请选择数据频率" style="width: 100%">
              <el-option label="1分钟" value="1m"></el-option>
              <el-option label="5分钟" value="5m"></el-option>
              <el-option label="15分钟" value="15m"></el-option>
              <el-option label="30分钟" value="30m"></el-option>
              <el-option label="1小时" value="1h"></el-option>
              <el-option label="4小时" value="4h"></el-option>
              <el-option label="1天" value="1d"></el-option>
              <el-option label="1周" value="1w"></el-option>
            </el-select>
          </el-form-item>

          <!-- 回测模式 -->
          <el-form-item label="回测模式" prop="mode">
            <el-radio-group v-model="backtestForm.mode">
              <el-radio label="standard">标准模式</el-radio>
              <el-radio label="fast">快速模式</el-radio>
              <el-radio label="detailed">详细模式</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="form-actions mt-20">
        <el-button type="primary" @click="submitBacktest" :loading="submitting">
          提交回测
        </el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { strategyAPI, backtestAPI } from '@/api/index';

export default {
  name: 'BacktestForm',
  data() {
    return {
      loading: false,
      submitting: false,
      strategies: [],
      dateRange: [],
      showAdvancedOptions: false,
      backtestForm: {
        name: '',
        description: '',
        strategy_id: '',
        initial_capital: 10000,
        commission_rate: 0.1,
        slippage: 0.05,
        leverage: 1,
        min_trade_amount: 0.001,
        data_frequency: '1d',
        mode: 'standard',
        start_date: '',
        end_date: '',
        date_range: []
      },
      rules: {
        name: [
          { required: true, message: '请输入回测名称', trigger: 'blur' },
          { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
        ],
        strategy_id: [
          { required: true, message: '请选择策略', trigger: 'change' }
        ],
        date_range: [
          { required: true, message: '请选择回测时间范围', trigger: 'change' }
        ],
        initial_capital: [
          { required: true, message: '请输入初始资金', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    dateRange(val) {
      if (val && val.length === 2) {
        this.backtestForm.start_date = val[0];
        this.backtestForm.end_date = val[1];
        this.backtestForm.date_range = val;
      } else {
        this.backtestForm.start_date = '';
        this.backtestForm.end_date = '';
        this.backtestForm.date_range = [];
      }
    }
  },
  created() {
    this.fetchStrategies();
  },
  methods: {
    async fetchStrategies() {
      this.loading = true;
      try {
        // 从策略管理中心获取真实策略列表
        const response = await this.$http.get('/api/v1/strategies');

        if (response.data && response.data.success) {
          // 处理策略数据，确保格式正确
          this.strategies = (response.data.data || []).map(strategy => ({
            id: strategy.id,
            name: strategy.name,
            description: strategy.description,
            type: strategy.type,
            category: strategy.category,
            status: strategy.status
          }));

          console.log('成功获取策略列表:', this.strategies.length, '个策略');
        } else {
          console.warn('策略API返回格式异常:', response.data);
          this.$message.warning('策略列表数据格式异常');
          this.strategies = [];
        }
      } catch (error) {
        console.error('获取策略列表失败:', error);
        this.$message.error('获取策略列表失败: ' + (error.message || '网络错误'));
        this.strategies = [];
      } finally {
        this.loading = false;
      }
    },
    submitBacktest() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          try {
            // 构建回测参数
            const backtestData = {
              name: this.backtestForm.name,
              description: this.backtestForm.description,
              strategy_id: parseInt(this.backtestForm.strategy_id),
              start_date: this.backtestForm.start_date,
              end_date: this.backtestForm.end_date,
              initial_capital: this.backtestForm.initial_capital,
              commission_rate: this.backtestForm.commission_rate,
              slippage: this.backtestForm.slippage,
              leverage: this.backtestForm.leverage,
              min_trade_amount: this.backtestForm.min_trade_amount,
              data_frequency: this.backtestForm.data_frequency,
              mode: this.backtestForm.mode
            };

            // 提交到API
            const response = await backtestAPI.createBacktest(backtestData);

            if (response.data && response.data.success) {
              this.$message.success('回测任务已提交，正在后台处理');
              this.$router.push('/backtests');
            } else {
              this.$message.warning('回测提交结果不符合预期，请检查回测状态');
              console.warn('回测API返回格式不符合预期:', response);
              this.$router.push('/backtests');
            }
          } catch (error) {
            console.error('提交回测失败:', error);
            this.$message.error('提交回测失败: ' + (error.message || '未知错误'));
          } finally {
            this.submitting = false;
          }
        } else {
          this.$message.warning('请完善表单信息');
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.form.resetFields();
      this.dateRange = [];
      this.backtestForm = {
        name: '',
        description: '',
        strategy_id: '',
        initial_capital: 10000,
        commission_rate: 0.1,
        slippage: 0.05,
        leverage: 1,
        min_trade_amount: 0.001,
        data_frequency: '1d',
        mode: 'standard',
        start_date: '',
        end_date: '',
        date_range: []
      };
    },
    backToList() {
      this.$router.push('/backtests');
    }
  }
};
</script>

<style scoped>
.backtest-form-container {
  padding: 20px;
  background-color: #081428;
  color: #FFFFFF;
  min-height: 100vh;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 30px;
}

h2 {
  color: #00F7FF;
  text-shadow: 0 0 10px rgba(0,247,255,0.7);
  margin-bottom: 20px;
}

.form-section {
  margin-bottom: 30px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.section-title {
  color: #00F7FF;
  font-size: 16px;
  text-shadow: 0 0 8px rgba(0,247,255,0.7);
  margin: 20px 0;
  border-bottom: 1px solid rgba(0, 247, 255, 0.3);
  padding-bottom: 10px;
}

/* 覆盖Element UI组件样式 */
::v-deep .el-card {
  background-color: rgba(0, 21, 40, 0.8);
  border: 1px solid #00F7FF;
  box-shadow: 0 0 15px rgba(0,247,255,0.3);
  margin-bottom: 20px;
}

::v-deep .el-card__header {
  background-color: rgba(0, 15, 40, 0.9);
  border-bottom: 1px solid #00F7FF;
  padding: 15px 20px;
}

::v-deep .el-form-item__label {
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.4);
}

::v-deep .el-input__inner {
  background-color: rgba(0, 21, 40, 0.5);
  border: 1px solid rgba(0, 247, 255, 0.3);
  color: #FFFFFF;
}

::v-deep .el-input__inner:focus,
::v-deep .el-input__inner:hover {
  border-color: #00F7FF;
  box-shadow: 0 0 8px rgba(0,247,255,0.5);
}

::v-deep .el-textarea__inner {
  background-color: rgba(0, 21, 40, 0.5);
  border: 1px solid rgba(0, 247, 255, 0.3);
  color: #FFFFFF;
}

::v-deep .el-textarea__inner:focus,
::v-deep .el-textarea__inner:hover {
  border-color: #00F7FF;
  box-shadow: 0 0 8px rgba(0,247,255,0.5);
}

::v-deep .el-select-dropdown {
  background-color: rgba(0, 21, 40, 0.9);
  border: 1px solid rgba(0, 247, 255, 0.3);
  box-shadow: 0 0 15px rgba(0,247,255,0.3);
}

::v-deep .el-select-dropdown__item {
  color: #FFFFFF;
}

::v-deep .el-select-dropdown__item.hover,
::v-deep .el-select-dropdown__item:hover {
  background-color: rgba(0, 247, 255, 0.2);
}

::v-deep .el-select-dropdown__item.selected {
  background-color: rgba(0, 247, 255, 0.3);
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
  font-weight: bold;
}

::v-deep .el-date-editor {
  background-color: rgba(0, 21, 40, 0.5);
  border: 1px solid rgba(0, 247, 255, 0.3);
}

::v-deep .el-date-editor .el-input__inner {
  background-color: transparent;
  border: none;
}

::v-deep .el-date-editor .el-input__icon {
  color: #00F7FF;
}

::v-deep .el-range-separator {
  color: #00F7FF;
}

::v-deep .el-date-editor.is-active,
::v-deep .el-date-editor:hover {
  border-color: #00F7FF;
  box-shadow: 0 0 8px rgba(0,247,255,0.5);
}

/* 日期选择器弹窗 */
::v-deep .el-picker-panel {
  background-color: rgba(0, 21, 40, 0.9);
  border: 1px solid rgba(0, 247, 255, 0.3);
  box-shadow: 0 0 15px rgba(0,247,255,0.3);
}

::v-deep .el-date-table td.available:hover,
::v-deep .el-date-table td.today {
  color: #00F7FF;
}

::v-deep .el-date-table td.current:not(.disabled) {
  background-color: rgba(0, 247, 255, 0.3);
  color: #FFFFFF;
}

::v-deep .el-picker-panel__footer {
  background-color: rgba(0, 21, 40, 0.9);
  border-top: 1px solid rgba(0, 247, 255, 0.1);
}

::v-deep .el-button {
  background-color: rgba(0, 21, 40, 0.7);
  border-color: #00F7FF;
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.8);
  transition: all 0.3s ease;
}

::v-deep .el-button:hover {
  background-color: rgba(0, 247, 255, 0.2);
  box-shadow: 0 0 15px rgba(0,247,255,0.5);
}

::v-deep .el-button--primary {
  background-color: rgba(0, 247, 255, 0.2);
  border-color: #00F7FF;
  color: #00F7FF;
}

::v-deep .el-button--primary:hover {
  background-color: rgba(0, 247, 255, 0.4);
  box-shadow: 0 0 20px rgba(0,247,255,0.7);
}

::v-deep .el-button--text {
  background: none;
  border: none;
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.5);
}

::v-deep .el-button--text:hover {
  color: #FFFFFF;
  text-shadow: 0 0 10px rgba(0,247,255,0.8);
  background: none;
  box-shadow: none;
}

::v-deep .el-input-number__decrease,
::v-deep .el-input-number__increase {
  background-color: rgba(0, 21, 40, 0.7);
  color: #00F7FF;
  border-color: rgba(0, 247, 255, 0.3);
}

::v-deep .el-input-number__decrease:hover,
::v-deep .el-input-number__increase:hover {
  color: #FFFFFF;
  background-color: rgba(0, 247, 255, 0.2);
}

::v-deep .el-input-number.is-controls-right .el-input-number__decrease,
::v-deep .el-input-number.is-controls-right .el-input-number__increase {
  background-color: rgba(0, 21, 40, 0.7);
  color: #00F7FF;
  border-color: rgba(0, 247, 255, 0.3);
}

::v-deep .el-input-number.is-controls-right .el-input-number__decrease:hover,
::v-deep .el-input-number.is-controls-right .el-input-number__increase:hover {
  color: #FFFFFF;
  background-color: rgba(0, 247, 255, 0.2);
}

::v-deep .el-switch__core {
  background-color: rgba(8, 20, 40, 0.8);
  border-color: rgba(0, 247, 255, 0.3);
}

::v-deep .el-switch.is-checked .el-switch__core {
  background-color: rgba(0, 247, 255, 0.5);
  border-color: #00F7FF;
}

::v-deep .el-collapse-item__header {
  background-color: rgba(0, 21, 40, 0.8);
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
  border-bottom: 1px solid rgba(0, 247, 255, 0.3);
}

::v-deep .el-collapse-item__wrap {
  background-color: rgba(0, 21, 40, 0.5);
  border-bottom: 1px solid rgba(0, 247, 255, 0.3);
}

::v-deep .el-collapse-item__content {
  padding: 15px;
  color: #FFFFFF;
}
</style>