#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask, jsonify, request
from flask_cors import CORS
import logging
import os
import sys
import jwt
from datetime import datetime, timedelta
from .database import db  # 添加数据库导入

# 设置日志记录
logging.basicConfig(level=logging.DEBUG,
                  format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                  handlers=[
                      logging.StreamHandler(),
                      logging.FileHandler('notification_api.log', encoding='utf-8')
                  ])
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)

# 配置CORS
CORS(app, resources={r"/*": {"origins": ["http://localhost:8080", "http://localhost:8081"], "supports_credentials": True}})

# JWT密钥和算法
SECRET_KEY = "your-secret-key-for-testing-only"
ALGORITHM = "HS256"

# 认证装饰器
def token_required(f):
    """验证JWT令牌的装饰器"""
    def decorated(*args, **kwargs):
        token = None

        # 从请求头获取token
        auth_header = request.headers.get('Authorization')
        if auth_header:
            token_parts = auth_header.split()
            if len(token_parts) == 2 and token_parts[0].lower() == 'bearer':
                token = token_parts[1]

        if not token:
            logger.warning("未提供认证令牌")
            return jsonify({"detail": "Not authenticated"}), 401

        try:
            # 验证token
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            user_id = int(payload.get("sub"))
        except Exception as e:
            logger.error(f"JWT验证错误: {str(e)}")
            return jsonify({"detail": "Invalid token"}), 401

        return f(*args, **kwargs)

    decorated.__name__ = f.__name__
    return decorated

# 通知API路由
@app.route('/', methods=['GET'])
def index():
    """首页"""
    logger.info("首页请求")
    return jsonify({"message": "通知API服务正在运行"})

@app.route('/api/v1/notifications/recent', methods=['GET'])
@token_required
def get_recent_notifications():
    """获取最近的通知"""
    logger.info("获取最近的通知请求")

    # 获取查询参数
    limit = request.args.get('limit', 10, type=int)
    
    # 从数据库获取真实通知数据
    try:
        db_notifications = db.get_notifications(limit=limit)
        notifications = [{
            "id": n[0],
            "title": n[1],
            "message": n[2],
            "level": n[3],
            "timestamp": n[5].isoformat(),
            "read": n[4]
        } for n in db_notifications]
        
        return jsonify(notifications)
    except Exception as e:
        logger.error(f"获取通知失败: {str(e)}")
        return jsonify({"error": "获取通知失败"}), 500

@app.route('/api/v1/alert-rules', methods=['GET', 'POST'])
@token_required
def manage_alert_rules():
    """管理告警规则"""
    if request.method == 'GET':
        logger.info("获取告警规则列表请求")

        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 20, type=int)
        status = request.args.get('status', 'all')
        rule_type = request.args.get('type', 'all')

        try:
            # 构建查询条件
            conditions = []
            params = []
            
            # 状态过滤
            if status == 'enabled':
                conditions.append("is_active = TRUE")
            elif status == 'disabled':
                conditions.append("is_active = FALSE")
                
            # 类型过滤
            if rule_type != 'all':
                conditions.append("type = %s")
                params.append(rule_type)
                
            # 构建完整查询
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            offset = (page - 1) * page_size
            
            # 获取总数
            with db._get_cursor() as cur:
                cur.execute(f"SELECT COUNT(*) FROM alert_rules {where_clause}", params)
                total = cur.fetchone()[0]
                
                # 获取分页数据
                cur.execute(f'''
                    SELECT * FROM alert_rules 
                    {where_clause}
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                ''', params + [page_size, offset])
                
                db_rules = cur.fetchall()
            
            # 转换格式
            rules = [{
                "id": r[0],
                "name": r[1],
                "type": r[2],
                "conditions": r[3],
                "enabled": r[4],
                "created_at": r[5].isoformat(),
                "updated_at": r[6].isoformat(),
                "notify_channels": r[7]
            } for r in db_rules]

            return jsonify({
                "data": rules,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total
                }
            })
        except Exception as e:
            logger.error(f"获取告警规则失败: {str(e)}")
            return jsonify({"error": "获取告警规则失败"}), 500

    elif request.method == 'POST':
        logger.info("创建告警规则请求")
        data = request.get_json()

        # 验证必需字段
        required_fields = ['name', 'type', 'conditions']
        if not all(field in data for field in required_fields):
            return jsonify({
                "error": "Missing required fields",
                "required": required_fields
            }), 400

        try:
            # 创建告警规则
            new_rule = db.create_alert_rule({
                "name": data["name"],
                "type": data["type"],
                "conditions": data["conditions"],
                "notify_channels": data.get("notify_channels", []),
                "is_active": data.get("enabled", True)
            })

            return jsonify({
                "id": new_rule[0],
                "name": new_rule[1],
                "type": new_rule[2],
                "conditions": new_rule[3],
                "enabled": new_rule[4],
                "created_at": new_rule[5].isoformat(),
                "updated_at": new_rule[6].isoformat(),
                "notify_channels": new_rule[7]
            }), 201
        except Exception as e:
            logger.error(f"创建告警规则失败: {str(e)}")
            return jsonify({"error": "创建告警规则失败"}), 500

@app.route('/api/v1/alert-rules/<rule_id>', methods=['PUT', 'DELETE'])
@token_required 
def update_or_delete_rule(rule_id):
    """更新或删除告警规则"""
    if request.method == 'PUT':
        logger.info(f"更新告警规则请求: {rule_id}")
        data = request.get_json()
        
        # 验证数据
        errors = validate_rule_data(data)
        if errors:
            return jsonify({
                "error": "数据验证失败",
                "details": errors
            }), 400
            
        try:
            # 确保enabled字段存在
            if 'enabled' not in data:
                data['enabled'] = True
                
            # 更新告警规则
            update_data = {
                "name": data["name"],
                "type": data["type"],
                "conditions": data["conditions"],
                "notify_channels": data.get("notify_channels", []),
                "is_active": data["enabled"]
            }
            
            with db._get_cursor() as cur:
                # 检查规则是否存在
                cur.execute("SELECT id FROM alert_rules WHERE id = %s", (rule_id,))
                if not cur.fetchone():
                    return jsonify({"error": "告警规则不存在"}), 404
                    
                # 执行更新
                updated = db.update_alert_rule(rule_id, update_data)
                if not updated:
                    return jsonify({"error": "更新告警规则失败"}), 500
                    
                # 获取更新后的规则
                cur.execute("SELECT * FROM alert_rules WHERE id = %s", (rule_id,))
                updated_rule = cur.fetchone()
                
            return jsonify({
                "id": updated_rule[0],
                "name": updated_rule[1],
                "type": updated_rule[2],
                "conditions": updated_rule[3],
                "enabled": updated_rule[4],
                "created_at": updated_rule[5].isoformat(),
                "updated_at": updated_rule[6].isoformat(),
                "notify_channels": updated_rule[7]
            })
        except Exception as e:
            logger.error(f"更新告警规则失败: {str(e)}", exc_info=True)
            return jsonify({
                "error": "更新告警规则失败",
                "details": str(e)
            }), 500
    
    elif request.method == 'DELETE':
        logger.info(f"删除告警规则请求: {rule_id}")
        try:
            with db._get_cursor() as cur:
                # 检查规则是否存在
                cur.execute("SELECT id FROM alert_rules WHERE id = %s", (rule_id,))
                if not cur.fetchone():
                    return jsonify({"error": "告警规则不存在"}), 404
                    
                # 执行删除
                deleted = db.delete_alert_rule(rule_id)
                if not deleted:
                    return jsonify({"error": "删除告警规则失败"}), 500
                    
                # 验证删除是否成功
                cur.execute("SELECT id FROM alert_rules WHERE id = %s", (rule_id,))
                if cur.fetchone():
                    return jsonify({"error": "删除告警规则失败"}), 500
                    
            return jsonify({
                "success": True,
                "message": f"规则 {rule_id} 已删除",
                "deleted_at": datetime.now().isoformat()
            }), 200
        except Exception as e:
            logger.error(f"删除告警规则失败: {str(e)}", exc_info=True)
            return jsonify({
                "error": "删除告警规则失败",
                "details": str(e)
            }), 500

@app.route('/api/v1/notifications/<notification_id>/read', methods=['PUT'])
@token_required
def mark_as_read(notification_id):
    """标记通知为已读"""
    logger.info(f"标记通知为已读: {notification_id}")
    return jsonify({
        "id": notification_id,
        "read": True,
        "read_at": datetime.now().isoformat()
    })

def validate_rule_data(data):
    """验证告警规则数据"""
    errors = []
    
    # 验证名称
    if not data.get('name') or len(data['name']) > 100:
        errors.append("名称不能为空且长度需小于100字符")
    
    # 验证条件
    if not isinstance(data.get('conditions'), list) or len(data['conditions']) == 0:
        errors.append("至少需要一个有效条件")
    
    # 验证通知渠道
    valid_channels = ['app', 'email', 'sms']
    if not all(channel in valid_channels for channel in data.get('notify_channels', [])):
        errors.append(f"无效的通知渠道，可用渠道: {valid_channels}")
    
    return errors

@app.errorhandler(400)
def bad_request(error):
    return jsonify({
        "error": "请求参数错误",
        "message": str(error),
        "status": 400
    }), 400

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "error": "资源未找到",
        "message": str(error),
        "status": 404
    }), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"服务器错误: {str(error)}")
    return jsonify({
        "error": "服务器内部错误",
        "message": "请稍后再试",
        "status": 500
    }), 500

# 主函数
if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8002))
    logger.info(f"启动通知API服务，端口: {port}")
    app.run(host='0.0.0.0', port=port, debug=True)