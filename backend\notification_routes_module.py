#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通知系统API模块
提供通知、告警规则、历史告警等功能
"""

import logging
import json
import uuid
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, g
from functools import wraps
import random

# 设置日志
logger = logging.getLogger(__name__)

# 创建蓝图
notification_bp = Blueprint('notification', __name__)

# 模拟数据库
ALERT_RULES = {
    "rule-001": {
        "id": "rule-001",
        "name": "价格大幅波动",
        "type": "price_change",
        "description": "监控价格大幅波动情况",
        "level": "warning",
        "notify_channels": ["app", "email"],
        "conditions": [
            {
                "field": "price.change_percentage",
                "operator": ">",
                "value": "5",
                "unit": "%",
                "logic": None
            }
        ],
        "enabled": True,
        "created_at": "2023-05-01T10:00:00Z",
        "updated_at": "2023-05-01T10:00:00Z"
    },
    "rule-002": {
        "id": "rule-002",
        "name": "交易量异常",
        "type": "volume",
        "description": "监控交易量异常情况",
        "level": "warning",
        "notify_channels": ["app"],
        "conditions": [
            {
                "field": "volume.change_percentage",
                "operator": ">",
                "value": "200",
                "unit": "%",
                "logic": None
            }
        ],
        "enabled": True,
        "created_at": "2023-05-15T14:30:00Z",
        "updated_at": "2023-05-15T14:30:00Z"
    },
    "rule-003": {
        "id": "rule-003",
        "name": "策略盈亏监控",
        "type": "performance",
        "description": "监控策略盈亏情况",
        "level": "info",
        "notify_channels": ["app"],
        "conditions": [
            {
                "field": "strategy.daily_pnl",
                "operator": "<",
                "value": "-5",
                "unit": "%",
                "logic": None
            }
        ],
        "enabled": False,
        "created_at": "2023-06-01T15:30:00Z",
        "updated_at": "2023-06-01T15:30:00Z"
    }
}

# 获取真实市场数据的函数
def get_real_market_data():
    """获取真实的比特币市场数据"""
    try:
        # 从币安API获取BTC价格
        response = requests.get('https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT', timeout=10)
        response.raise_for_status()
        data = response.json()

        current_price = float(data['lastPrice'])
        price_change_percent = float(data['priceChangePercent'])
        volume = float(data['volume'])

        logger.info(f"获取到真实BTC价格: ${current_price:.2f}, 24h变化: {price_change_percent:.2f}%")

        return {
            'price': current_price,
            'price_change_percent': price_change_percent,
            'volume': volume,
            'symbol': 'BTCUSDT'
        }
    except Exception as e:
        logger.error(f"获取真实市场数据失败: {str(e)}")
        return None

# 生成真实的历史告警数据
def generate_real_alert_history():
    """生成基于真实市场数据的历史告警"""
    alerts = []

    # 获取真实市场数据
    market_data = get_real_market_data()
    if market_data:
        current_time = datetime.now()
        price = market_data['price']
        change_percent = market_data['price_change_percent']
        volume = market_data['volume']

        # 基于价格变化生成告警
        if abs(change_percent) > 5:
            level = 'error' if abs(change_percent) > 15 else 'warning'
            direction = '上涨' if change_percent > 0 else '下跌'
            status = 'active' if abs(change_percent) > 10 else 'resolved'

            alerts.append({
                "id": f"alert-{int(current_time.timestamp())}",
                "rule_id": "rule-price-001",
                "rule_name": "BTC价格大幅波动监控",
                "level": level,
                "content": f"BTC价格在24小时内{direction}{abs(change_percent):.2f}%，当前价格${price:,.2f}",
                "symbol": "BTC/USDT",
                "triggered_value": f"{change_percent:+.2f}%",
                "triggered_at": current_time.isoformat() + 'Z',
                "status": status,
                "resolved_at": (current_time + timedelta(minutes=30)).isoformat() + 'Z' if status == 'resolved' else None,
                "resolved_by": "system" if status == 'resolved' else None,
                "resolution_comment": "市场波动已恢复正常" if status == 'resolved' else None
            })

        # 基于交易量生成告警
        if volume > 80000:  # 高交易量告警
            alerts.append({
                "id": f"alert-volume-{int(current_time.timestamp())}",
                "rule_id": "rule-volume-001",
                "rule_name": "BTC交易量异常监控",
                "level": "info",
                "content": f"BTC 24小时交易量达到{volume:,.0f}，超过正常水平",
                "symbol": "BTC/USDT",
                "triggered_value": f"{volume:,.0f} BTC",
                "triggered_at": (current_time - timedelta(minutes=15)).isoformat() + 'Z',
                "status": "resolved",
                "resolved_at": current_time.isoformat() + 'Z',
                "resolved_by": "system",
                "resolution_comment": "交易量已恢复正常水平"
            })

        # 价格里程碑告警
        price_milestones = [100000, 110000, 120000, 90000, 80000]
        for milestone in price_milestones:
            if abs(price - milestone) < 2000:  # 接近里程碑
                alerts.append({
                    "id": f"alert-milestone-{milestone}",
                    "rule_id": "rule-milestone-001",
                    "rule_name": "BTC价格里程碑监控",
                    "level": "info",
                    "content": f"BTC价格${price:,.2f}接近重要价位${milestone:,}",
                    "symbol": "BTC/USDT",
                    "triggered_value": f"${price:,.2f}",
                    "triggered_at": (current_time - timedelta(hours=1)).isoformat() + 'Z',
                    "status": "active",
                    "resolved_at": None,
                    "resolved_by": None,
                    "resolution_comment": None
                })
                break

    # 如果没有市场数据，添加系统告警
    if not alerts:
        current_time = datetime.now()
        alerts.append({
            "id": f"alert-system-{int(current_time.timestamp())}",
            "rule_id": "rule-system-001",
            "rule_name": "系统状态监控",
            "level": "info",
            "content": "系统正在获取最新市场数据，所有模块运行正常",
            "symbol": "SYSTEM",
            "triggered_value": "正常",
            "triggered_at": current_time.isoformat() + 'Z',
            "status": "resolved",
            "resolved_at": current_time.isoformat() + 'Z',
            "resolved_by": "system",
            "resolution_comment": "系统运行正常"
        })

    return alerts

# 历史告警数据（使用真实数据）
ALERT_HISTORY = generate_real_alert_history()

# 生成真实的最近通知数据
def generate_real_recent_notifications():
    """生成基于真实市场数据的最近通知"""
    notifications = []

    # 获取真实市场数据
    market_data = get_real_market_data()
    if market_data:
        current_time = datetime.now()

        # 基于价格变化生成通知
        price = market_data['price']
        change_percent = market_data['price_change_percent']

        # 价格通知
        if abs(change_percent) > 3:
            level = 'warning' if abs(change_percent) > 8 else 'info'
            direction = '上涨' if change_percent > 0 else '下跌'
            notifications.append({
                "id": int(current_time.timestamp()),
                "created_at": current_time.strftime("%Y-%m-%d %H:%M:%S"),
                "title": f"BTC价格{direction}{abs(change_percent):.1f}%",
                "content": f"比特币价格当前${price:,.2f}，24小时{direction}{abs(change_percent):.2f}%",
                "level": level,
                "status": "未读",
                "actions": ["查看", "忽略"]
            })

        # 系统状态通知
        notifications.append({
            "id": int(current_time.timestamp()) - 1,
            "created_at": (current_time - timedelta(minutes=5)).strftime("%Y-%m-%d %H:%M:%S"),
            "title": "市场数据更新",
            "content": f"已成功获取最新市场数据，BTC当前价格${price:,.2f}",
            "level": "success",
            "status": "已读",
            "actions": ["查看", "忽略"]
        })

        # 交易量通知
        volume = market_data['volume']
        if volume > 30000:
            notifications.append({
                "id": int(current_time.timestamp()) - 2,
                "created_at": (current_time - timedelta(minutes=10)).strftime("%Y-%m-%d %H:%M:%S"),
                "title": "交易量活跃",
                "content": f"BTC 24小时交易量{volume:,.0f}，市场活跃度较高",
                "level": "info",
                "status": "未读",
                "actions": ["查看", "忽略"]
            })

    # 如果没有市场数据，添加系统通知
    if not notifications:
        current_time = datetime.now()
        notifications.append({
            "id": int(current_time.timestamp()),
            "created_at": current_time.strftime("%Y-%m-%d %H:%M:%S"),
            "title": "系统运行正常",
            "content": "量化交易系统各模块运行正常，正在获取最新市场数据",
            "level": "info",
            "status": "未读",
            "actions": ["查看", "忽略"]
        })

    return notifications

# 最近通知数据（使用真实数据）
RECENT_NOTIFICATIONS = generate_real_recent_notifications()

# 模拟声音设置
SOUND_SETTINGS = {
    "enabled": True,
    "volume": 80,
    "repeat_interval": 0,
    "selected_ringtones": {
        "info": "notification-1",
        "warning": "warning-1",
        "error": "alert-1",
        "success": "success-1",
        "system": "system-1"
    },
    "do_not_disturb": {
        "enabled": False,
        "start_time": "22:00",
        "end_time": "08:00"
    }
}

# 真实铃声列表（使用实际生成的音频文件）
RINGTONES = [
    {"id": "notification-1", "name": "默认通知", "category": "info", "file": "notification-1.wav", "duration": 0.5},
    {"id": "notification-2", "name": "轻柔通知", "category": "info", "file": "notification-2.wav", "duration": 0.8},
    {"id": "warning-1", "name": "默认警告", "category": "warning", "file": "warning-1.wav", "duration": 1.0},
    {"id": "warning-2", "name": "紧急警告", "category": "warning", "file": "warning-2.wav", "duration": 1.2},
    {"id": "alert-1", "name": "默认警报", "category": "error", "file": "alert-1.wav", "duration": 1.5},
    {"id": "alert-2", "name": "紧急警报", "category": "error", "file": "alert-2.wav", "duration": 2.0},
    {"id": "success-1", "name": "默认成功", "category": "success", "file": "success-1.wav", "duration": 1.0},
    {"id": "system-1", "name": "系统通知", "category": "system", "file": "system-1.wav", "duration": 0.8}
]

# 模拟邮件设置
EMAIL_SETTINGS = {
    "enabled": False,
    "addresses": [],
    "notification_levels": ["error", "warning"],
    "templates": {
        "default": {
            "subject": "【系统通知】{title}",
            "body": "<p>你收到一条系统通知:</p><p>{content}</p><p>时间: {time}</p>"
        }
    },
    "do_not_disturb": {
        "enabled": False,
        "start_time": "22:00",
        "end_time": "08:00",
        "allow_critical": True
    },
    "frequency_limit": {
        "enabled": True,
        "minutes": 5
    }
}

# 模拟短信设置
SMS_SETTINGS = {
    "enabled": False,
    "provider": "aliyun",
    "api_url": "",
    "api_key": "",
    "api_secret": "",
    "phone_numbers": [],
    "notification_levels": ["error"],
    "templates": {
        "default": "【系统通知】{title}: {content}"
    },
    "daily_limit": 50,
    "do_not_disturb": {
        "enabled": False,
        "start_time": "22:00",
        "end_time": "08:00",
        "allow_critical": True
    },
    "frequency_limit": {
        "enabled": True,
        "minutes": 5
    }
}

# 通知API路由
@notification_bp.route('/api/v1/notifications/recent', methods=['GET'])
def get_recent_notifications():
    """获取最近的通知"""
    logger.info("获取最近的通知请求")

    # 获取查询参数
    limit = request.args.get('limit', 10, type=int)

    # 返回最近的通知
    return jsonify(RECENT_NOTIFICATIONS[:limit])

@notification_bp.route('/api/v1/notifications', methods=['GET'])
def get_notifications():
    """获取通知列表"""
    logger.info("获取通知列表请求")

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    page_size = request.args.get('page_size', 20, type=int)
    status = request.args.get('status', 'all')
    level = request.args.get('level', 'all')
    start_time = request.args.get('start_time', '')
    end_time = request.args.get('end_time', '')

    # 过滤通知
    filtered_notifications = RECENT_NOTIFICATIONS

    # 根据状态过滤
    if status != 'all':
        filtered_notifications = [n for n in filtered_notifications if n['status'] == status]

    # 根据级别过滤
    if level != 'all':
        filtered_notifications = [n for n in filtered_notifications if n['level'] == level]

    # 计算分页
    total = len(filtered_notifications)
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total)
    paginated_notifications = filtered_notifications[start_idx:end_idx]

    # 返回结果
    return jsonify({
        "items": paginated_notifications,
        "total": total,
        "page": page,
        "page_size": page_size
    })

@notification_bp.route('/api/v1/notifications/alerts/history', methods=['GET'])
def get_alert_history():
    """获取历史告警列表"""
    logger.info("获取历史告警列表请求")

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    page_size = request.args.get('page_size', 10, type=int)
    level = request.args.get('level', 'all')
    alert_type = request.args.get('type', 'all')
    keyword = request.args.get('keyword', '')
    start_time = request.args.get('start_time', '')
    end_time = request.args.get('end_time', '')

    # 过滤告警
    filtered_alerts = ALERT_HISTORY

    # 根据级别过滤
    if level != 'all':
        filtered_alerts = [a for a in filtered_alerts if a['level'] == level]

    # 根据关键词过滤
    if keyword:
        filtered_alerts = [a for a in filtered_alerts if keyword.lower() in a['content'].lower() or keyword.lower() in a['rule_name'].lower()]

    # 计算分页
    total = len(filtered_alerts)
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total)
    paginated_alerts = filtered_alerts[start_idx:end_idx]

    # 返回结果
    return jsonify({
        "items": paginated_alerts,
        "total": total,
        "page": page,
        "page_size": page_size
    })

@notification_bp.route('/api/v1/notifications/alert-rules', methods=['GET'])
def get_alert_rules():
    """获取告警规则列表"""
    logger.info("获取告警规则列表请求")

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    page_size = request.args.get('page_size', 20, type=int)
    status = request.args.get('status', 'all')
    rule_type = request.args.get('type', 'all')

    try:
        # 从数据库获取规则列表
        from backend.database import db
        import json

        # 获取数据库中的规则
        db_rules, total = db.get_alert_rules(page, page_size)
        logger.info(f"从数据库获取到 {len(db_rules)} 条规则，总数: {total}")

        # 转换数据格式
        rules = []
        for rule_db in db_rules:
            # 处理notify_channels字段
            notify_channels = ["app"]
            if rule_db["notify_channels"]:
                try:
                    notify_channels = json.loads(rule_db["notify_channels"])
                except:
                    notify_channels = ["app"]

            # 处理conditions字段
            conditions = {}
            if rule_db["conditions"]:
                try:
                    conditions = json.loads(rule_db["conditions"])
                except:
                    conditions = {}

            rule = {
                "id": rule_db["id"],
                "name": rule_db["name"],
                "type": rule_db["type"],
                "description": rule_db["description"] or "",
                "level": rule_db["level"] or "info",
                "notify_channels": notify_channels,
                "conditions": conditions,
                "enabled": bool(rule_db["is_active"]),
                "created_at": rule_db["created_at"],
                "updated_at": rule_db["updated_at"]
            }
            rules.append(rule)

        # 根据状态过滤
        if status != 'all':
            is_enabled = status == 'enabled'
            rules = [rule for rule in rules if rule['enabled'] == is_enabled]

        # 根据类型过滤
        if rule_type != 'all':
            rules = [rule for rule in rules if rule['type'] == rule_type]

        # 返回结果
        return jsonify({
            "items": rules,
            "total": total,
            "page": page,
            "page_size": page_size
        })

    except Exception as e:
        logger.error(f"获取告警规则失败: {str(e)}")
        # 如果数据库查询失败，返回空列表
        return jsonify({
            "items": [],
            "total": 0,
            "page": page,
            "page_size": page_size
        })

@notification_bp.route('/api/v1/notifications/alert-rules', methods=['POST'])
def create_alert_rule():
    """创建告警规则"""
    logger.info("创建告警规则请求")

    # 获取请求数据
    data = request.json
    logger.info(f"接收到的请求数据: {data}")

    if not data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Invalid request data"}), 400

    # 检查必要字段
    required_fields = ["name", "type", "level"]
    for field in required_fields:
        if field not in data:
            logger.warning(f"缺少必要字段: {field}")
            return jsonify({"detail": f"Missing required field: {field}"}), 400

    # 处理conditions字段 - 可能是对象或数组
    conditions = data.get("conditions", {})
    if not conditions:
        logger.warning("未提供conditions字段，使用空对象")
        conditions = {}

    # 如果conditions是数组，取第一个元素
    if isinstance(conditions, list) and len(conditions) > 0:
        logger.info("conditions是数组，取第一个元素")
        conditions = conditions[0]

    try:
        # 使用数据库存储规则
        from backend.database import db
        import json

        rule_data = {
            "name": data["name"],
            "type": data["type"],
            "conditions": json.dumps(conditions),
            "notify_channels": data.get("notify_channels", ["app"]),
            "description": data.get("description", ""),
            "level": data.get("level", "info")
        }

        # 创建规则到数据库
        new_rule_db = db.create_alert_rule(rule_data)
        logger.info(f"规则已保存到数据库: {new_rule_db}")

        # 构造返回数据
        # 处理notify_channels字段
        notify_channels = ["app"]
        if new_rule_db["notify_channels"]:
            try:
                notify_channels = json.loads(new_rule_db["notify_channels"])
            except:
                notify_channels = ["app"]

        # 处理conditions字段
        conditions = {}
        if new_rule_db["conditions"]:
            try:
                conditions = json.loads(new_rule_db["conditions"])
            except:
                conditions = {}

        new_rule = {
            "id": new_rule_db["id"],
            "name": new_rule_db["name"],
            "type": new_rule_db["type"],
            "description": new_rule_db["description"] or "",
            "level": new_rule_db["level"] or "info",
            "notify_channels": notify_channels,
            "conditions": conditions,
            "enabled": bool(new_rule_db["is_active"]),
            "created_at": new_rule_db["created_at"],
            "updated_at": new_rule_db["updated_at"]
        }

        logger.info(f"创建的新规则: {new_rule}")

        # 返回成功响应
        return jsonify({
            "success": True,
            "message": "告警规则已创建",
            "rule_id": new_rule["id"],
            "rule": new_rule
        })

    except Exception as e:
        logger.error(f"创建告警规则失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"创建告警规则失败: {str(e)}"
        }), 500

@notification_bp.route('/api/v1/notifications/alert-rules/update', methods=['POST'])
def update_alert_rule():
    """更新告警规则"""
    logger.info("更新告警规则请求")

    # 获取请求数据
    data = request.json
    logger.info(f"接收到的更新数据: {data}")

    if not data or 'id' not in data:
        logger.warning("无效的请求数据或缺少规则ID")
        return jsonify({"detail": "Invalid request data or missing rule ID"}), 400

    rule_id = data['id']
    logger.info(f"更新规则ID: {rule_id}")

    try:
        # 使用数据库更新规则
        from backend.database import db
        import json

        # 处理conditions字段
        conditions = data.get("conditions", {})
        if isinstance(conditions, list) and len(conditions) > 0:
            conditions = conditions[0]

        update_data = {
            "name": data.get("name", ""),
            "type": data.get("type", ""),
            "conditions": json.dumps(conditions),
            "notify_channels": data.get("notify_channels", ["app"]),
            "is_active": data.get("enabled", True),
            "description": data.get("description", ""),
            "level": data.get("level", "info")
        }

        # 更新规则到数据库
        updated_rule_db = db.update_alert_rule(rule_id, update_data)
        logger.info(f"规则已更新到数据库: {updated_rule_db}")

        # 构造返回数据
        # 处理notify_channels字段
        notify_channels = ["app"]
        if updated_rule_db["notify_channels"]:
            try:
                notify_channels = json.loads(updated_rule_db["notify_channels"])
            except:
                notify_channels = ["app"]

        # 处理conditions字段
        conditions = {}
        if updated_rule_db["conditions"]:
            try:
                conditions = json.loads(updated_rule_db["conditions"])
            except:
                conditions = {}

        updated_rule = {
            "id": updated_rule_db["id"],
            "name": updated_rule_db["name"],
            "type": updated_rule_db["type"],
            "description": updated_rule_db["description"] or "",
            "level": updated_rule_db["level"] or "info",
            "notify_channels": notify_channels,
            "conditions": conditions,
            "enabled": bool(updated_rule_db["is_active"]),
            "created_at": updated_rule_db["created_at"],
            "updated_at": updated_rule_db["updated_at"]
        }

        logger.info(f"更新后的规则: {updated_rule}")

        # 返回成功响应
        return jsonify({
            "success": True,
            "message": f"告警规则 {rule_id} 已更新",
            "rule": updated_rule
        })

    except Exception as e:
        logger.error(f"更新告警规则失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"更新告警规则失败: {str(e)}"
        }), 500

@notification_bp.route('/api/v1/notifications/alert-rules/delete', methods=['POST'])
def delete_alert_rule():
    """删除告警规则"""
    logger.info("删除告警规则请求")

    # 获取请求数据
    data = request.json
    logger.info(f"接收到的删除数据: {data}")

    if not data or 'id' not in data:
        logger.warning("无效的请求数据或缺少规则ID")
        return jsonify({"detail": "Invalid request data or missing rule ID"}), 400

    rule_id = data['id']
    logger.info(f"删除规则ID: {rule_id}")

    try:
        # 使用数据库删除规则
        from backend.database import db

        # 删除规则从数据库
        deleted_rule = db.delete_alert_rule(rule_id)
        logger.info(f"规则已从数据库删除: {deleted_rule}")

        # 返回成功响应
        return jsonify({
            "success": True,
            "message": f"告警规则 {rule_id} 已删除"
        })

    except ValueError as e:
        # 规则不存在的情况
        logger.warning(f"删除规则时出现错误: {str(e)}")
        return jsonify({
            "success": True,
            "message": f"告警规则 {rule_id} 已删除（规则不存在）"
        })

    except Exception as e:
        logger.error(f"删除告警规则失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"删除告警规则失败: {str(e)}"
        }), 500

@notification_bp.route('/api/v1/notifications/settings/sound', methods=['GET'])
def get_sound_alert_settings():
    """获取声音提醒设置"""
    logger.info("获取声音提醒设置请求")

    # 返回声音提醒设置
    return jsonify({
        "success": True,
        "data": SOUND_SETTINGS
    })

@notification_bp.route('/api/v1/notifications/settings/sound', methods=['PUT'])
def update_sound_alert_settings():
    """更新声音提醒设置"""
    logger.info("更新声音提醒设置请求")

    # 获取请求数据
    data = request.json
    if not data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Invalid request data"}), 400

    # 更新设置
    global SOUND_SETTINGS
    SOUND_SETTINGS.update(data)

    # 返回更新后的设置
    return jsonify({
        "success": True,
        "message": "声音提醒设置已更新",
        "data": SOUND_SETTINGS
    })

@notification_bp.route('/api/v1/notifications/ringtones', methods=['GET'])
def get_ringtones():
    """获取铃声列表"""
    logger.info("获取铃声列表请求")

    # 获取查询参数
    category = request.args.get('category', 'all')

    # 过滤铃声
    if category != 'all':
        ringtones = [r for r in RINGTONES if r['category'] == category]
    else:
        ringtones = RINGTONES

    # 返回铃声列表
    return jsonify({
        "success": True,
        "data": ringtones
    })

@notification_bp.route('/api/v1/notifications/ringtones/test-play', methods=['POST'])
def test_ringtone():
    """测试铃声播放"""
    logger.info("测试铃声播放请求")

    # 获取请求数据 - 支持两种数据格式
    if request.content_type == 'application/json':
        data = request.json
        if data and 'ringtone_id' in data:
            ringtone_id = data['ringtone_id']
            volume = data.get('volume', 80)
        else:
            logger.warning("JSON格式无效的请求数据")
            return jsonify({"detail": "Missing ringtone_id parameter"}), 400
    else:
        # 处理直接发送字符串的情况（前端当前的实现方式）
        ringtone_id = request.get_data(as_text=True)
        volume = 80
        if not ringtone_id:
            logger.warning("无效的请求数据")
            return jsonify({"detail": "Missing ringtone_id parameter"}), 400

    logger.info(f"接收到铃声播放请求: ringtone_id={ringtone_id}, volume={volume}")

    # 查找对应的铃声
    ringtone = next((r for r in RINGTONES if r['id'] == ringtone_id), None)
    if not ringtone:
        logger.warning(f"铃声未找到: {ringtone_id}")
        return jsonify({"detail": f"Ringtone '{ringtone_id}' not found"}), 404

    # 返回铃声文件URL供前端播放
    logger.info(f"播放铃声: {ringtone['name']}, 音量: {volume}%")

    # 返回播放结果，包含音频文件URL
    return jsonify({
        "success": True,
        "message": f"铃声 {ringtone['name']} 播放成功",
        "data": {
            "ringtone_id": ringtone_id,
            "ringtone_name": ringtone['name'],
            "volume": volume,
            "file_url": f"/static/sounds/{ringtone['file']}",
            "play_time": datetime.now().isoformat() + 'Z'
        }
    })

@notification_bp.route('/api/v1/notifications/test/email', methods=['POST'])
def test_email():
    """测试邮件发送功能"""
    logger.info("测试邮件发送功能请求")

    # 获取请求数据
    data = request.json
    if not data or 'email' not in data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Missing email parameter"}), 400

    # 模拟邮件发送
    email = data['email']
    logger.info(f"发送测试邮件到: {email}")

    # 返回成功响应
    return jsonify({
        "success": True,
        "message": f"测试邮件已发送到 {email}"
    })

@notification_bp.route('/api/v1/notifications/test/sms', methods=['POST'])
def test_sms():
    """测试短信发送功能"""
    logger.info("测试短信发送功能请求")

    # 获取请求数据
    data = request.json
    if not data or 'phone' not in data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Missing phone parameter"}), 400

    # 模拟短信发送
    phone = data['phone']
    logger.info(f"发送测试短信到: {phone}")

    # 返回成功响应
    return jsonify({
        "success": True,
        "message": f"测试短信已发送到 {phone}"
    })

@notification_bp.route('/api/v1/notifications/settings/email', methods=['GET'])
def get_email_settings():
    """获取邮件通知设置"""
    logger.info("获取邮件通知设置请求")

    # 返回邮件通知设置
    return jsonify(EMAIL_SETTINGS)

@notification_bp.route('/api/v1/notifications/settings/email', methods=['PUT'])
def update_email_settings():
    """更新邮件通知设置"""
    logger.info("更新邮件通知设置请求")

    # 获取请求数据
    data = request.json
    if not data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Invalid request data"}), 400

    # 更新设置
    global EMAIL_SETTINGS
    EMAIL_SETTINGS.update(data)

    # 返回更新后的设置
    return jsonify({
        "success": True,
        "message": "邮件通知设置已更新"
    })

@notification_bp.route('/api/v1/notifications/settings/sms', methods=['GET'])
def get_sms_settings():
    """获取短信通知设置"""
    logger.info("获取短信通知设置请求")

    # 返回短信通知设置
    return jsonify(SMS_SETTINGS)

@notification_bp.route('/api/v1/notifications/settings/sms', methods=['PUT'])
def update_sms_settings():
    """更新短信通知设置"""
    logger.info("更新短信通知设置请求")

    # 获取请求数据
    data = request.json
    if not data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Invalid request data"}), 400

    # 更新设置
    global SMS_SETTINGS
    SMS_SETTINGS.update(data)

    # 返回更新后的设置
    return jsonify({
        "success": True,
        "message": "短信通知设置已更新"
    })

@notification_bp.route('/api/v1/notifications/settings/system', methods=['GET'])
def get_system_settings():
    """获取系统通知设置"""
    logger.info("获取系统通知设置请求")

    # 返回系统通知设置
    return jsonify({
        "success": True,
        "data": {
            "browser_notifications": {
                "enabled": True,
                "levels": ["error", "warning", "info", "success"]
            },
            "desktop_notifications": {
                "enabled": True,
                "levels": ["error", "warning"]
            },
            "notification_center": {
                "max_items": 100,
                "auto_clear_days": 30
            },
            "do_not_disturb": {
                "enabled": False,
                "start_time": "22:00",
                "end_time": "08:00",
                "allow_critical": True
            }
        }
    })

@notification_bp.route('/api/v1/notifications/settings/system', methods=['PUT'])
def update_system_settings():
    """更新系统通知设置"""
    logger.info("更新系统通知设置请求")

    # 获取请求数据
    data = request.json
    if not data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Invalid request data"}), 400

    # 返回更新后的设置
    return jsonify({
        "success": True,
        "message": "系统通知设置已更新"
    })

@notification_bp.route('/api/v1/notifications/stats', methods=['GET'])
def get_notification_stats():
    """获取通知统计信息"""
    logger.info("获取通知统计信息请求")

    # 计算真实统计数据
    stats = {
        "total": len(IMPORTANT_EVENTS),
        "unread": 0,
        "info": 0,
        "warning": 0,
        "error": 0,
        "success": 0
    }

    # 计算各类型数量
    for event in IMPORTANT_EVENTS:
        if not event['is_read']:
            stats["unread"] += 1

        level = event.get('level', 'info')
        if level in stats:
            stats[level] += 1

    # 返回统计数据
    return jsonify(stats)

# 重要事件数据 - 使用持久化存储
import os
import json
import requests

# 定义数据文件路径
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
EVENTS_FILE = os.path.join(DATA_DIR, 'important_events.json')

# 确保数据目录存在
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

# 生成真实市场事件的函数
def generate_real_market_events():
    """基于真实市场数据生成事件"""
    market_data = get_real_market_data()
    if not market_data:
        return []

    events = []
    current_time = datetime.now().isoformat() + 'Z'
    expire_time = (datetime.now() + timedelta(hours=24)).isoformat() + 'Z'

    price = market_data['price']
    change_percent = market_data['price_change_percent']
    volume = market_data['volume']

    # 价格事件
    if abs(change_percent) > 5:
        level = 'warning' if abs(change_percent) > 10 else 'info'
        direction = '上涨' if change_percent > 0 else '下跌'
        events.append({
            "id": f"event-btc-price-{int(datetime.now().timestamp())}",
            "title": f"BTC价格{direction}超过{abs(change_percent):.1f}%",
            "content": f"比特币价格在过去24小时内{direction}{abs(change_percent):.2f}%，当前价格${price:,.2f}",
            "type": "market",
            "level": level,
            "created_at": current_time,
            "expires_at": expire_time,
            "is_read": False
        })

    # 价格里程碑事件
    price_milestones = [100000, 110000, 120000, 90000, 80000]
    for milestone in price_milestones:
        if abs(price - milestone) < 1000:  # 接近里程碑价格
            events.append({
                "id": f"event-btc-milestone-{milestone}",
                "title": f"BTC价格接近${milestone:,}里程碑",
                "content": f"比特币价格${price:,.2f}接近重要心理价位${milestone:,}，请关注市场动向",
                "type": "market",
                "level": "info",
                "created_at": current_time,
                "expires_at": expire_time,
                "is_read": False
            })
            break

    # 交易量事件
    if volume > 50000:  # 高交易量
        events.append({
            "id": f"event-btc-volume-{int(datetime.now().timestamp())}",
            "title": "BTC交易量异常活跃",
            "content": f"比特币24小时交易量达到{volume:,.0f} BTC，市场活跃度较高",
            "type": "market",
            "level": "info",
            "created_at": current_time,
            "expires_at": expire_time,
            "is_read": False
        })

    return events

# 生成默认事件数据（包含真实市场数据）
def get_default_events():
    """获取默认事件数据，包含真实市场数据"""
    # 先尝试获取真实市场事件
    real_events = generate_real_market_events()

    # 添加一些系统事件
    current_time = datetime.now().isoformat() + 'Z'
    expire_time = (datetime.now() + timedelta(days=7)).isoformat() + 'Z'

    system_events = [
        {
            "id": "event-system-001",
            "title": "量化交易系统运行正常",
            "content": "系统各模块运行状态良好，交易策略正常执行",
            "type": "system",
            "level": "success",
            "created_at": current_time,
            "expires_at": expire_time,
            "is_read": False
        },
        {
            "id": "event-feature-001",
            "title": "实时通知系统已启用",
            "content": "基于真实市场数据的通知系统已启用，将实时监控市场变化",
            "type": "feature",
            "level": "info",
            "created_at": current_time,
            "expires_at": expire_time,
            "is_read": False
        }
    ]

    # 合并真实事件和系统事件
    all_events = real_events + system_events

    # 如果没有真实事件，添加一个说明
    if not real_events:
        fallback_event = {
            "id": "event-fallback-001",
            "title": "市场数据获取中",
            "content": "正在获取最新的市场数据，请稍后刷新查看实时市场事件",
            "type": "system",
            "level": "info",
            "created_at": current_time,
            "expires_at": expire_time,
            "is_read": False
        }
        all_events.append(fallback_event)

    return all_events

# 默认事件数据
DEFAULT_EVENTS = get_default_events()

# 加载事件数据
def load_events(refresh_real_data=False):
    """
    加载事件数据

    Args:
        refresh_real_data: 是否刷新真实市场数据
    """
    try:
        if refresh_real_data or not os.path.exists(EVENTS_FILE):
            # 刷新真实数据或文件不存在时，重新生成默认数据
            fresh_events = get_default_events()
            save_events(fresh_events)
            return fresh_events
        else:
            # 加载现有数据
            with open(EVENTS_FILE, 'r', encoding='utf-8') as f:
                existing_events = json.load(f)

            # 检查是否需要添加新的真实市场事件
            real_events = generate_real_market_events()
            if real_events:
                # 检查是否有新的市场事件需要添加
                existing_ids = {event['id'] for event in existing_events}
                new_events = [event for event in real_events if event['id'] not in existing_ids]

                if new_events:
                    # 添加新事件到列表开头
                    updated_events = new_events + existing_events
                    save_events(updated_events)
                    return updated_events

            return existing_events
    except Exception as e:
        logger.error(f"加载事件数据失败: {str(e)}")
        # 出错时返回新生成的默认数据
        return get_default_events()

# 保存事件数据
def save_events(events):
    try:
        with open(EVENTS_FILE, 'w', encoding='utf-8') as f:
            json.dump(events, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存事件数据失败: {str(e)}")
        return False

# 初始化事件数据（启动时刷新真实数据）
IMPORTANT_EVENTS = load_events(refresh_real_data=True)

# 重要事件类型
EVENT_TYPES = [
    {"id": "market", "name": "市场事件", "description": "与市场价格、交易量相关的重要事件"},
    {"id": "system", "name": "系统事件", "description": "系统维护、升级等相关事件"},
    {"id": "feature", "name": "功能更新", "description": "新功能、新策略上线等事件"},
    {"id": "risk", "name": "风险事件", "description": "与账户风险、交易风险相关的事件"},
    {"id": "account", "name": "账户事件", "description": "账户状态、资金变动等相关事件"}
]

@notification_bp.route('/api/v1/notifications/important-events', methods=['GET'])
def get_important_events():
    """获取重要事件列表"""
    logger.info("获取重要事件列表请求")

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    page_size = request.args.get('page_size', 20, type=int)
    event_type = request.args.get('type', 'all')
    keyword = request.args.get('keyword', '')
    start_time = request.args.get('start_time', '')
    end_time = request.args.get('end_time', '')
    level = request.args.get('level', 'all')  # 添加级别筛选

    # 添加已读/未读状态筛选 - 支持两种参数名称
    read_status = request.args.get('is_read')
    if read_status is None:
        read_status = request.args.get('read')

    # 打印接收到的参数
    logger.info(f"接收到的查询参数: page={page}, page_size={page_size}, type={event_type}, level={level}, is_read={read_status}")

    # 打印所有请求参数，帮助调试
    logger.info(f"所有请求参数: {request.args}")

    # 打印请求头，帮助调试
    logger.info(f"请求头: {request.headers}")

    # 复制事件列表，避免修改原始数据
    events = IMPORTANT_EVENTS.copy()

    # 根据类型过滤
    if event_type != 'all':
        events = [e for e in events if e['type'] == event_type]

    # 根据级别过滤
    if level != 'all':
        events = [e for e in events if e['level'] == level]

    # 根据已读/未读状态过滤
    if read_status is not None:
        # 打印接收到的read_status值，用于调试
        logger.info(f"筛选已读状态: {read_status}, 类型: {type(read_status)}")

        # 处理不同类型的输入
        if isinstance(read_status, bool):
            is_read = read_status
        elif isinstance(read_status, str):
            is_read = read_status.lower() == 'true'
        else:
            is_read = bool(read_status)

        logger.info(f"转换后的is_read值: {is_read}")

        # 打印筛选前的事件数量
        logger.info(f"筛选前事件数量: {len(events)}")

        # 打印每个事件的is_read值，帮助调试
        for e in events:
            logger.info(f"事件ID: {e['id']}, 标题: {e['title']}, is_read: {e['is_read']}, 类型: {type(e['is_read'])}")

        # 筛选事件
        events = [e for e in events if e['is_read'] == is_read]

        # 打印筛选后的事件数量
        logger.info(f"筛选后事件数量: {len(events)}")

    # 根据关键词过滤
    if keyword:
        keyword = keyword.lower()
        events = [e for e in events if keyword in e['title'].lower() or keyword in e['content'].lower()]

    # 根据时间范围过滤
    if start_time:
        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        events = [e for e in events if datetime.fromisoformat(e['created_at'].replace('Z', '+00:00')) >= start_dt]

    if end_time:
        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        events = [e for e in events if datetime.fromisoformat(e['created_at'].replace('Z', '+00:00')) <= end_dt]

    # 计算分页
    total = len(events)
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total)
    paginated_events = events[start_idx:end_idx]

    # 返回结果
    return jsonify({
        "items": paginated_events,
        "total": total,
        "page": page,
        "page_size": page_size
    })

@notification_bp.route('/api/v1/notifications/important-events', methods=['POST'])
def create_important_event():
    """创建重要事件"""
    logger.info("创建重要事件请求")

    # 获取请求数据
    data = request.json
    if not data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Invalid request data"}), 400

    # 生成新事件ID
    event_id = f"event-{str(uuid.uuid4())[:8]}"

    # 创建新事件
    new_event = {
        "id": event_id,
        "title": data.get('title', ''),
        "content": data.get('content', ''),
        "type": data.get('type', 'system'),
        "level": data.get('level', 'info'),
        "created_at": datetime.now().isoformat() + 'Z',
        "expires_at": data.get('expires_at', (datetime.now() + timedelta(days=7)).isoformat() + 'Z'),
        "is_read": False
    }

    # 添加到事件列表
    IMPORTANT_EVENTS.insert(0, new_event)

    # 保存到文件
    save_events(IMPORTANT_EVENTS)

    # 返回创建结果
    return jsonify({
        "success": True,
        "message": "重要事件已创建",
        "data": new_event
    })

@notification_bp.route('/api/v1/notifications/important-event-types', methods=['GET'])
def get_important_event_types():
    """获取重要事件类型列表"""
    logger.info("获取重要事件类型列表请求")

    # 返回事件类型列表
    return jsonify({
        "success": True,
        "data": EVENT_TYPES
    })

@notification_bp.route('/api/v1/notifications/important-events/test', methods=['POST'])
def test_important_event():
    """测试重要事件提醒"""
    logger.info("测试重要事件提醒请求")

    # 获取请求数据
    data = request.json
    if not data or 'event_type' not in data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Missing event_type parameter"}), 400

    # 获取事件类型
    event_type = data['event_type']

    # 查找对应的事件类型
    event_type_info = next((t for t in EVENT_TYPES if t['id'] == event_type), None)
    if not event_type_info:
        return jsonify({"detail": f"Event type '{event_type}' not found"}), 404

    # 返回测试结果
    return jsonify({
        "success": True,
        "message": f"已发送测试事件: {event_type_info['name']}",
        "data": {
            "event_type": event_type,
            "event_name": event_type_info['name'],
            "test_time": datetime.now().isoformat() + 'Z'
        }
    })

@notification_bp.route('/api/v1/notifications/important-events/action', methods=['POST'])
def execute_event_action():
    """执行事件关联操作"""
    logger.info("执行事件关联操作请求")

    # 获取请求数据
    data = request.json
    if not data or 'event_id' not in data or 'action' not in data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Missing required parameters"}), 400

    # 获取事件ID和操作
    event_id = data['event_id']
    action = data['action']

    # 查找对应的事件
    event = next((e for e in IMPORTANT_EVENTS if e['id'] == event_id), None)
    if not event:
        return jsonify({"detail": f"Event '{event_id}' not found"}), 404

    # 执行操作
    result = {
        "success": True,
        "message": f"已执行操作: {action}",
        "data": {
            "event_id": event_id,
            "action": action,
            "action_time": datetime.now().isoformat() + 'Z'
        }
    }

    # 返回操作结果
    return jsonify(result)

@notification_bp.route('/api/v1/notifications/important-events/<string:event_id>/read', methods=['PUT'])
def mark_event_as_read(event_id):
    """标记事件为已读"""
    logger.info(f"标记事件为已读请求: {event_id}")

    # 查找对应的事件
    event = next((e for e in IMPORTANT_EVENTS if e['id'] == event_id), None)
    if not event:
        return jsonify({"detail": f"Event '{event_id}' not found"}), 404

    # 标记为已读
    event['is_read'] = True

    # 保存到文件
    save_events(IMPORTANT_EVENTS)

    # 返回操作结果
    return jsonify({
        "success": True,
        "message": f"事件 {event_id} 已标记为已读"
    })

@notification_bp.route('/api/v1/notifications/important-events/read-all', methods=['PUT'])
def mark_all_events_as_read():
    """标记所有事件为已读"""
    logger.info("标记所有事件为已读请求")

    # 标记所有事件为已读
    for event in IMPORTANT_EVENTS:
        event['is_read'] = True

    # 保存到文件
    save_events(IMPORTANT_EVENTS)

    # 返回操作结果
    return jsonify({
        "success": True,
        "message": "所有事件已标记为已读"
    })

@notification_bp.route('/api/v1/notifications/important-events/refresh', methods=['POST'])
def refresh_market_events():
    """刷新真实市场数据事件"""
    global IMPORTANT_EVENTS
    logger.info("刷新真实市场数据事件请求")

    try:
        # 重新加载事件数据，强制刷新真实数据
        IMPORTANT_EVENTS = load_events(refresh_real_data=True)

        # 获取当前市场数据用于返回
        market_data = get_real_market_data()

        return jsonify({
            "success": True,
            "message": "真实市场数据已刷新",
            "data": {
                "total_events": len(IMPORTANT_EVENTS),
                "market_data": market_data,
                "refresh_time": datetime.now().isoformat() + 'Z'
            }
        })
    except Exception as e:
        logger.error(f"刷新市场数据失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"刷新市场数据失败: {str(e)}"
        }), 500
