#!/usr/bin/env python3
"""
生成真实的缓存历史数据
通过调用API来产生真实的缓存活动，让系统记录历史数据
"""

import requests
import time
import json

def generate_cache_history():
    """生成真实的缓存历史数据"""
    
    print("🔄 开始生成真实缓存历史数据...")
    print("=" * 60)
    
    # 获取认证token
    print("\n1. 🔐 获取认证令牌...")
    try:
        auth_response = requests.post('http://localhost:8000/api/v1/auth/token', 
                                     json={'username': 'admin', 'password': 'admin123'})
        
        if auth_response.status_code != 200:
            print(f"❌ 认证失败: {auth_response.status_code}")
            return
        
        token = auth_response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print(f"✅ 认证成功")
        
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return
    
    # 生成真实的缓存活动
    print("\n2. 🔄 生成真实缓存活动...")
    
    # 定义要调用的API端点列表
    api_endpoints = [
        '/api/v1/performance/summary',
        '/api/v1/performance/analysis', 
        '/api/v1/performance/cache/stats',
        '/api/v1/performance/api/stats',
        '/api/v1/performance/api/endpoints'
    ]
    
    # 进行多轮API调用，产生真实的缓存活动
    for round_num in range(10):
        print(f"\n   第{round_num+1}轮缓存活动:")
        
        for endpoint in api_endpoints:
            try:
                response = requests.get(f'http://localhost:8000{endpoint}', headers=headers)
                if response.status_code == 200:
                    print(f"      ✅ {endpoint}: {response.status_code}")
                else:
                    print(f"      ❌ {endpoint}: {response.status_code}")
            except Exception as e:
                print(f"      ❌ {endpoint}: 异常 {e}")
        
        # 每轮之间等待一段时间，让缓存统计有时间更新
        if round_num < 9:  # 最后一轮不需要等待
            print(f"      ⏰ 等待30秒让缓存统计更新...")
            time.sleep(30)
    
    # 检查最终的缓存统计
    print("\n3. 📊 检查最终缓存统计...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
        if response.status_code == 200:
            stats = response.json()['data']
            print(f"   最终缓存统计:")
            print(f"      缓存大小: {stats['size']}")
            print(f"      最大容量: {stats['max_size']}")
            print(f"      命中次数: {stats['hits']}")
            print(f"      未命中次数: {stats['misses']}")
            print(f"      命中率: {stats['hit_rate']:.2%}")
            
            if stats['hits'] > 0:
                print(f"   ✅ 缓存系统正常工作，有真实的缓存活动")
            else:
                print(f"   ⚠️ 缓存系统可能没有活动")
        else:
            print(f"   ❌ 获取缓存统计失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 检查缓存历史数据
    print("\n4. 📈 检查缓存历史数据...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/metrics/cache?limit=20', headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                history_data = result['data']
                print(f"   ✅ 缓存历史数据API正常")
                print(f"   历史记录数量: {len(history_data)}")
                
                if history_data:
                    print(f"   📊 最新5条历史记录:")
                    for i, record in enumerate(history_data[-5:]):
                        print(f"      记录{i+1}:")
                        print(f"         时间: {record['timestamp'][:19]}")
                        print(f"         命中率: {record['hit_rate']:.2%}")
                        print(f"         缓存大小: {record['size']}")
                        print(f"         命中次数: {record['hits']}")
                        print(f"         未命中次数: {record['misses']}")
                    
                    print(f"\n   🎯 数据质量验证:")
                    print(f"      ✅ 有{len(history_data)}条真实历史记录")
                    print(f"      ✅ 数据时间跨度: {history_data[0]['timestamp'][:19]} 到 {history_data[-1]['timestamp'][:19]}")
                    
                    # 验证数据变化
                    hit_rates = [r['hit_rate'] for r in history_data]
                    sizes = [r['size'] for r in history_data]
                    
                    print(f"      ✅ 命中率范围: {min(hit_rates):.2%} - {max(hit_rates):.2%}")
                    print(f"      ✅ 缓存大小范围: {min(sizes)} - {max(sizes)}")
                    
                else:
                    print(f"   ⚠️ 暂无历史数据，需要等待系统记录")
            else:
                print(f"   ❌ API返回失败: {result.get('message', '未知错误')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print("\n" + "=" * 60)
    print("🔄 真实缓存历史数据生成完成")
    print("💡 现在可以刷新前端页面查看缓存性能趋势图表")
    print("💡 图表应该显示真实的缓存活动数据")

if __name__ == "__main__":
    generate_cache_history()
