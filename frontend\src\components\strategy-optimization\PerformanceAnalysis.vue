<template>
  <div class="performance-analysis-container">
    <div v-if="loading" class="loading-container">
      <div class="el-loading-spinner">
        <i class="el-icon-loading"></i>
        <p class="el-loading-text">加载中...</p>
      </div>
    </div>
    <div v-else>
      <div class="metrics-section">
        <h3>核心指标</h3>
        <div class="metrics-grid">
          <div v-for="(value, key) in metrics" :key="key" class="metric-card">
            <div class="metric-title">{{ getMetricTitle(key) }}</div>
            <div class="metric-value" :class="getMetricClass(key, value)">
              {{ formatMetricValue(key, value) }}
            </div>
          </div>
        </div>
      </div>

      <div class="chart-section">
        <h3>月度收益率</h3>
        <div class="chart-container">
          <canvas ref="monthlyReturnChart"></canvas>
        </div>
      </div>

      <div class="risk-metrics-section">
        <h3>风险指标</h3>
        <div class="metrics-grid">
          <div v-for="(value, key) in riskMetrics" :key="key" class="metric-card">
            <div class="metric-title">{{ getRiskMetricTitle(key) }}</div>
            <div class="metric-value" :class="getRiskMetricClass(key, value)">
              {{ formatRiskMetricValue(key, value) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, watch } from 'vue';
import { getStrategyPerformance } from '@/api';
import Chart from 'chart.js/auto';

export default {
  name: 'PerformanceAnalysis',
  props: {
    strategyId: {
      type: String,
      required: true
    },
    dateRange: {
      type: Object,
      default: () => ({ startDate: null, endDate: null })
    }
  },
  setup(props, { emit }) {
    const loading = ref(true);
    const performanceData = ref(null);
    const monthlyReturnChart = ref(null);
    const chartInstance = ref(null);

    const metrics = computed(() => {
      if (!performanceData.value) return {};
      return performanceData.value.metrics;
    });

    const riskMetrics = computed(() => {
      if (!performanceData.value) return {};
      return performanceData.value.risk_metrics;
    });

    const monthlyReturns = computed(() => {
      if (!performanceData.value) return [];
      return performanceData.value.monthly_returns;
    });

    const loadPerformanceData = async () => {
      loading.value = true;
      try {
        const response = await getStrategyPerformance(
          props.strategyId,
          props.dateRange.startDate,
          props.dateRange.endDate
        );

        // 处理响应数据格式
        let data = response;
        if (response && response.data) {
          data = response.data;
        }

        performanceData.value = data;
        console.log('绩效数据加载完成:', data);
        emit('data-loaded', data);

        // 数据加载完成后，延迟初始化图表
        setTimeout(() => {
          if (monthlyReturnChart.value && monthlyReturns.value.length > 0) {
            initChart();
          }
        }, 100);
      } catch (error) {
        console.error('加载策略绩效数据失败:', error);
      } finally {
        loading.value = false;
      }
    };

    const initChart = () => {
      console.log('初始化图表，月度收益数据:', monthlyReturns.value);

      if (!monthlyReturns.value || !monthlyReturns.value.length) {
        console.log('没有月度收益数据，跳过图表初始化');
        return;
      }

      if (!monthlyReturnChart.value) {
        console.log('图表容器不存在，跳过图表初始化');
        return;
      }

      const ctx = monthlyReturnChart.value.getContext('2d');

      // 销毁之前的图表实例
      if (chartInstance.value) {
        chartInstance.value.destroy();
      }

      const labels = monthlyReturns.value.map(item => item.month);
      const values = monthlyReturns.value.map(item => item.return);
      const backgroundColors = values.map(value => value >= 0 ? 'rgba(75, 192, 192, 0.4)' : 'rgba(255, 99, 132, 0.4)');
      const borderColors = values.map(value => value >= 0 ? 'rgba(75, 192, 192, 1)' : 'rgba(255, 99, 132, 1)');

      console.log('图表数据:', { labels, values });

      chartInstance.value = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: '月度收益率 (%)',
            data: values,
            backgroundColor: backgroundColors,
            borderColor: borderColors,
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: '月度收益率表现',
              color: '#e1e1ff'
            },
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: 'rgba(200, 200, 200, 0.2)'
              },
              ticks: {
                color: '#a9a9c8',
                callback: function(value) {
                  return value + '%';
                }
              }
            },
            x: {
              grid: {
                display: false
              },
              ticks: {
                color: '#a9a9c8'
              }
            }
          }
        }
      });

      console.log('图表初始化完成');
    };

    // 格式化指标名称
    const getMetricTitle = (key) => {
      const titles = {
        annualized_return: '年化收益率',
        sharpe_ratio: '夏普比率',
        max_drawdown: '最大回撤',
        win_rate: '胜率',
        avg_profit_loss_ratio: '盈亏比'
      };
      return titles[key] || key;
    };

    // 格式化风险指标名称
    const getRiskMetricTitle = (key) => {
      const titles = {
        volatility: '波动率',
        var: '风险价值(VaR)',
        beta: 'Beta值'
      };
      return titles[key] || key;
    };

    // 获取指标数值的样式类
    const getMetricClass = (key, value) => {
      if (key === 'annualized_return' || key === 'sharpe_ratio') {
        return value > 0 ? 'positive' : 'negative';
      } else if (key === 'max_drawdown') {
        return value < 20 ? 'positive' : 'negative';
      }
      return '';
    };

    // 获取风险指标的样式类
    const getRiskMetricClass = (key, value) => {
      if (key === 'volatility') {
        return value < 15 ? 'positive' : 'negative';
      } else if (key === 'var') {
        return value < 3 ? 'positive' : 'negative';
      }
      return '';
    };

    // 格式化指标值显示
    const formatMetricValue = (key, value) => {
      if (key === 'annualized_return' || key === 'max_drawdown' || key === 'win_rate') {
        return `${value}%`;
      }
      return value;
    };

    // 格式化风险指标值显示
    const formatRiskMetricValue = (key, value) => {
      if (key === 'volatility') {
        return `${value}%`;
      } else if (key === 'var') {
        return `${value}%`;
      }
      return value;
    };

    onMounted(() => {
      loadPerformanceData();
    });

    watch(() => props.strategyId, () => {
      loadPerformanceData();
    });

    watch(() => props.dateRange, () => {
      loadPerformanceData();
    }, { deep: true });

    watch(monthlyReturns, () => {
      setTimeout(() => {
        if (monthlyReturnChart.value) {
          initChart();
        }
      }, 100);
    });

    return {
      loading,
      metrics,
      riskMetrics,
      monthlyReturnChart,
      getMetricTitle,
      getRiskMetricTitle,
      getMetricClass,
      getRiskMetricClass,
      formatMetricValue,
      formatRiskMetricValue
    };
  }
};
</script>

<style scoped>
/* 量子科技美学风格 */
.performance-analysis-container {
  padding: 24px;
  background: rgba(6,21,46,0.8);
  border: 1px solid rgba(0,247,255,0.3);
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0,247,255,0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.performance-analysis-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00F7FF, transparent);
  box-shadow: 0 0 10px rgba(0,247,255,0.6);
}

.metrics-section, .risk-metrics-section, .chart-section {
  margin-bottom: 32px;
}

h3 {
  color: #00F7FF;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
  text-shadow: 0 0 12px rgba(0,247,255,0.6);
  border-bottom: 1px solid rgba(0,247,255,0.3);
  padding-bottom: 12px;
  position: relative;
}

h3::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 60px;
  height: 2px;
  background: #00F7FF;
  box-shadow: 0 0 8px rgba(0,247,255,0.8);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
}

.metric-card {
  background: rgba(6,21,46,0.6);
  border: 1px solid rgba(0,247,255,0.2);
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0,247,255,0.1);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0,247,255,0.5), transparent);
}

.metric-card:hover {
  border-color: rgba(0,247,255,0.5);
  box-shadow: 0 8px 32px rgba(0,247,255,0.2);
  transform: translateY(-2px);
}

.metric-title {
  font-size: 14px;
  color: rgba(255,255,255,0.7);
  margin-bottom: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #e1e1ff;
  text-shadow: 0 0 8px rgba(225,225,255,0.3);
}

.positive {
  color: #00FF94;
  text-shadow: 0 0 12px rgba(0,255,148,0.6);
}

.negative {
  color: #FF6B6B;
  text-shadow: 0 0 12px rgba(255,107,107,0.6);
}

.chart-container {
  height: 350px;
  background: rgba(6,21,46,0.6);
  border: 1px solid rgba(0,247,255,0.2);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0,247,255,0.1);
  backdrop-filter: blur(5px);
  position: relative;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0,247,255,0.5), transparent);
}

.loading-container {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.loading-container .el-loading-spinner {
  color: #00F7FF;
}

.loading-container .el-loading-spinner i {
  font-size: 48px;
  color: #00F7FF;
  text-shadow: 0 0 20px rgba(0,247,255,0.6);
  animation: quantum-spin 2s linear infinite;
}

.loading-container .el-loading-text {
  color: rgba(255,255,255,0.8);
  font-size: 16px;
  margin-top: 16px;
  text-shadow: 0 0 8px rgba(255,255,255,0.3);
}

@keyframes quantum-spin {
  0% {
    transform: rotate(0deg);
    text-shadow: 0 0 20px rgba(0,247,255,0.6);
  }
  50% {
    transform: rotate(180deg);
    text-shadow: 0 0 30px rgba(0,247,255,0.8);
  }
  100% {
    transform: rotate(360deg);
    text-shadow: 0 0 20px rgba(0,247,255,0.6);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .performance-analysis-container {
    padding: 16px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .metric-card {
    padding: 16px;
  }

  .metric-value {
    font-size: 20px;
  }

  .chart-container {
    height: 280px;
    padding: 16px;
  }

  h3 {
    font-size: 18px;
  }
}

/* 滚动条样式 */
.performance-analysis-container::-webkit-scrollbar {
  width: 6px;
}

.performance-analysis-container::-webkit-scrollbar-track {
  background: rgba(6,21,46,0.3);
}

.performance-analysis-container::-webkit-scrollbar-thumb {
  background: rgba(0,247,255,0.3);
  border-radius: 3px;
}

.performance-analysis-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0,247,255,0.5);
}
</style>