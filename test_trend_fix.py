#!/usr/bin/env python3
"""
测试API趋势图修复
验证前端逻辑错误是否已修复
"""

import requests
import json

def test_api_trend_fix():
    """测试API趋势数据修复"""
    
    print("🔧 测试API趋势图修复...")
    
    # 1. 登录获取token
    print("\n1. 登录获取认证token...")
    login_url = "http://localhost:8000/api/v1/auth/token"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. 测试API趋势数据格式
    print("\n2. 测试API趋势数据格式...")
    trend_url = "http://localhost:8000/api/v1/performance/metrics/api?limit=10"
    
    try:
        response = requests.get(trend_url, headers=headers)
        if response.status_code == 200:
            trend_data = response.json()
            print(f"✅ API请求成功")
            print(f"   - success字段: {trend_data.get('success')}")
            print(f"   - message字段: {trend_data.get('message')}")
            print(f"   - data字段存在: {'data' in trend_data}")
            
            if trend_data.get("success") and trend_data.get("data"):
                data_points = len(trend_data["data"])
                print(f"   - 数据点数量: {data_points}")
                
                if data_points > 0:
                    sample = trend_data["data"][0]
                    print(f"   - 示例数据点: {sample}")
                    
                    # 检查必需字段
                    required_fields = ['timestamp', 'avg_time', 'max_time']
                    missing_fields = [f for f in required_fields if f not in sample]
                    
                    if missing_fields:
                        print(f"   ❌ 缺少必需字段: {missing_fields}")
                        return False
                    else:
                        print(f"   ✅ 数据格式正确，包含所有必需字段")
                        return True
            else:
                print(f"   ❌ API响应格式错误")
                print(f"   完整响应: {trend_data}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 API趋势图修复验证测试")
    print("=" * 50)
    
    success = test_api_trend_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 修复验证成功！")
        print("✅ API趋势数据格式正确")
        print("✅ 前端应该能正常显示趋势图")
        print("\n📋 修复内容:")
        print("  - 修复了前端条件判断逻辑")
        print("  - 增强了错误处理和调试信息")
        print("  - 确保数据格式完全符合前端期望")
    else:
        print("❌ 修复验证失败！")
        print("需要进一步检查问题")
    print("=" * 50)

if __name__ == "__main__":
    main()
