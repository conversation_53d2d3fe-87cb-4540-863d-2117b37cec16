#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据同步相关的模型定义
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime

class DataGap(BaseModel):
    """数据缺口模型"""
    start: datetime
    end: datetime
    missing_records: int
    gap_duration_minutes: float
    severity: str

class RepairDataGapsRequest(BaseModel):
    """数据缺口修复请求模型"""
    source_id: int
    exchange: str
    symbol: str
    timeframe: str
    gaps: List[Dict[str, Any]]
    repair_method: str = "auto"  # auto, resync, interpolate, mark_missing

class RepairDataGapsResponse(BaseModel):
    """数据缺口修复响应模型"""
    success: bool
    message: str
    repaired_gaps: Optional[List[Dict[str, Any]]] = None
    failed_gaps: Optional[List[Dict[str, Any]]] = None
    total_repaired: int = 0
    total_failed: int = 0
    repair_method_used: Optional[str] = None
