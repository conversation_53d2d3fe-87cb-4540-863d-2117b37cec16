#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask, jsonify, request
import logging

# 设置日志记录
logging.basicConfig(level=logging.DEBUG,
                  format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                  handlers=[
                      logging.StreamHandler(),
                      logging.FileHandler('test_notification_api.log', encoding='utf-8')
                  ])
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)

# 通知API路由
@app.route('/', methods=['GET'])
def index():
    """首页"""
    logger.info("首页请求")
    return jsonify({"message": "通知API服务正在运行"})

@app.route('/api/v1/notifications/recent', methods=['GET'])
def get_recent_notifications():
    """获取最近的通知"""
    logger.info("获取最近的通知请求")

    # 返回最近的通知
    # 注意：这里返回一个空数组，而不是对象，以符合前端期望的格式
    return jsonify([])

@app.route('/api/v1/alert-rules', methods=['GET'])
def get_alert_rules():
    """获取告警规则列表"""
    logger.info("获取告警规则列表请求")

    # 返回告警规则列表
    return jsonify([])

# 主函数
if __name__ == '__main__':
    logger.info("启动测试通知API服务，端口: 8002")
    app.run(host='0.0.0.0', port=8002, debug=True)
