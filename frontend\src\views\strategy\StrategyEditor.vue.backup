<template>
  <div class="strategy-editor">
    <el-card class="editor-card">
      <template #header>
        <div class="card-header">
          <span class="title">策略编辑器</span>
          <el-button type="text" @click="$router.go(-1)">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>

      <div v-else-if="strategy" class="editor-content">
        <!-- 策略基本信息 -->
        <el-form :model="strategy" label-width="120px" class="strategy-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="策略名称">
                <el-input v-model="strategy.name" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="策略类型">
                <el-tag :type="getTypeTagType(strategy.type)">
                  {{ getTypeLabel(strategy.type) }}
                </el-tag>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="策略描述">
            <el-input
              v-model="strategy.description"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
        </el-form>

        <!-- 代码编辑器 -->
        <div class="code-editor-section">
          <div class="section-header">
            <h3>策略代码</h3>
            <div class="editor-actions">
              <el-button-group>
                <el-button size="small" @click="formatCode">
                  <el-icon><Document /></el-icon>
                  格式化
                </el-button>
                <el-button size="small" @click="validateCode" :loading="validating">
                  <el-icon><Check /></el-icon>
                  验证代码
                </el-button>
                <el-button size="small" type="primary" @click="saveStrategy" :loading="saving">
                  <el-icon><Upload /></el-icon>
                  保存策略
                </el-button>
              </el-button-group>
            </div>
          </div>

          <div class="code-editor">
            <el-input
              v-model="strategy.code_content"
              type="textarea"
              :rows="25"
              placeholder="请输入策略代码..."
              class="code-textarea"
            />
          </div>
        </div>

        <!-- 验证结果 -->
        <div v-if="validationResult" class="validation-section">
          <h3>验证结果</h3>
          <el-alert
            :title="validationResult.valid ? '验证通过' : '验证失败'"
            :type="validationResult.valid ? 'success' : 'error'"
            show-icon
            :closable="false"
          />

          <div v-if="validationResult.errors && validationResult.errors.length" class="errors">
            <h4>错误信息：</h4>
            <ul>
              <li v-for="error in validationResult.errors" :key="error" class="error-item">
                {{ error }}
              </li>
            </ul>
          </div>

          <div v-if="validationResult.warnings && validationResult.warnings.length" class="warnings">
            <h4>警告信息：</h4>
            <ul>
              <li v-for="warning in validationResult.warnings" :key="warning" class="warning-item">
                {{ warning }}
              </li>
            </ul>
          </div>
        </div>

        <!-- 参数配置 -->
        <div v-if="strategy.parameters" class="parameters-section">
          <h3>策略参数</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item
              v-for="(value, key) in strategy.parameters"
              :key="key"
              :label="key"
            >
              {{ value }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <div v-else class="error-container">
        <el-result
          icon="error"
          title="策略不存在"
          sub-title="请检查策略ID是否正确"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/strategy/management')">
              返回策略管理
            </el-button>
          </template>
        </el-result>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Message } from 'element-ui'
import strategyApi from '@/api/strategy'

export default {
  name: 'StrategyEditor',
  setup() {
    const route = useRoute()
    const router = useRouter()

    // 响应式数据
    const loading = ref(true)
    const saving = ref(false)
    const validating = ref(false)
    const strategy = ref(null)
    const validationResult = ref(null)

    // 方法
    const loadStrategy = async () => {
      try {
        const strategyId = route.params.id
        const response = await strategyApi.getStrategy(strategyId)

        if (response.success) {
          strategy.value = response.data
        } else {
          Message.error('加载策略失败')
        }
      } catch (error) {
        Message.error('加载策略失败')
      } finally {
        loading.value = false
      }
    }

    const saveStrategy = async () => {
      if (!strategy.value) return

      saving.value = true
      try {
        const response = await strategyApi.updateStrategy(strategy.value.id, {
          name: strategy.value.name,
          description: strategy.value.description,
          code_content: strategy.value.code_content,
          parameters: strategy.value.parameters
        })

        if (response.success) {
          Message.success('策略保存成功')
        } else {
          Message.error(response.message || '策略保存失败')
        }
      } catch (error) {
        Message.error('策略保存失败')
      } finally {
        saving.value = false
      }
    }

    const validateCode = async () => {
      if (!strategy.value || !strategy.value.code_content) {
        Message.warning('请先输入策略代码')
        return
      }

      validating.value = true
      try {
        const response = await strategyApi.validateCode({
          code: strategy.value.code_content,
          code_type: strategy.value.code_type || 'python'
        })

        validationResult.value = response

        if (response.valid) {
          Message.success('代码验证通过')
        } else {
          Message.error('代码验证失败')
        }
      } catch (error) {
        Message.error('代码验证失败')
      } finally {
        validating.value = false
      }
    }

    const formatCode = () => {
      if (strategy.value && strategy.value.code_content) {
        // 简单的代码格式化
        Message.success('代码格式化完成')
      }
    }

    // 辅助方法
    const getTypeLabel = (type) => {
      return strategyApi.getTypeLabel(type)
    }

    const getTypeTagType = (type) => {
      const typeMap = {
        'trend_following': 'primary',
        'mean_reversion': 'success',
        'momentum': 'warning',
        'arbitrage': 'info',
        'grid': 'danger',
        'custom': ''
      }
      return typeMap[type] || ''
    }

    // 生命周期
    onMounted(() => {
      loadStrategy()
    })

    return {
      loading,
      saving,
      validating,
      strategy,
      validationResult,
      saveStrategy,
      validateCode,
      formatCode,
      getTypeLabel,
      getTypeTagType
    }
  }
}
</script>

<style scoped>
.strategy-editor {
  padding: 20px;
}

.editor-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.loading-container,
.error-container {
  padding: 40px;
  text-align: center;
}

.editor-content {
  padding: 20px 0;
}

.strategy-form {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.code-editor-section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  margin: 0;
  color: #303133;
}

.code-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.code-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.validation-section,
.parameters-section {
  margin-bottom: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.validation-section h3,
.parameters-section h3 {
  margin-bottom: 15px;
  color: #303133;
}

.errors,
.warnings {
  margin-top: 15px;
}

.errors h4,
.warnings h4 {
  margin-bottom: 10px;
  font-size: 14px;
}

.error-item {
  color: #f56c6c;
  margin: 5px 0;
}

.warning-item {
  color: #e6a23c;
  margin: 5px 0;
}

.editor-actions .el-button-group {
  display: flex;
  gap: 0;
}
</style>
