#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import json
from datetime import datetime

def fix_bollinger_reversion():
    """修复布林带回归策略模板"""
    
    # 连接到API服务使用的数据库
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    # 检查是否已存在布林带回归策略
    cursor.execute('SELECT COUNT(*) FROM strategy_templates WHERE category = ?', ('bollinger_reversion',))
    count = cursor.fetchone()[0]
    print(f'现有布林带回归策略数量: {count}')

    if count == 0:
        print('添加布林带回归策略模板...')
        
        # 布林带回归策略参数模式
        parameter_schema = {
            'parameters': [
                {
                    'name': 'period',
                    'type': 'integer',
                    'default': 20,
                    'min': 10,
                    'max': 50,
                    'description': '布林带周期',
                    'required': True
                },
                {
                    'name': 'std_dev',
                    'type': 'float',
                    'default': 2.0,
                    'min': 1.0,
                    'max': 3.0,
                    'description': '标准差倍数',
                    'required': True
                },
                {
                    'name': 'reversion_threshold',
                    'type': 'float',
                    'default': 0.8,
                    'min': 0.5,
                    'max': 1.0,
                    'description': '回归阈值',
                    'required': True
                }
            ]
        }
        
        # 默认参数
        default_parameters = {
            'period': 20,
            'std_dev': 2.0,
            'reversion_threshold': 0.8
        }
        
        # 布林带回归策略代码模板
        code_template = '''
def initialize(context):
    context.period = {{ period | default(20) }}
    context.std_dev = {{ std_dev | default(2.0) }}
    context.reversion_threshold = {{ reversion_threshold | default(0.8) }}
    context.position = 0

def handle_bar(context, data):
    import numpy as np
    
    # 获取历史价格数据
    prices = data.get_history(context.period)
    if len(prices) < context.period:
        return {'action': 'HOLD'}
    
    # 计算布林带
    close_prices = [p['close'] for p in prices]
    sma = np.mean(close_prices)
    std = np.std(close_prices)
    
    upper_band = sma + (context.std_dev * std)
    lower_band = sma - (context.std_dev * std)
    
    current_price = data['close']
    
    # 计算价格在布林带中的相对位置
    if upper_band != lower_band:
        position_ratio = (current_price - lower_band) / (upper_band - lower_band)
    else:
        position_ratio = 0.5
    
    # 均值回归交易逻辑
    if position_ratio >= context.reversion_threshold and context.position <= 0:
        # 价格接近上轨，预期回归，卖出
        return {
            'action': 'SELL',
            'quantity': 1,
            'reason': f'价格接近上轨({position_ratio:.2f}), 预期回归'
        }
    elif position_ratio <= (1 - context.reversion_threshold) and context.position >= 0:
        # 价格接近下轨，预期回归，买入
        return {
            'action': 'BUY',
            'quantity': 1,
            'reason': f'价格接近下轨({position_ratio:.2f}), 预期回归'
        }
    elif 0.4 <= position_ratio <= 0.6:
        # 价格回到中轨附近，平仓
        if context.position != 0:
            return {
                'action': 'CLOSE',
                'reason': f'价格回归中轨({position_ratio:.2f}), 平仓获利'
            }
    
    return {'action': 'HOLD'}
        '''
        
        # 插入布林带回归策略模板
        cursor.execute('''
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            '布林带回归策略',
            'mean_reversion',
            'bollinger_reversion',
            '基于布林带的均值回归策略，当价格偏离均值时进行反向交易，等待价格回归',
            code_template.strip(),
            json.dumps(parameter_schema),
            json.dumps(default_parameters),
            1,
            datetime.now().isoformat()
        ))
        
        conn.commit()
        print('布林带回归策略模板添加成功!')
    else:
        print('布林带回归策略模板已存在')

    # 验证结果
    cursor.execute('SELECT name, category FROM strategy_templates WHERE category = ?', ('bollinger_reversion',))
    results = cursor.fetchall()
    print('布林带回归策略:')
    for row in results:
        print(f'  {row[0]} - {row[1]}')

    conn.close()

if __name__ == "__main__":
    fix_bollinger_reversion()
