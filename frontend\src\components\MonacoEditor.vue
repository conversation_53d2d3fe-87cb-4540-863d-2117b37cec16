<template>
  <div class="monaco-editor-container">
    <div ref="editorContainer" class="editor-container"></div>
  </div>
</template>

<script>
// 使用CDN方式加载Monaco Editor，避免Webpack编译问题

export default {
  name: 'MonacoEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'python'
    },
    theme: {
      type: String,
      default: 'vs-dark'
    },
    options: {
      type: Object,
      default: () => ({})
    },
    height: {
      type: [String, Number],
      default: 400
    }
  },
  data() {
    return {
      editor: null,
      isInitialized: false
    }
  },
  mounted() {
    this.loadMonacoEditor()
  },
  beforeDestroy() {
    if (this.editor) {
      this.editor.dispose()
    }
  },
  watch: {
    value(newValue) {
      if (this.editor && newValue !== this.editor.getValue()) {
        this.editor.setValue(newValue || '')
      }
    },
    language(newLanguage) {
      if (this.editor) {
        monaco.editor.setModelLanguage(this.editor.getModel(), newLanguage)
      }
    },
    theme(newTheme) {
      if (this.editor) {
        monaco.editor.setTheme(newTheme)
      }
    }
  },
  methods: {
    loadMonacoEditor() {
      // 检查Monaco Editor是否已加载
      if (window.monaco) {
        this.initMonaco()
        return
      }

      // 动态加载Monaco Editor CDN
      const script = document.createElement('script')
      script.src = 'https://cdn.jsdelivr.net/npm/monaco-editor@0.34.1/min/vs/loader.js'
      script.onload = () => {
        window.require.config({
          paths: {
            'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.34.1/min/vs'
          }
        })
        window.require(['vs/editor/editor.main'], () => {
          this.initMonaco()
        })
      }
      document.head.appendChild(script)
    },

    initMonaco() {
      // 注册Pine Script语言
      this.registerPineScriptLanguage()

      // 设置编辑器容器高度
      this.$refs.editorContainer.style.height = typeof this.height === 'number'
        ? `${this.height}px`
        : this.height

      // 默认编辑器选项
      const defaultOptions = {
        value: this.value || '',
        language: this.language,
        theme: this.theme,
        automaticLayout: true,
        fontSize: 14,
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        lineNumbers: 'on',
        roundedSelection: false,
        scrollBeyondLastLine: false,
        readOnly: false,
        minimap: { enabled: true },
        folding: true,
        selectOnLineNumbers: true,
        wordWrap: 'on',
        contextmenu: true,
        mouseWheelZoom: true,
        formatOnPaste: true,
        formatOnType: true,
        autoIndent: 'advanced',
        bracketMatching: 'always',
        suggestOnTriggerCharacters: true,
        acceptSuggestionOnEnter: 'on',
        tabCompletion: 'on',
        wordBasedSuggestions: true,
        parameterHints: { enabled: true }
      }

      // 合并用户选项
      const editorOptions = { ...defaultOptions, ...this.options }

      // 创建编辑器实例
      this.editor = window.monaco.editor.create(this.$refs.editorContainer, editorOptions)

      // 监听内容变化
      this.editor.onDidChangeModelContent(() => {
        const value = this.editor.getValue()
        this.$emit('input', value)
        this.$emit('change', value)
      })

      // 监听焦点事件
      this.editor.onDidFocusEditorWidget(() => {
        this.$emit('focus')
      })

      this.editor.onDidBlurEditorWidget(() => {
        this.$emit('blur')
      })

      this.isInitialized = true
      this.$emit('ready', this.editor)
    },

    registerPineScriptLanguage() {
      // 注册Pine Script语言
      window.monaco.languages.register({ id: 'pinescript' })

      // 设置语法高亮
      window.monaco.languages.setMonarchTokensProvider('pinescript', {
        tokenizer: {
          root: [
            // 注释
            [/\/\/.*$/, 'comment'],
            [/\/\*/, 'comment', '@comment'],

            // 字符串
            [/"([^"\\]|\\.)*$/, 'string.invalid'],
            [/"/, 'string', '@string'],
            [/'([^'\\]|\\.)*$/, 'string.invalid'],
            [/'/, 'string', '@string_single'],

            // 数字
            [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
            [/\d+/, 'number'],

            // Pine Script关键字
            [/\b(strategy|indicator|study|library|if|else|for|while|switch|var|varip|input|plot|plotshape|plotchar|bgcolor|fill|hline|line|label|table|box|polyline|import|export|method|type|true|false|na|close|open|high|low|volume|time|bar_index|barstate|session|syminfo|timeframe|ticker|math|array|matrix|map|request|ta|str|color|int|float|bool|string|series|simple|const)\b/, 'keyword'],

            // Pine Script内置函数
            [/\b(sma|ema|rsi|macd|stoch|bb|atr|adx|cci|mfi|obv|vwap|pivot|crossover|crossunder|rising|falling|highest|lowest|sum|avg|min|max|abs|round|floor|ceil|log|exp|sqrt|pow|sin|cos|tan|asin|acos|atan|tostring|tonumber|na|nz|fixnan|sign|change|mom|roc|dev|stdev|variance|correlation|percentile_linear_interpolation|percentile_nearest_rank|array_new|array_size|array_get|array_set|array_push|array_pop|array_shift|array_unshift|array_remove|array_insert|array_slice|array_reverse|array_sort|array_includes|array_indexof|array_join|array_copy|array_concat|array_fill|array_from|matrix_new|matrix_rows|matrix_columns|matrix_get|matrix_set|matrix_fill|matrix_copy|matrix_submatrix|matrix_add_row|matrix_add_col|matrix_remove_row|matrix_remove_col|matrix_swap_rows|matrix_swap_columns|matrix_reverse|matrix_transpose|matrix_pinv|matrix_inv|matrix_mult|matrix_det|matrix_rank|matrix_trace|matrix_eigenvalues|matrix_eigenvectors|map_new|map_size|map_get|map_set|map_remove|map_clear|map_contains|map_keys|map_values|map_copy|request_security|request_security_lower_tf|request_splits|request_dividends|request_earnings|request_quandl|request_financial|request_economic|alert|alertcondition|barcolor|bgcolor|fill|hline|line_new|line_set_x1|line_set_y1|line_set_x2|line_set_y2|line_set_color|line_set_style|line_set_width|line_set_extend|line_get_x1|line_get_y1|line_get_x2|line_get_y2|line_delete|label_new|label_set_x|label_set_y|label_set_text|label_set_color|label_set_textcolor|label_set_size|label_set_style|label_set_tooltip|label_get_x|label_get_y|label_get_text|label_delete|table_new|table_set_position|table_set_bgcolor|table_set_frame_color|table_set_frame_width|table_set_border_color|table_set_border_width|table_cell|table_clear|table_delete|box_new|box_set_left|box_set_top|box_set_right|box_set_bottom|box_set_bgcolor|box_set_border_color|box_set_border_style|box_set_border_width|box_set_text|box_set_text_color|box_set_text_size|box_get_left|box_get_top|box_get_right|box_get_bottom|box_delete|polyline_new|polyline_delete|polyline_get|polyline_copy|input_bool|input_int|input_float|input_string|input_symbol|input_timeframe|input_session|input_source|input_color|input_text_area|input_table_id|plot|plotshape|plotchar|plotcandle|plotbar|plotarrow|fill|bgcolor|barcolor|strategy_entry|strategy_exit|strategy_close|strategy_close_all|strategy_cancel|strategy_cancel_all)\b/, 'support.function'],

            // 操作符
            [/[=><!~?:&|+\-*\/\^%]+/, 'operator'],

            // 分隔符
            [/[;,.]/, 'delimiter'],
            [/[()[\]{}]/, 'delimiter.bracket'],

            // 标识符
            [/[a-zA-Z_]\w*/, 'identifier']
          ],

          comment: [
            [/[^\/*]+/, 'comment'],
            [/\*\//, 'comment', '@pop'],
            [/[\/*]/, 'comment']
          ],

          string: [
            [/[^\\"]+/, 'string'],
            [/\\./, 'string.escape'],
            [/"/, 'string', '@pop']
          ],

          string_single: [
            [/[^\\']+/, 'string'],
            [/\\./, 'string.escape'],
            [/'/, 'string', '@pop']
          ]
        }
      })

      // 设置自动补全
      window.monaco.languages.registerCompletionItemProvider('pinescript', {
        provideCompletionItems: (model, position) => {
          const suggestions = [
            // Pine Script关键字补全
            {
              label: 'strategy',
              kind: window.monaco.languages.CompletionItemKind.Keyword,
              insertText: 'strategy("${1:My Strategy}", overlay=${2:true})',
              insertTextRules: window.monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Define a strategy'
            },
            {
              label: 'if',
              kind: window.monaco.languages.CompletionItemKind.Keyword,
              insertText: 'if ${1:condition}\n    ${2:// code}\n',
              insertTextRules: window.monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Conditional statement'
            },
            {
              label: 'ta.sma',
              kind: window.monaco.languages.CompletionItemKind.Function,
              insertText: 'ta.sma(${1:source}, ${2:length})',
              insertTextRules: window.monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Simple Moving Average'
            },
            {
              label: 'ta.ema',
              kind: window.monaco.languages.CompletionItemKind.Function,
              insertText: 'ta.ema(${1:source}, ${2:length})',
              insertTextRules: window.monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Exponential Moving Average'
            },
            {
              label: 'ta.rsi',
              kind: window.monaco.languages.CompletionItemKind.Function,
              insertText: 'ta.rsi(${1:source}, ${2:length})',
              insertTextRules: window.monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Relative Strength Index'
            },
            {
              label: 'strategy.entry',
              kind: window.monaco.languages.CompletionItemKind.Function,
              insertText: 'strategy.entry("${1:id}", ${2:strategy.long}, ${3:qty})',
              insertTextRules: window.monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Enter a position'
            },
            {
              label: 'strategy.close',
              kind: window.monaco.languages.CompletionItemKind.Function,
              insertText: 'strategy.close("${1:id}")',
              insertTextRules: window.monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Close a position'
            }
          ]

          return { suggestions }
        }
      })
    },

    // 公共方法
    getValue() {
      return this.editor ? this.editor.getValue() : ''
    },

    setValue(value) {
      if (this.editor) {
        this.editor.setValue(value || '')
      }
    },

    focus() {
      if (this.editor) {
        this.editor.focus()
      }
    },

    getEditor() {
      return this.editor
    },

    formatCode() {
      if (this.editor) {
        this.editor.getAction('editor.action.formatDocument').run()
      }
    }
  }
}
</script>

<style scoped>
.monaco-editor-container {
  width: 100%;
  height: 100%;
}

.editor-container {
  width: 100%;
  height: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}
</style>
