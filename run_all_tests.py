#!/usr/bin/env python3
"""
策略管理系统自动化测试运行器
运行所有测试并生成详细报告
"""

import unittest
import sys
import os
import time
import json
from datetime import datetime
from io import StringIO

# 添加项目根目录到路径
sys.path.append(os.path.dirname(__file__))

class TestResult:
    """测试结果类"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.skipped_tests = 0
        self.errors = []
        self.failures = []
        self.skipped = []
        self.performance_data = {}

class ColoredTextTestResult(unittest.TextTestResult):
    """带颜色的测试结果输出"""
    
    def __init__(self, stream, descriptions, verbosity, test_result_obj):
        super().__init__(stream, descriptions, verbosity)
        self.test_result_obj = test_result_obj
        
        # ANSI颜色代码
        self.colors = {
            'green': '\033[92m',
            'red': '\033[91m',
            'yellow': '\033[93m',
            'blue': '\033[94m',
            'purple': '\033[95m',
            'cyan': '\033[96m',
            'white': '\033[97m',
            'bold': '\033[1m',
            'end': '\033[0m'
        }
    
    def colorize(self, text, color):
        """给文本添加颜色"""
        if sys.platform == 'win32':
            return text  # Windows可能不支持ANSI颜色
        return f"{self.colors.get(color, '')}{text}{self.colors['end']}"
    
    def startTest(self, test):
        super().startTest(test)
        self.test_result_obj.total_tests += 1
    
    def addSuccess(self, test):
        super().addSuccess(test)
        self.test_result_obj.passed_tests += 1
        print(f"  {self.colorize('✓', 'green')} {test._testMethodName}")
    
    def addError(self, test, err):
        super().addError(test, err)
        self.test_result_obj.failed_tests += 1
        self.test_result_obj.errors.append((test, err))
        print(f"  {self.colorize('✗', 'red')} {test._testMethodName} - ERROR")
    
    def addFailure(self, test, err):
        super().addFailure(test, err)
        self.test_result_obj.failed_tests += 1
        self.test_result_obj.failures.append((test, err))
        print(f"  {self.colorize('✗', 'red')} {test._testMethodName} - FAIL")
    
    def addSkip(self, test, reason):
        super().addSkip(test, reason)
        self.test_result_obj.skipped_tests += 1
        self.test_result_obj.skipped.append((test, reason))
        print(f"  {self.colorize('⚠', 'yellow')} {test._testMethodName} - SKIPPED")

def run_test_suite(test_module_path, test_result_obj, suite_name):
    """运行测试套件"""
    print(f"\n{'-' * 60}")
    print(f"🧪 运行 {suite_name}")
    print(f"{'-' * 60}")
    
    try:
        # 动态导入测试模块
        spec = unittest.util.spec_from_file_location("test_module", test_module_path)
        test_module = unittest.util.module_from_spec(spec)
        spec.loader.exec_module(test_module)
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(test_module)
        
        # 运行测试
        stream = StringIO()
        runner = unittest.TextTestRunner(
            stream=stream,
            verbosity=2,
            resultclass=lambda stream, descriptions, verbosity: 
                ColoredTextTestResult(stream, descriptions, verbosity, test_result_obj)
        )
        
        suite_start_time = time.time()
        result = runner.run(suite)
        suite_end_time = time.time()
        
        suite_duration = suite_end_time - suite_start_time
        print(f"\n📊 {suite_name} 完成 - 耗时: {suite_duration:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 运行 {suite_name} 时出错: {str(e)}")
        return False

def generate_test_report(test_result_obj):
    """生成测试报告"""
    duration = test_result_obj.end_time - test_result_obj.start_time
    
    print(f"\n{'=' * 80}")
    print(f"📋 测试报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'=' * 80}")
    
    print(f"⏱️  总耗时: {duration:.2f}秒")
    print(f"📊 总测试数: {test_result_obj.total_tests}")
    print(f"✅ 通过: {test_result_obj.passed_tests}")
    print(f"❌ 失败: {test_result_obj.failed_tests}")
    print(f"⚠️  跳过: {test_result_obj.skipped_tests}")
    
    # 计算成功率
    if test_result_obj.total_tests > 0:
        success_rate = (test_result_obj.passed_tests / test_result_obj.total_tests) * 100
        print(f"📈 成功率: {success_rate:.1f}%")
    
    # 显示失败的测试
    if test_result_obj.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in test_result_obj.failures:
            print(f"  - {test._testMethodName}")
    
    # 显示错误的测试
    if test_result_obj.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in test_result_obj.errors:
            print(f"  - {test._testMethodName}")
    
    # 显示跳过的测试
    if test_result_obj.skipped:
        print(f"\n⚠️  跳过的测试:")
        for test, reason in test_result_obj.skipped:
            print(f"  - {test._testMethodName}: {reason}")
    
    # 保存JSON报告
    report_data = {
        "timestamp": datetime.now().isoformat(),
        "duration": duration,
        "total_tests": test_result_obj.total_tests,
        "passed_tests": test_result_obj.passed_tests,
        "failed_tests": test_result_obj.failed_tests,
        "skipped_tests": test_result_obj.skipped_tests,
        "success_rate": (test_result_obj.passed_tests / test_result_obj.total_tests * 100) if test_result_obj.total_tests > 0 else 0,
        "failures": [{"test": str(test), "error": str(err)} for test, err in test_result_obj.failures],
        "errors": [{"test": str(test), "error": str(err)} for test, err in test_result_obj.errors],
        "skipped": [{"test": str(test), "reason": reason} for test, reason in test_result_obj.skipped]
    }
    
    try:
        with open("test_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        print(f"\n💾 详细报告已保存到: test_report.json")
    except Exception as e:
        print(f"\n⚠️  保存报告失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 策略管理系统自动化测试开始")
    print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建测试结果对象
    test_result = TestResult()
    test_result.start_time = time.time()
    
    # 定义测试套件
    test_suites = [
        {
            "name": "单元测试",
            "path": "backend/tests/unit/test_strategy_management.py",
            "description": "测试策略模板、参数验证、代码生成等核心功能"
        },
        {
            "name": "集成测试", 
            "path": "backend/tests/integration/test_strategy_api_integration.py",
            "description": "测试API集成、数据库操作、完整工作流程"
        },
        {
            "name": "性能测试",
            "path": "backend/tests/performance/test_strategy_performance.py", 
            "description": "测试API响应时间、并发处理、内存使用"
        }
    ]
    
    # 运行所有测试套件
    successful_suites = 0
    for suite in test_suites:
        print(f"\n📝 {suite['description']}")
        
        if os.path.exists(suite["path"]):
            if run_test_suite(suite["path"], test_result, suite["name"]):
                successful_suites += 1
        else:
            print(f"⚠️  测试文件不存在: {suite['path']}")
    
    test_result.end_time = time.time()
    
    # 生成测试报告
    generate_test_report(test_result)
    
    # 输出总结
    print(f"\n🏁 测试完成!")
    print(f"📊 成功运行的测试套件: {successful_suites}/{len(test_suites)}")
    
    # 根据测试结果设置退出码
    if test_result.failed_tests > 0:
        print("❌ 存在失败的测试")
        sys.exit(1)
    elif test_result.total_tests == 0:
        print("⚠️  没有运行任何测试")
        sys.exit(1)
    else:
        print("✅ 所有测试通过!")
        sys.exit(0)

if __name__ == "__main__":
    main()
