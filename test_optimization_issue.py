#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化问题诊断脚本
专门检测为什么应用建议后数据不变的问题
"""

import requests
import json
import time
import sys

def test_optimization_issue():
    """测试策略优化数据变化问题"""
    print("=" * 60)
    print("策略优化数据变化问题诊断")
    print("=" * 60)
    
    backend_url = "http://localhost:8000"
    frontend_url = "http://localhost:8080"
    
    strategy_id = None
    
    # 1. 获取策略ID
    print("\n1. 获取测试策略ID...")
    try:
        response = requests.get(f"{backend_url}/api/v1/strategies", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and len(data['data']) > 0:
                strategy_id = data['data'][0]['id']
                print(f"✓ 找到策略ID: {strategy_id}")
            else:
                print("✗ 没有可用策略")
                return False
        else:
            print(f"✗ 获取策略失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 获取策略异常: {e}")
        return False
    
    # 2. 获取初始优化数据
    print(f"\n2. 获取初始优化数据 (策略ID: {strategy_id})...")
    initial_data = None
    try:
        response = requests.get(f"{backend_url}/api/v1/strategies/{strategy_id}/optimization", timeout=10)
        if response.status_code == 200:
            initial_data = response.json()
            print("✓ 获取初始优化数据成功")
            
            # 显示当前性能
            current_perf = initial_data.get('current_performance', {})
            print(f"  当前年化收益率: {current_perf.get('annualized_return')}%")
            print(f"  当前夏普比率: {current_perf.get('sharpe_ratio')}")
            print(f"  当前最大回撤: {current_perf.get('max_drawdown')}%")
            
            # 显示优化建议
            suggestions = initial_data.get('optimization_suggestions', [])
            print(f"  优化建议数量: {len(suggestions)}")
            for i, suggestion in enumerate(suggestions):
                print(f"    建议{i+1}: {suggestion.get('parameter')} ({suggestion.get('current_value')} -> {suggestion.get('suggested_value')})")
        else:
            print(f"✗ 获取初始优化数据失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 获取初始优化数据异常: {e}")
        return False
    
    # 3. 应用第一个优化建议
    if initial_data and initial_data.get('optimization_suggestions'):
        first_suggestion = initial_data['optimization_suggestions'][0]
        parameter = first_suggestion.get('parameter')
        suggested_value = first_suggestion.get('suggested_value')
        
        print(f"\n3. 应用优化建议: {parameter} = {suggested_value}...")
        try:
            apply_data = {
                'parameter': parameter,
                'value': suggested_value
            }
            response = requests.post(f"{backend_url}/api/v1/strategies/{strategy_id}/optimize", 
                                   json=apply_data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✓ 优化应用成功")
                    print(f"  应用参数: {result.get('applied_parameter')}")
                    print(f"  应用值: {result.get('applied_value')}")
                    print(f"  更新时间: {result.get('timestamp')}")
                else:
                    print(f"✗ 优化应用失败: {result.get('message')}")
                    return False
            else:
                print(f"✗ 优化应用请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 优化应用异常: {e}")
            return False
    else:
        print("\n3. 没有可用的优化建议")
        return False
    
    # 4. 等待并重新获取优化数据
    print(f"\n4. 等待3秒后重新获取优化数据...")
    time.sleep(3)
    
    try:
        response = requests.get(f"{backend_url}/api/v1/strategies/{strategy_id}/optimization", timeout=10)
        if response.status_code == 200:
            new_data = response.json()
            print("✓ 获取新优化数据成功")
            
            # 比较数据变化
            print(f"\n5. 数据变化分析...")
            
            # 比较当前性能
            initial_perf = initial_data.get('current_performance', {})
            new_perf = new_data.get('current_performance', {})
            
            print("  当前性能指标比较:")
            print(f"    年化收益率: {initial_perf.get('annualized_return')} -> {new_perf.get('annualized_return')}")
            print(f"    夏普比率: {initial_perf.get('sharpe_ratio')} -> {new_perf.get('sharpe_ratio')}")
            print(f"    最大回撤: {initial_perf.get('max_drawdown')} -> {new_perf.get('max_drawdown')}")
            
            # 比较优化建议
            initial_suggestions = initial_data.get('optimization_suggestions', [])
            new_suggestions = new_data.get('optimization_suggestions', [])
            
            print("  优化建议比较:")
            print(f"    建议数量: {len(initial_suggestions)} -> {len(new_suggestions)}")
            
            data_changed = False
            
            # 检查性能指标是否变化
            for key in ['annualized_return', 'sharpe_ratio', 'max_drawdown']:
                if initial_perf.get(key) != new_perf.get(key):
                    data_changed = True
                    print(f"    ✓ {key} 发生变化")
            
            # 检查建议是否变化
            if len(initial_suggestions) != len(new_suggestions):
                data_changed = True
                print("    ✓ 建议数量发生变化")
            else:
                for i, (initial_sug, new_sug) in enumerate(zip(initial_suggestions, new_suggestions)):
                    if (initial_sug.get('current_value') != new_sug.get('current_value') or
                        initial_sug.get('suggested_value') != new_sug.get('suggested_value')):
                        data_changed = True
                        print(f"    ✓ 建议{i+1} 发生变化")
            
            if data_changed:
                print("\n🎉 数据发生了变化！优化功能正常工作")
                return True
            else:
                print("\n❌ 数据没有变化！这就是问题所在")
                print("\n🔍 问题分析:")
                print("1. 优化算法使用固定随机种子，导致每次生成相同数据")
                print("2. 应用的参数没有真正影响优化建议的计算")
                print("3. 后端缓存机制导致数据不刷新")
                print("4. 数据库更新成功但优化算法没有读取最新参数")
                
                # 检查数据库中的参数是否真的更新了
                print(f"\n6. 检查数据库参数更新...")
                try:
                    # 这里可以添加直接查询数据库的代码
                    print("  需要检查数据库中策略参数是否真的更新了")
                except Exception as e:
                    print(f"  数据库检查失败: {e}")
                
                return False
        else:
            print(f"✗ 获取新优化数据失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 获取新优化数据异常: {e}")
        return False

def analyze_optimization_algorithm():
    """分析优化算法的问题"""
    print("\n" + "=" * 60)
    print("优化算法问题分析")
    print("=" * 60)
    
    print("\n根据代码分析，问题可能出现在以下几个方面：")
    print("\n1. 固定随机种子问题:")
    print("   - 代码中使用 random.seed(strategy_id + 1000) 生成固定种子")
    print("   - 这导致每次为同一策略生成的优化数据都相同")
    print("   - 即使参数发生变化，随机种子仍然相同")
    
    print("\n2. 优化算法不考虑当前参数:")
    print("   - 优化建议的生成没有读取策略的实际参数")
    print("   - 只是基于策略ID生成模拟数据")
    print("   - 应该根据当前参数计算真实的优化建议")
    
    print("\n3. 缺少参数变化检测:")
    print("   - 优化算法没有检测参数是否发生变化")
    print("   - 应该在参数变化后重新计算优化建议")
    
    print("\n4. 建议的解决方案:")
    print("   - 修改随机种子生成逻辑，加入参数哈希值")
    print("   - 让优化算法读取策略的实际参数")
    print("   - 根据参数变化动态调整优化建议")
    print("   - 添加参数变化时间戳，影响数据生成")

if __name__ == "__main__":
    success = test_optimization_issue()
    analyze_optimization_algorithm()
    
    if not success:
        print(f"\n💡 建议修复步骤:")
        print("1. 修改后端优化数据生成逻辑")
        print("2. 让随机种子包含参数信息")
        print("3. 根据实际参数计算优化建议")
        print("4. 添加参数变化检测机制")
        sys.exit(1)
    else:
        print(f"\n✅ 优化功能工作正常")
        sys.exit(0)
