#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio
import logging
import os
import json

logger = logging.getLogger(__name__)

class MonitorManager:
    """
    监控管理器，用于管理数据源监控任务
    """
    
    _initialized = False
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, init_services: bool = True):
        """
        初始化监控管理器
        
        Args:
            config: 配置信息
            init_services: 是否立即初始化服务
        """
        if self._initialized:
            return
        
        self._initialized = True
        self._monitor_tasks = {}  # 保存正在运行的监控任务 {source_id: task}
        
        # 默认配置
        self.config = config or {
            'monitoring': {
                'enabled': True,
                'auto_schedule': True,
                'schedule_interval': 3600,  # 默认1小时
                'data_quality': {
                    'enabled': True,
                    'alert_enabled': True
                },
                'anomaly_detection': {
                    'enabled': True
                },
                'cross_validation': {
                    'enabled': True
                },
                'dashboard': {
                    'refresh_interval': 300  # 5分钟刷新一次
                },
                'save_results': True,
                'result_storage_path': 'data/monitoring'
            }
        }
        
        # 初始化服务
        if init_services:
            self._init_services()
            
        logger.info("监控管理器已初始化")
    
    def _init_services(self):
        """初始化监控相关服务"""
        try:
            # 导入数据质量处理器
            from app.utils.data_quality_processor import DataQualityProcessor
            self.quality_processor = DataQualityProcessor()
            
            # 确保结果存储目录存在
            if self.config['monitoring']['save_results']:
                os.makedirs(self.config['monitoring']['result_storage_path'], exist_ok=True)
                
            logger.info("监控服务初始化成功")
        except Exception as e:
            logger.error(f"初始化监控服务失败: {str(e)}")
            raise
    
    async def monitor_data_source(self, 
                         source_id: int,
                         options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        监控数据源
        
        Args:
            source_id: 数据源ID
            options: 监控选项
            
        Returns:
            监控结果
        """
        try:
            options = options or {}
            
            # 获取数据源信息
            from app.db.database import get_db
            db = next(get_db())
            
            from app.models.data import DataSource
            source_info = db.query(DataSource).filter(DataSource.id == source_id).first()
            
            if not source_info:
                logger.error(f"数据源不存在: {source_id}")
                return {
                    "source_id": source_id,
                    "status": "error",
                    "message": f"数据源不存在: {source_id}"
                }
            
            # 获取监控选项
            timeframes = options.get('timeframes', [source_info.timeframe])
            if isinstance(timeframes, str):
                timeframes = [timeframes]
                
            # 设置监控选项
            monitor_options = {
                'detect_price': options.get('detect_price', True),
                'detect_volume': options.get('detect_volume', True),
                'check_consistency': options.get('check_consistency', True),
                'repair': options.get('repair', False)
            }
            
            # 获取数据源的交易所和交易对信息
            exchange = source_info.source_type
            symbol = source_info.symbol
            
            # 监控结果
            results = {}
            
            # 对每个时间级别进行监控
            for timeframe in timeframes:
                logger.info(f"监控数据源 {source_id} ({exchange}:{symbol}) 的 {timeframe} 数据")
                
                # 获取市场数据
                # 这里应该调用市场数据服务获取数据
                # 为了演示，我们假设已经获取了数据
                primary_data = []
                
                # 执行监控
                result = await self._monitor_data(
                    source_id=source_id,
                    exchange=exchange,
                    symbol=symbol,
                    timeframe=timeframe,
                    data=primary_data,
                    options=monitor_options
                )
                
                # 保存结果
                results[timeframe] = result
                
                # 检查是否需要生成告警
                if self.config['monitoring']['data_quality']['alert_enabled']:
                    await self._check_and_create_alerts(result, source_info)
                
                # 保存监控结果到存储
                if self.config['monitoring']['save_results']:
                    self._save_monitoring_result(source_id, timeframe, result)
            
            return {
                "source_id": source_id,
                "exchange": exchange,
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "results": results
            }
        except Exception as e:
            logger.error(f"监控数据源 {source_id} 失败: {str(e)}")
            return {
                "source_id": source_id,
                "status": "error",
                "message": f"监控失败: {str(e)}"
            }
    
    async def _monitor_data(self,
                    source_id: int,
                    exchange: str,
                    symbol: str,
                    timeframe: str,
                    data: List[Dict[str, Any]],
                    options: Dict[str, Any]) -> Dict[str, Any]:
        """
        监控数据
        
        Args:
            source_id: 数据源ID
            exchange: 交易所
            symbol: 交易对
            timeframe: 时间级别
            data: 市场数据
            options: 监控选项
            
        Returns:
            监控结果
        """
        try:
            # 调用数据质量处理器
            from app.db.database import get_db
            db = next(get_db())
            
            # 获取质量评分
            quality_score = await self.quality_processor.get_source_quality_score(
                db, source_id, timeframe, 
                datetime.now() - timedelta(days=7), datetime.now()
            )
            
            # 获取缺口摘要
            gap_summary = await self.quality_processor.get_source_gap_summary(
                db, source_id, timeframe,
                datetime.now() - timedelta(days=7), datetime.now()
            )
            
            # 获取异常摘要
            anomaly_summary = await self.quality_processor.get_source_anomaly_summary(
                db, source_id, timeframe,
                datetime.now() - timedelta(days=7), datetime.now()
            )
            
            # 生成监控指标
            monitoring_metrics = self.quality_processor.generate_monitoring_metrics(
                source_id,
                exchange,
                symbol,
                timeframe,
                {"quality_score": quality_score["overall"]},
                gap_summary
            )
            
            # 生成监控结果
            return {
                "metrics": monitoring_metrics,
                "quality_score": quality_score,
                "gap_summary": gap_summary,
                "anomaly_summary": anomaly_summary,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"监控数据失败: {str(e)}")
            return {
                "status": "error",
                "message": f"监控数据失败: {str(e)}"
            }
    
    async def _check_and_create_alerts(self, 
                              result: Dict[str, Any],
                              source_info: Any) -> None:
        """
        检查并创建告警
        
        Args:
            result: 监控结果
            source_info: 数据源信息
        """
        try:
            # 检查质量评分
            quality_score = result.get("quality_score", {}).get("overall", 0)
            
            if quality_score < 60:
                # 创建质量告警
                logger.warning(f"数据源 {source_info.id} ({source_info.name}) 的质量评分过低: {quality_score}")
                
                # 这里应该调用告警服务创建告警
                # 为了演示，我们只记录日志
        except Exception as e:
            logger.error(f"检查并创建告警失败: {str(e)}")
    
    def _save_monitoring_result(self,
                       source_id: int,
                       timeframe: str,
                       result: Dict[str, Any]) -> None:
        """
        保存监控结果
        
        Args:
            source_id: 数据源ID
            timeframe: 时间级别
            result: 监控结果
        """
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{source_id}_{timeframe}_{timestamp}.json"
            
            # 保存路径
            save_path = os.path.join(
                self.config['monitoring']['result_storage_path'],
                filename
            )
            
            # 保存结果
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
                
            logger.info(f"监控结果已保存到: {save_path}")
        except Exception as e:
            logger.error(f"保存监控结果失败: {str(e)}")
    
    async def schedule_monitoring(self, 
                        source_id: int, 
                        interval: Optional[int] = None):
        """
        为数据源调度定期监控
        
        Args:
            source_id: 数据源ID
            interval: 监控间隔（秒）
        """
        # 取消已有的定期监控
        if source_id in self._monitor_tasks:
            self._monitor_tasks[source_id].cancel()
            
        # 获取监控间隔
        if interval is None:
            interval = self.config['monitoring']['schedule_interval']
        
        # 创建新的监控任务
        task = asyncio.create_task(self._periodic_monitor(source_id, interval))
        self._monitor_tasks[source_id] = task
        
        logger.info(f"已为数据源 {source_id} 调度监控，间隔 {interval} 秒")
        return True
    
    async def _periodic_monitor(self, source_id: int, interval: int):
        """
        执行定期监控
        
        Args:
            source_id: 数据源ID
            interval: 监控间隔（秒）
        """
        try:
            while True:
                # 执行监控
                await self.monitor_data_source(source_id)
                
                # 等待下一次监控
                await asyncio.sleep(interval)
        except asyncio.CancelledError:
            logger.info(f"数据源 {source_id} 的定期监控已取消")
        except Exception as e:
            logger.error(f"数据源 {source_id} 的定期监控出错: {str(e)}")
            # 抛出异常，以便上层处理或重新启动
            raise
    
    async def cancel_monitoring(self, source_id: int) -> bool:
        """
        取消数据源的定期监控
        
        Args:
            source_id: 数据源ID
            
        Returns:
            是否成功取消
        """
        if source_id in self._monitor_tasks:
            self._monitor_tasks[source_id].cancel()
            del self._monitor_tasks[source_id]
            logger.info(f"已取消数据源 {source_id} 的定期监控")
            return True
        return False
