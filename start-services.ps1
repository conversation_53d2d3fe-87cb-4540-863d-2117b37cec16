# 量化交易系统服务启动脚本

# 颜色配置
$infoColor = "Cyan"
$successColor = "Green"
$errorColor = "Red"
$warningColor = "Yellow"

# 检查端口是否被占用
function Test-PortInUse {
    param(
        [int]$Port
    )

    $connections = netstat -ano | findstr ":$Port "
    if ($connections | Select-String "LISTENING") {
        return $true
    }
    return $false
}

# 创建日志目录
if (-not (Test-Path "logs")) {
    New-Item -Path "logs" -ItemType Directory | Out-Null
}

# 提示信息
Write-Host "=========================================================" -ForegroundColor $infoColor
Write-Host "                 量化交易系统服务启动工具                 " -ForegroundColor $infoColor
Write-Host "=========================================================" -ForegroundColor $infoColor
Write-Host ""

# 初始化进程ID列表
$processIds = @()
$processListFile = "service-processes.txt"

# 检查并清理旧进程文件
if (Test-Path $processListFile) {
    Remove-Item $processListFile -Force
}

# 先停止之前运行的服务
Write-Host "正在停止所有运行中的服务..." -ForegroundColor $infoColor
Stop-Process -Name "python" -ErrorAction SilentlyContinue
Stop-Process -Name "node" -ErrorAction SilentlyContinue
Write-Host "已停止所有服务" -ForegroundColor $successColor
Start-Sleep -Seconds 3
Write-Host ""

# 检查Python
Write-Host "检查Python环境..." -ForegroundColor $infoColor
try {
    $pythonVersion = python --version
    Write-Host $pythonVersion -ForegroundColor $successColor

    # 安装必要的包
    Write-Host "安装必要的Python包..." -ForegroundColor $infoColor
    pip install Flask PyJWT flask-cors dash dash-bootstrap-components celery[sqlalchemy] sqlalchemy fastapi uvicorn -q
    Write-Host "Python包安装完成" -ForegroundColor $successColor
} catch {
    Write-Host "Python检查失败: $_" -ForegroundColor $errorColor
    Write-Host "请确保Python已安装并添加到PATH" -ForegroundColor $errorColor
    exit
}

# 设置环境变量
$env:PYTHONPATH = "$PWD"
$env:FLASK_DEBUG = "1"
$env:FLASK_APP = "backend\simple_api.py"
$env:VUE_APP_API_URL = "http://localhost:8000/api/v1"
$env:VUE_APP_BACKTEST_API_URL = "http://localhost:8001/api/v1"
$env:VUE_APP_RISK_API_URL = "http://localhost:8002/api/v1"
$env:VUE_APP_SIGNAL_API_URL = "http://localhost:8004/api/v1"
$env:VUE_APP_MARKET_API_URL = "http://localhost:8005/api/v1"
$env:VUE_APP_WEBSOCKET_URL = "ws://localhost:8765/ws"

Write-Host "环境变量已设置" -ForegroundColor $successColor
Write-Host ""

# 初始化数据库
Write-Host "初始化数据库..." -ForegroundColor $infoColor
try {
    python -m backend.app.db.setup_db
    if ($LASTEXITCODE -ne 0) {
        Write-Host "数据库初始化失败！" -ForegroundColor $errorColor
        Write-Host "继续启动其他服务..." -ForegroundColor $warningColor
    } else {
        Write-Host "数据库初始化成功！" -ForegroundColor $successColor
    }
} catch {
    Write-Host "数据库初始化出错: $_" -ForegroundColor $errorColor
    Write-Host "继续启动其他服务..." -ForegroundColor $warningColor
}
Write-Host ""

# 检查端口占用情况
$mainApiPortBusy = Test-PortInUse -Port 8000
$backendPortBusy = Test-PortInUse -Port 8001
$riskApiPortBusy = Test-PortInUse -Port 8002
$datasourceApiPortBusy = Test-PortInUse -Port 8003
$signalApiPortBusy = Test-PortInUse -Port 8004
$marketApiPortBusy = Test-PortInUse -Port 8005
$websocketPortBusy = Test-PortInUse -Port 8765
$tradeExecPortBusy = Test-PortInUse -Port 8888
$dashboardPortBusy = Test-PortInUse -Port 6009
$frontendPortBusy = Test-PortInUse -Port 8080

# 输出已占用端口警告
if ($mainApiPortBusy -or $backendPortBusy -or $riskApiPortBusy -or $datasourceApiPortBusy -or
    $signalApiPortBusy -or $marketApiPortBusy -or $websocketPortBusy -or $frontendPortBusy -or
    $tradeExecPortBusy) {

    Write-Host "警告: 以下端口已被占用，相应的服务可能无法启动:" -ForegroundColor $warningColor
    if ($mainApiPortBusy) { Write-Host "- 端口 8000 (主API服务)" -ForegroundColor $warningColor }
    if ($backendPortBusy) { Write-Host "- 端口 8001 (后台管理API)" -ForegroundColor $warningColor }
    if ($riskApiPortBusy) { Write-Host "- 端口 8002 (风险管理API)" -ForegroundColor $warningColor }
    if ($datasourceApiPortBusy) { Write-Host "- 端口 8003 (数据源API服务)" -ForegroundColor $warningColor }
    if ($signalApiPortBusy) { Write-Host "- 端口 8004 (信号API服务)" -ForegroundColor $warningColor }
    if ($marketApiPortBusy) { Write-Host "- 端口 8005 (市场数据API服务)" -ForegroundColor $warningColor }
    if ($websocketPortBusy) { Write-Host "- 端口 8765 (WebSocket服务)" -ForegroundColor $warningColor }
    if ($frontendPortBusy) { Write-Host "- 端口 8080 (前端服务)" -ForegroundColor $warningColor }
    if ($tradeExecPortBusy) { Write-Host "- 端口 8888 (交易执行服务)" -ForegroundColor $warningColor }
}

# 启动Celery Worker和Beat
Write-Host "[0/11] 启动Celery Worker和Beat..." -ForegroundColor $infoColor
try {
    # 确保安装必要的依赖
    pip install sqlalchemy celery -q

    # 创建Celery数据目录
    $celeryDataDir = "backend\celery_data"
    if (-not (Test-Path $celeryDataDir)) {
        New-Item -Path $celeryDataDir -ItemType Directory | Out-Null
        Write-Host "创建目录: $celeryDataDir" -ForegroundColor $infoColor
    }

    # 使用当前激活的Python环境
    $pythonExe = (Get-Command python).Source
    Write-Host "使用Python环境: $pythonExe" -ForegroundColor $infoColor

    # 确保设置正确的PYTHONPATH环境变量
    $env:PYTHONPATH = "$PWD"

    # 创建一个临时脚本来启动Celery Worker，确保环境变量正确设置
    $workerScriptPath = ".\temp_celery_worker.ps1"
    $workerScript = @"
`$env:PYTHONPATH = "$PWD"
cd "$PWD"
& "$pythonExe" -m celery -A backend.app.core.celery_config worker --loglevel=debug --pool=solo --without-heartbeat --without-gossip --without-mingle
"@
    $workerScript | Out-File -FilePath $workerScriptPath -Encoding utf8

    # 创建一个临时脚本来启动Celery Beat，确保环境变量正确设置
    $beatScriptPath = ".\temp_celery_beat.ps1"
    $beatScript = @"
`$env:PYTHONPATH = "$PWD"
cd "$PWD"
& "$pythonExe" -m celery -A backend.app.core.celery_config beat --loglevel=debug
"@
    $beatScript | Out-File -FilePath $beatScriptPath -Encoding utf8

    # 启动Celery Worker，使用临时脚本
    $celeryProcess = Start-Process -FilePath "powershell.exe" -ArgumentList "-ExecutionPolicy Bypass -File $workerScriptPath" -PassThru -WindowStyle Normal -RedirectStandardOutput "logs\celery_worker.log"

    # 等待Worker启动
    Start-Sleep -Seconds 5

    # 启动Celery Beat，使用临时脚本
    $celeryBeatProcess = Start-Process -FilePath "powershell.exe" -ArgumentList "-ExecutionPolicy Bypass -File $beatScriptPath" -PassThru -WindowStyle Normal -RedirectStandardOutput "logs\celery_beat.log"

    # 检查启动状态
    if ($celeryProcess -and $celeryBeatProcess) {
        $processIds += $celeryProcess.Id
        $processIds += $celeryBeatProcess.Id

        Write-Host "Celery Worker已启动，进程ID: $($celeryProcess.Id)" -ForegroundColor $successColor
        Write-Host "Celery Beat已启动，进程ID: $($celeryBeatProcess.Id)" -ForegroundColor $successColor
        Add-Content -Path $processListFile -Value "Celery Worker,,$($celeryProcess.Id)"
        Add-Content -Path $processListFile -Value "Celery Beat,,$($celeryBeatProcess.Id)"

        # 等待几秒，确保进程启动
        Start-Sleep -Seconds 5

        # 检查Celery Worker和Beat是否正常运行
        $workerRunning = $false
        $beatRunning = $false

        # 检查Worker进程
        try {
            $workerProcess = Get-Process -Id $celeryProcess.Id -ErrorAction SilentlyContinue
            if ($workerProcess) {
                $workerRunning = $true
                Write-Host "Celery Worker运行正常" -ForegroundColor $successColor
            } else {
                Write-Host "Celery Worker进程已终止" -ForegroundColor $errorColor
                # 显示日志文件的最后几行，帮助诊断问题
                Write-Host "Celery Worker日志内容:" -ForegroundColor $warningColor
                if (Test-Path "logs\celery_worker.log") {
                    Get-Content "logs\celery_worker.log" -Tail 10
                } else {
                    Write-Host "找不到Celery Worker日志文件" -ForegroundColor $errorColor
                }
            }
        } catch {
            Write-Host "Celery Worker状态检查失败: $_" -ForegroundColor $errorColor
        }

        # 检查Beat进程
        try {
            $beatProcess = Get-Process -Id $celeryBeatProcess.Id -ErrorAction SilentlyContinue
            if ($beatProcess) {
                $beatRunning = $true
                Write-Host "Celery Beat运行正常" -ForegroundColor $successColor
            } else {
                Write-Host "Celery Beat进程已终止" -ForegroundColor $errorColor
                # 显示日志文件的最后几行，帮助诊断问题
                Write-Host "Celery Beat日志内容:" -ForegroundColor $warningColor
                if (Test-Path "logs\celery_beat.log") {
                    Get-Content "logs\celery_beat.log" -Tail 10
                } else {
                    Write-Host "找不到Celery Beat日志文件" -ForegroundColor $errorColor
                }
            }
        } catch {
            Write-Host "Celery Beat状态检查失败: $_" -ForegroundColor $errorColor
        }

        # 如果任一进程不运行，显示警告
        if (-not $workerRunning -or -not $beatRunning) {
            Write-Host "警告: Celery服务启动异常，请检查日志文件" -ForegroundColor $warningColor
            Write-Host "  - Worker日志: logs\celery_worker.log" -ForegroundColor $warningColor
            Write-Host "  - Beat日志: logs\celery_beat.log" -ForegroundColor $warningColor

            # 检查是否存在SQLite数据库文件
            if (-not (Test-Path "celery.db")) {
                Write-Host "警告: 未找到celery.db文件，这可能是Celery服务启动失败的原因" -ForegroundColor $warningColor
                Write-Host "正在创建空的SQLite数据库文件..." -ForegroundColor $infoColor

                # 创建空的SQLite数据库文件
                $null = New-Item -ItemType File -Path "celery.db" -Force
                Write-Host "已创建celery.db文件，请重新运行启动脚本" -ForegroundColor $successColor
            }

            # 检查backend/app/tasks.py是否存在
            if (-not (Test-Path "backend\app\tasks.py")) {
                Write-Host "警告: 未找到backend\app\tasks.py文件，这可能是Celery服务启动失败的原因" -ForegroundColor $warningColor
            }

            # 检查Python依赖
            Write-Host "检查Celery相关依赖..." -ForegroundColor $infoColor
            pip install celery sqlalchemy redis -q

            Write-Host "继续启动其他服务..." -ForegroundColor $infoColor
        }
    } else {
        Write-Host "启动Celery服务失败!" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动Celery服务时出错: $_" -ForegroundColor $errorColor
}
Write-Host ""

# 确保logs目录存在
if (-not (Test-Path "logs")) {
    New-Item -Path "logs" -ItemType Directory | Out-Null
    Write-Host "已创建logs目录" -ForegroundColor $infoColor
}

# 启动主API服务
Write-Host "[1/10] 启动主API服务 (端口 8000)..." -ForegroundColor $infoColor
try {
    if (-not $mainApiPortBusy) {
        # 启动主API服务，在新的终端窗口中显示
        $mainApiProcess = Start-Process -FilePath "cmd" -ArgumentList "/k", "python backend\simple_api.py" -PassThru -WindowStyle Normal

        if ($mainApiProcess) {
            $processIds += $mainApiProcess.Id
            Write-Host "主API服务已启动，进程ID: $($mainApiProcess.Id)" -ForegroundColor $successColor
            Add-Content -Path $processListFile -Value "主API服务,8000,$($mainApiProcess.Id)"

            # 等待服务启动并进行健康检查
            Start-Sleep -Seconds 5
            try {
                $response = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/health" -Method GET -TimeoutSec 10 -ErrorAction Stop
                if ($response.status -eq "ok") {
                    Write-Host "主API服务健康检查通过" -ForegroundColor $successColor
                } else {
                    Write-Host "主API服务响应异常: $response" -ForegroundColor $warningColor
                }
            } catch {
                Write-Host "主API服务健康检查失败: $_" -ForegroundColor $warningColor
                Write-Host "服务可能仍在启动中，请稍后手动检查" -ForegroundColor $warningColor
            }
        } else {
            Write-Host "启动主API服务失败!" -ForegroundColor $errorColor
        }
    } else {
        Write-Host "主API服务启动失败: 端口 8000 已被占用" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动主API服务时出错: $_" -ForegroundColor $errorColor
}

# 启动回测API服务
Write-Host "[2/10] 启动回测API服务 (端口 8001)..." -ForegroundColor $infoColor
try {
    if (-not $backendPortBusy) {
        $backtestApiProcess = Start-Process -FilePath "python" -ArgumentList "-m uvicorn backend.backtest_api_fastapi:app --host 0.0.0.0 --port 8001" -PassThru -WindowStyle Normal -RedirectStandardOutput "logs\backtest_api.log"
        if ($backtestApiProcess) {
            $processIds += $backtestApiProcess.Id
            Write-Host "回测API服务已启动，进程ID: $($backtestApiProcess.Id)" -ForegroundColor $successColor
            Add-Content -Path $processListFile -Value "回测API服务,8001,$($backtestApiProcess.Id)"
        } else {
            Write-Host "启动回测API服务失败!" -ForegroundColor $errorColor
        }
    } else {
        Write-Host "回测API服务启动失败: 端口 8001 已被占用" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动回测API服务时出错: $_" -ForegroundColor $errorColor
}

# 启动风险管理API服务
Write-Host "[3/10] 启动风险管理API服务 (端口 8002)..." -ForegroundColor $infoColor
try {
    if (-not $riskApiPortBusy) {
        # 检查根目录下是否存在risk_management_api.py
        if (Test-Path "risk_management_api.py") {
            $riskApiProcess = Start-Process -FilePath "python" -ArgumentList "risk_management_api.py" -PassThru -WindowStyle Normal -RedirectStandardOutput "logs\risk_api.log"
        } else {
            # 如果根目录下不存在，则使用backend目录下的
            $riskApiProcess = Start-Process -FilePath "python" -ArgumentList "backend\risk_management_api.py" -PassThru -WindowStyle Normal -RedirectStandardOutput "logs\risk_api.log"
        }

        if ($riskApiProcess) {
            $processIds += $riskApiProcess.Id
            Write-Host "风险管理API服务已启动，进程ID: $($riskApiProcess.Id)" -ForegroundColor $successColor
            Add-Content -Path $processListFile -Value "风险管理API服务,8002,$($riskApiProcess.Id)"
        } else {
            Write-Host "启动风险管理API服务失败!" -ForegroundColor $errorColor
        }
    } else {
        Write-Host "风险管理API服务启动失败: 端口 8002 已被占用" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动风险管理API服务时出错: $_" -ForegroundColor $errorColor
}

# 启动交易API服务
Write-Host "[4/11] 启动交易API服务 (端口 8003)..." -ForegroundColor $infoColor
try {
    if (-not $datasourceApiPortBusy) {
        # 从.env文件加载API密钥
        $envFile = "backend\.env"
        if (Test-Path $envFile) {
            Get-Content $envFile | ForEach-Object {
                if ($_ -match "^\s*([^#][^=]+)=(.*)$") {
                    $key = $matches[1].Trim()
                    $value = $matches[2].Trim()
                    # 设置环境变量
                    [Environment]::SetEnvironmentVariable($key, $value, "Process")
                    Write-Host "已加载环境变量: $key" -ForegroundColor $infoColor
                }
            }
        } else {
            Write-Host "警告: .env文件不存在，API密钥可能未正确设置" -ForegroundColor $warningColor
        }

        # 启动交易API服务
        $tradingApiProcess = Start-Process -FilePath "python" -ArgumentList "backend\trading_api.py" -PassThru -WindowStyle Normal -RedirectStandardOutput "logs\trading_api.log"
        if ($tradingApiProcess) {
            $processIds += $tradingApiProcess.Id
            Write-Host "交易API服务已启动，进程ID: $($tradingApiProcess.Id)" -ForegroundColor $successColor
            Add-Content -Path $processListFile -Value "交易API服务,8003,$($tradingApiProcess.Id)"

            # 等待5秒确保服务启动
            Start-Sleep -Seconds 5
            # 检查服务是否正常响应
            try {
                $response = Invoke-RestMethod -Uri "http://localhost:8003/health" -Method GET -TimeoutSec 5 -ErrorAction Stop
                if ($response.status -eq "ok") {
                    Write-Host "交易API服务健康检查通过" -ForegroundColor $successColor
                } else {
                    Write-Host "交易API服务响应异常: $response" -ForegroundColor $warningColor
                }
            } catch {
                Write-Host "交易API服务健康检查失败: $_" -ForegroundColor $warningColor
                Write-Host "服务可能尚未完全启动，请检查日志" -ForegroundColor $warningColor
            }
        } else {
            Write-Host "启动交易API服务失败!" -ForegroundColor $errorColor
        }
    } else {
        Write-Host "交易API服务启动失败: 端口 8003 已被占用" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动交易API服务时出错: $_" -ForegroundColor $errorColor
}

# 启动信号API服务
Write-Host "[5/11] 启动信号API服务 (端口 8004)..." -ForegroundColor $infoColor
try {
    if (-not $signalApiPortBusy) {
        $signalApiProcess = Start-Process -FilePath "python" -ArgumentList "backend\signal_api.py" -PassThru -WindowStyle Normal -RedirectStandardOutput "logs\signal_api.log"
        if ($signalApiProcess) {
            $processIds += $signalApiProcess.Id
            Write-Host "信号API服务已启动，进程ID: $($signalApiProcess.Id)" -ForegroundColor $successColor
            Add-Content -Path $processListFile -Value "信号API服务,8004,$($signalApiProcess.Id)"
        } else {
            Write-Host "启动信号API服务失败!" -ForegroundColor $errorColor
        }
    } else {
        Write-Host "信号API服务启动失败: 端口 8004 已被占用" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动信号API服务时出错: $_" -ForegroundColor $errorColor
}

# 启动市场数据API服务
Write-Host "[6/11] 启动市场数据API服务 (端口 8005)..." -ForegroundColor $infoColor
try {
    if (-not $marketApiPortBusy) {
        # 设置PYTHONPATH确保能找到backend模块
        $env:PYTHONPATH = "$PWD"

        $marketApiProcess = Start-Process -FilePath "python" -ArgumentList "backend\data_api_fastapi.py" -PassThru -WindowStyle Normal -RedirectStandardOutput "logs\market_api.log"
        if ($marketApiProcess) {
            $processIds += $marketApiProcess.Id
            Write-Host "市场数据API服务已启动，进程ID: $($marketApiProcess.Id)" -ForegroundColor $successColor
            Add-Content -Path $processListFile -Value "市场数据API服务,8005,$($marketApiProcess.Id)"

            # 等待5秒确保服务启动
            Start-Sleep -Seconds 5
            # 检查服务是否正常响应
            try {
                $response = Invoke-RestMethod -Uri "http://localhost:8005/health" -Method GET -TimeoutSec 5 -ErrorAction Stop
                if ($response.status -eq "ok") {
                    Write-Host "市场数据API服务健康检查通过" -ForegroundColor $successColor
                } else {
                    Write-Host "市场数据API服务响应异常: $response" -ForegroundColor $warningColor
                }
            } catch {
                Write-Host "市场数据API服务健康检查失败: $_" -ForegroundColor $warningColor
                Write-Host "服务可能尚未完全启动，请检查日志" -ForegroundColor $warningColor
            }
        } else {
            Write-Host "启动市场数据API服务失败!" -ForegroundColor $errorColor
        }
    } else {
        Write-Host "市场数据API服务启动失败: 端口 8005 已被占用" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动市场数据API服务时出错: $_" -ForegroundColor $errorColor
}

# 启动WebSocket服务
Write-Host "[6/10] 启动WebSocket服务 (端口 8765)..." -ForegroundColor $infoColor
try {
    if (-not $websocketPortBusy) {
        # 安装WebSocket服务所需的依赖
        pip install websockets asyncio aiohttp -q

        # 不使用重定向输出，让输出显示在终端窗口中
        $websocketProcess = Start-Process -FilePath "python" -ArgumentList "backend\websocket_server.py" -PassThru -WindowStyle Normal
        if ($websocketProcess) {
            $processIds += $websocketProcess.Id
            Write-Host "WebSocket服务已启动，进程ID: $($websocketProcess.Id)" -ForegroundColor $successColor
            Add-Content -Path $processListFile -Value "WebSocket服务,8765,$($websocketProcess.Id)"
        } else {
            Write-Host "启动WebSocket服务失败!" -ForegroundColor $errorColor
        }
    } else {
        Write-Host "WebSocket服务启动失败: 端口 8765 已被占用" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动WebSocket服务时出错: $_" -ForegroundColor $errorColor
}

# 启动交易执行服务
Write-Host "[7/10] 启动交易执行服务 (端口 8888)..." -ForegroundColor $infoColor
try {
    if (-not $tradeExecPortBusy) {
        if (Test-Path "backend\trade_execution_service.py") {
            $tradeExecProcess = Start-Process -FilePath "python" -ArgumentList "backend\trade_execution_service.py" -PassThru -WindowStyle Normal -RedirectStandardOutput "logs\trade_execution.log"
            if ($tradeExecProcess) {
                $processIds += $tradeExecProcess.Id
                Write-Host "交易执行服务已启动，进程ID: $($tradeExecProcess.Id)" -ForegroundColor $successColor
                Add-Content -Path $processListFile -Value "交易执行服务,8888,$($tradeExecProcess.Id)"
            } else {
                Write-Host "启动交易执行服务失败!" -ForegroundColor $errorColor
            }
        } else {
            Write-Host "交易执行服务脚本不存在，跳过启动" -ForegroundColor $warningColor
        }
    } else {
        Write-Host "交易执行服务启动失败: 端口 8888 已被占用" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动交易执行服务时出错: $_" -ForegroundColor $errorColor
}

# 等待WebSocket服务完全启动，避免端口冲突
Write-Host "等待WebSocket服务完全初始化..." -ForegroundColor $infoColor
Start-Sleep -Seconds 10

# 启动WebSocket监控服务 (使用8767端口避免与WebSocket服务健康检查端口8766冲突)
Write-Host "[8/10] 启动WebSocket监控服务 (端口 8767)..." -ForegroundColor $infoColor
try {
    # 检查8767端口是否被占用
    $wsMonitorPortBusy = $false
    try {
        $testConnection = New-Object System.Net.Sockets.TcpClient
        $testConnection.Connect("localhost", 8767)
        $testConnection.Close()
        $wsMonitorPortBusy = $true
        Write-Host "端口 8767 已被占用" -ForegroundColor $warningColor
    } catch {
        $wsMonitorPortBusy = $false
    }

    if (-not $wsMonitorPortBusy) {
        # 检查必要的依赖并安装
        pip install websockets asyncio aiohttp aiohttp_cors psutil -q

        # 确保配置目录存在
        $configDir = "backend\config"
        if (-not (Test-Path $configDir)) {
            New-Item -Path $configDir -ItemType Directory | Out-Null
            Write-Host "已创建配置目录: $configDir" -ForegroundColor $infoColor
        }

        # 使用新的启动命令，指定监控端口为8767
        $wsMonitorProcess = Start-Process -FilePath "python" -ArgumentList "backend\websocket_monitor.py --monitor-port 8767" -PassThru -WindowStyle Normal

        if ($wsMonitorProcess) {
            $processIds += $wsMonitorProcess.Id
            Write-Host "WebSocket监控服务已启动，进程ID: $($wsMonitorProcess.Id)" -ForegroundColor $successColor
            Add-Content -Path $processListFile -Value "WebSocket监控服务,8767,$($wsMonitorProcess.Id)"

            # 给服务一点时间启动
            Start-Sleep -Seconds 5

            # 检查服务是否正常运行
            try {
                $null = Invoke-RestMethod -Uri "http://localhost:8767/stats" -Method Get -TimeoutSec 5 -ErrorAction Stop
                Write-Host "WebSocket监控服务已成功响应健康检查" -ForegroundColor $successColor
            }
            catch {
                Write-Host "WebSocket监控服务健康检查失败，但进程已启动" -ForegroundColor $warningColor
                Write-Host "错误详情: $_" -ForegroundColor $warningColor
            }
        } else {
            Write-Host "启动WebSocket监控服务失败!" -ForegroundColor $errorColor
        }
    } else {
        Write-Host "WebSocket监控服务启动失败: 端口 8767 已被占用" -ForegroundColor $errorColor
        Write-Host "尝试使用已有的服务实例..." -ForegroundColor $infoColor

        # 检查现有服务是否响应
        try {
            $null = Invoke-RestMethod -Uri "http://localhost:8767/stats" -Method Get -TimeoutSec 5 -ErrorAction Stop
            Write-Host "现有WebSocket监控服务响应正常" -ForegroundColor $successColor
        }
        catch {
            Write-Host "现有WebSocket监控服务响应异常: $_" -ForegroundColor $warningColor
        }
    }
} catch {
    Write-Host "启动WebSocket监控服务时出错: $_" -ForegroundColor $errorColor
}

# 等待API服务初始化
Write-Host "等待API服务初始化..." -ForegroundColor $infoColor
Start-Sleep -Seconds 5

# 启动监控驾驶舱服务
Write-Host "[9/10] 启动监控驾驶舱 (端口 6009)..." -ForegroundColor $infoColor
try {
    if (-not $dashboardPortBusy) {
        # 不使用重定向输出，让输出显示在终端窗口中
        $dashboardProcess = Start-Process -FilePath "python" -ArgumentList "utils\dashboard\run_app.py --api-url http://localhost:8000 --port 6009" -PassThru -WindowStyle Normal

        if ($dashboardProcess) {
            $processIds += $dashboardProcess.Id
            Write-Host "监控驾驶舱已启动，进程ID: $($dashboardProcess.Id)" -ForegroundColor $successColor
            Write-Host "访问URL: http://localhost:6009" -ForegroundColor $successColor
            Add-Content -Path $processListFile -Value "监控驾驶舱,6009,$($dashboardProcess.Id)"
            # 给它一点时间启动
            Start-Sleep -Seconds 3
        } else {
            Write-Host "启动监控驾驶舱失败!" -ForegroundColor $errorColor
        }
    } else {
        Write-Host "监控驾驶舱启动失败: 端口 6009 已被占用" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动监控驾驶舱时出错: $_" -ForegroundColor $errorColor
}

# 启动前端服务
Write-Host "[10/10] 启动Vue前端应用 (端口 8080)..." -ForegroundColor $infoColor
try {
    if (-not $frontendPortBusy) {
        # 切换到前端目录
        Push-Location "frontend"

        # 确保node_modules存在
        if (-not (Test-Path "node_modules")) {
            Write-Host "正在安装依赖，这可能需要几分钟..." -ForegroundColor $infoColor
            npm install
        }

        # 设置前端环境变量
        $env:VUE_APP_MARKET_API_URL = "http://localhost:8005/api/v1"
        $env:VUE_APP_SIGNAL_API_URL = "http://localhost:8004/api/v1"
        $env:VUE_APP_RISK_API_URL = "http://localhost:8002/api/v1"
        $env:VUE_APP_BACKTEST_API_URL = "http://localhost:8001/api/v1"
        $env:VUE_APP_MAIN_API_URL = "http://localhost:8000/api/v1"
        $env:VUE_APP_MODE = "production"
        $env:NODE_ENV = "development"

        # 直接在新窗口启动前端应用，使用npm serve脚本（包含自动端口检查和清理功能）
        Write-Host "正在启动前端服务..." -ForegroundColor $infoColor
        $frontendProcess = Start-Process -FilePath "cmd.exe" -ArgumentList "/k", "cd /d $PWD && set PATH=C:\Program Files\nodejs;%PATH% && npm run serve" -PassThru -WindowStyle Normal

        if ($frontendProcess) {
            $processIds += $frontendProcess.Id
            Write-Host "Vue前端应用已启动，进程ID: $($frontendProcess.Id)" -ForegroundColor $successColor
            Write-Host "访问URL: http://localhost:8080" -ForegroundColor $successColor
            Add-Content -Path "..\$processListFile" -Value "Vue前端应用,8080,$($frontendProcess.Id)"
        } else {
            Write-Host "Vue前端应用启动失败!" -ForegroundColor $errorColor
        }

        # 返回原目录
        Pop-Location
    } else {
        Write-Host "Vue前端应用启动失败: 端口 8080 已被占用" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动Vue前端应用时出错: $_" -ForegroundColor $errorColor
}

# 启动数据质量报告API服务
Write-Host "[11/11] 启动数据质量报告API服务 (端口 8006)..." -ForegroundColor $infoColor
try {
    # 检查8006端口是否被占用
    $reportsApiPortBusy = $false
    try {
        $testConnection = New-Object System.Net.Sockets.TcpClient
        $testConnection.Connect("localhost", 8006)
        $testConnection.Close()
        $reportsApiPortBusy = $true
        Write-Host "端口 8006 已被占用" -ForegroundColor $warningColor
    } catch {
        $reportsApiPortBusy = $false
    }

    if (-not $reportsApiPortBusy) {
        # 安装必要的依赖
        pip install flask flask-cors markdown -q

        # 启动数据质量报告API服务
        $reportsApiProcess = Start-Process -FilePath "python" -ArgumentList "backend\reports_api.py" -PassThru -WindowStyle Normal -RedirectStandardOutput "logs\reports_api.log"

        if ($reportsApiProcess) {
            $processIds += $reportsApiProcess.Id
            Write-Host "数据质量报告API服务已启动，进程ID: $($reportsApiProcess.Id)" -ForegroundColor $successColor
            Add-Content -Path $processListFile -Value "数据质量报告API服务,8006,$($reportsApiProcess.Id)"

            # 给服务一点时间启动
            Start-Sleep -Seconds 5

            # 检查服务是否正常响应
            try {
                $response = Invoke-RestMethod -Uri "http://localhost:8006/api/v1/health" -Method GET -TimeoutSec 5 -ErrorAction Stop
                if ($response.status -eq "ok") {
                    Write-Host "数据质量报告API服务健康检查通过" -ForegroundColor $successColor
                } else {
                    Write-Host "数据质量报告API服务响应异常: $response" -ForegroundColor $warningColor
                }
            } catch {
                Write-Host "数据质量报告API服务健康检查失败: $_" -ForegroundColor $warningColor
                Write-Host "服务可能尚未完全启动，请检查日志" -ForegroundColor $warningColor
            }
        } else {
            Write-Host "启动数据质量报告API服务失败!" -ForegroundColor $errorColor
        }
    } else {
        Write-Host "数据质量报告API服务启动失败: 端口 8006 已被占用" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动数据质量报告API服务时出错: $_" -ForegroundColor $errorColor
}

# 等待前端应用初始化
Write-Host "等待前端应用初始化..." -ForegroundColor $infoColor
Start-Sleep -Seconds 30

# 自动打开浏览器
Write-Host "打开浏览器访问应用..." -ForegroundColor $infoColor
try {
    Start-Process "http://localhost:8080"
    Start-Sleep -Seconds 2
    Start-Process "http://localhost:6009"
    Write-Host "已自动打开浏览器访问应用" -ForegroundColor $successColor
} catch {
    Write-Host "打开浏览器失败: $_" -ForegroundColor $errorColor
}

# 保存进程ID到文件
$processIds | Out-File -FilePath "service-processes-ids.txt" -Append

Write-Host ""
Write-Host "=========================================================" -ForegroundColor $successColor
Write-Host "                  系统启动完成                            " -ForegroundColor $successColor
Write-Host "=========================================================" -ForegroundColor $successColor
Write-Host ""
Write-Host "系统各服务端口:" -ForegroundColor $infoColor
Write-Host "[1] 量化交易系统前端: http://localhost:8080" -ForegroundColor $infoColor
Write-Host "[2] 监控驾驶舱: http://localhost:6009" -ForegroundColor $infoColor
Write-Host "[3] 主API服务: http://localhost:8000" -ForegroundColor $infoColor
Write-Host "[4] 其他API服务:" -ForegroundColor $infoColor
Write-Host "   - 回测API: 8001" -ForegroundColor $infoColor
Write-Host "   - 风险管理API: 8002" -ForegroundColor $infoColor
Write-Host "   - 交易信号API: 8004" -ForegroundColor $infoColor
Write-Host "   - 市场数据API: 8005" -ForegroundColor $infoColor
Write-Host "   - WebSocket服务: 8765" -ForegroundColor $infoColor
Write-Host "   - WebSocket监控服务: http://localhost:8767" -ForegroundColor $infoColor
Write-Host ""
Write-Host "注意:" -ForegroundColor $warningColor
Write-Host "- 首次加载可能需要30-60秒" -ForegroundColor $warningColor
Write-Host "- 如果页面加载失败，请等待1分钟后刷新页面" -ForegroundColor $warningColor
Write-Host "- 监控驾驶舱需要主API服务(8000)才能正常工作" -ForegroundColor $warningColor
Write-Host ""
Write-Host "要停止所有服务，请运行 stop-services.ps1 脚本" -ForegroundColor $infoColor