#!/usr/bin/env python3
"""
测试修复后的API端点
"""

import requests
import json

def test_fixed_apis():
    """测试修复后的API端点"""
    
    print("🔍 测试修复后的API端点...")
    print("=" * 60)
    
    # 获取认证token
    print("\n1. 🔐 获取认证令牌...")
    try:
        auth_response = requests.post('http://localhost:8000/api/v1/auth/token', 
                                     json={'username': 'admin', 'password': 'admin123'})
        
        if auth_response.status_code != 200:
            print(f"❌ 认证失败: {auth_response.status_code}")
            return
        
        token = auth_response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print(f"✅ 认证成功")
        
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return
    
    # 测试修复的API端点
    test_cases = [
        {
            'name': '性能分析',
            'url': '/api/v1/performance/analysis',
            'expected_fields': ['summary', 'issues', 'recommendations']
        },
        {
            'name': 'API统计',
            'url': '/api/v1/performance/api/stats',
            'expected_fields': ['count', 'avg', 'min', 'max']
        },
        {
            'name': '内存使用情况',
            'url': '/api/v1/performance/memory/usage',
            'expected_fields': ['virtual', 'swap', 'process']
        },
        {
            'name': '内存分析',
            'url': '/api/v1/performance/memory/analysis',
            'expected_fields': ['summary', 'recommendations', 'warnings']
        }
    ]
    
    print(f"\n2. 📊 测试 {len(test_cases)} 个修复的API端点...")
    
    all_success = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n   {i:2d}. 测试 {test_case['name']}...")
        
        try:
            response = requests.get(f"http://localhost:8000{test_case['url']}", headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success'):
                    api_data = data.get('data', {})
                    
                    # 检查预期字段
                    missing_fields = []
                    for field in test_case['expected_fields']:
                        if field not in api_data:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        print(f"      ❌ 缺少字段: {missing_fields}")
                        all_success = False
                    else:
                        print(f"      ✅ 所有字段完整")
                        
                        # 显示关键数据
                        if test_case['name'] == '性能分析':
                            summary = api_data.get('summary', {})
                            print(f"         性能评分: {summary.get('performance_score', 'N/A')}")
                            print(f"         CPU使用率: {summary.get('cpu_usage', 'N/A'):.1f}%")
                            print(f"         内存使用率: {summary.get('memory_usage', 'N/A'):.1f}%")
                            
                        elif test_case['name'] == 'API统计':
                            print(f"         请求数量: {api_data.get('count', 'N/A')}")
                            print(f"         平均响应时间: {api_data.get('avg', 'N/A')}")
                            
                        elif test_case['name'] == '内存使用情况':
                            virtual = api_data.get('virtual', {})
                            print(f"         虚拟内存使用率: {virtual.get('percent', 'N/A'):.1f}%")
                            
                        elif test_case['name'] == '内存分析':
                            summary = api_data.get('summary', {})
                            print(f"         内存状态: {summary.get('status', 'N/A')}")
                            print(f"         内存使用率: {summary.get('memory_usage_percent', 'N/A'):.1f}%")
                else:
                    print(f"      ❌ API返回失败: {data.get('message', '未知错误')}")
                    all_success = False
            else:
                print(f"      ❌ HTTP错误: {response.status_code}")
                print(f"         响应: {response.text[:200]}...")
                all_success = False
                
        except Exception as e:
            print(f"      ❌ 请求异常: {e}")
            all_success = False
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📋 修复验证测试报告:")
    
    if all_success:
        print("🎉 所有修复都验证通过！")
        print("\n✅ 修复内容:")
        print("   - 性能分析API: 添加了summary字段")
        print("   - API统计API: 修复了空数据时缺少avg字段的问题")
        print("   - 内存使用API: 添加了virtual、swap、process字段")
        print("   - 内存分析API: 添加了summary字段")
        print("\n🚀 现在所有API都返回完整的数据结构！")
    else:
        print("❌ 部分修复验证失败")
        print("   需要进一步检查和修复")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    test_fixed_apis()
