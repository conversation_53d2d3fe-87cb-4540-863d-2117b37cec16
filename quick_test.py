#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证系统设置融合结果
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_api_endpoints():
    """测试关键API端点"""
    print("🔧 测试系统设置融合结果...")
    print("=" * 50)

    # 测试新的API端点
    new_endpoints = [
        ("/api/v1/config/system", "系统配置"),
        ("/api/v1/config/api-keys", "API密钥配置"),
        ("/api/v1/config/system-params", "系统参数"),
        ("/api/v1/config/cache-size", "缓存大小"),
    ]

    print("✅ 测试新的API端点:")
    for endpoint, name in new_endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('success', True):  # 有些API可能没有success字段
                    print(f"   ✅ {name}: 正常")
                else:
                    print(f"   ❌ {name}: 响应错误 - {data.get('message', 'Unknown')}")
            else:
                print(f"   ❌ {name}: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: 异常 - {str(e)}")

    # 测试已废弃的API端点
    old_endpoints = [
        ("/api/v1/unified-config/system", "旧系统配置"),
        ("/api/v1/unified-config/api-keys", "旧API密钥配置"),
    ]

    print("\n🚫 测试已废弃的API端点:")
    for endpoint, name in old_endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            if response.status_code == 404:
                print(f"   ✅ {name}: 已正确移除 (404)")
            elif response.status_code == 405:
                print(f"   ⚠️ {name}: 方法不允许 (405) - 路径可能仍存在")
            else:
                print(f"   ❌ {name}: 仍然可访问 (HTTP {response.status_code})")
        except Exception as e:
            print(f"   ✅ {name}: 连接失败，已移除")

    print("\n📊 测试完成!")

def test_frontend_routes():
    """测试前端路由"""
    print("\n🌐 前端页面访问测试:")
    print("请手动验证以下页面:")
    print("   - 系统设置: http://localhost:8080/#/settings (仅管理员)")
    print("   - 个人设置: http://localhost:8080/#/user-settings (所有用户)")
    print("   - 统一配置管理页面应该已被移除")

if __name__ == "__main__":
    print("系统设置融合快速测试")
    print("测试新API端点和验证旧端点移除")
    print()

    # 检查后端服务
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            exit(1)
    except:
        print("❌ 无法连接后端服务")
        exit(1)

    test_api_endpoints()
    test_frontend_routes()

    print("\n🎉 快速测试完成!")
    print("如需详细测试，请运行: python test_settings_integration.py")
