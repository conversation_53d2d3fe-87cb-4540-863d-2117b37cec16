# 八戒量子交易驾驶舱 - 仪表盘页面

import dash
from dash import html, dcc
from dash.dependencies import Input, Output, State
import plotly.graph_objects as go
from datetime import datetime, timedelta
import numpy as np
import logging

# 导入告警面板组件
from utils.dashboard.components.alerts_dashboard_panel import create_alerts_dashboard_panel, register_alerts_panel_callbacks

logger = logging.getLogger(__name__)

def create_dashboard_page():
    """创建仪表盘页面 - 量子科技美学风格"""

    return html.Div(
        id='page-dashboard',
        className='page',
        children=[
            # 确保标题不被导航栏挡住
            html.Div([
                html.H2("八戒量子交易驾驶舱", className="page-title quantum-title")
            ], className="dashboard-header", style={"marginTop": "80px", "paddingTop": "20px"}),

            # 仪表盘定制按钮
            html.Button(
                html.I(className="fas fa-sliders-h"),
                id="dashboard-customize-btn",
                title="自定义仪表盘",
                style={
                    "position": "fixed",
                    "zIndex": 1100,
                    "bottom": "30px",  # 修改为bottom而不是top，放置在底部
                    "right": "30px",
                    "width": "48px",
                    "height": "48px",
                    "backgroundColor": "rgba(0, 10, 30, 0.8)",
                    "color": "#00f7ff",
                    "border": "2px solid #00f7ff",
                    "borderRadius": "50%",
                    "display": "flex",
                    "alignItems": "center",
                    "justifyContent": "center",
                    "cursor": "pointer",
                    "boxShadow": "0 0 20px rgba(0, 247, 255, 0.6)",
                    "transition": "all 0.3s ease"
                }
            ),

            # 定制面板
            html.Div(
                id="dashboard-customizer-panel",
                children=[
                    html.H3("仪表盘定制", className="panel-section-title"),

                    # 模块排序区域
                    html.Div(
                        className="customizer-section",
                        children=[
                            html.H4("模块顺序", className="section-subtitle"),
                            html.Div(
                                id="dashboard-sections-drag-container",
                                className="drag-container",
                                children=[
                                    html.Div(
                                        id="drag-item-market-overview",
                                        className="quantum-drag-item drag-item",
                                        draggable="true",
                                        children=[
                                            html.I(className="fas fa-grip-lines mr-2 sortable-handle"),
                                            html.Span("市场概览", className="draggable-item-title")
                                        ]
                                    ),
                                    html.Div(
                                        id="drag-item-price-chart",
                                        className="quantum-drag-item drag-item",
                                        draggable="true",
                                        children=[
                                            html.I(className="fas fa-grip-lines mr-2 sortable-handle"),
                                            html.Span("价格走势", className="draggable-item-title")
                                        ]
                                    ),
                                    html.Div(
                                        id="drag-item-market-depth",
                                        className="quantum-drag-item drag-item",
                                        draggable="true",
                                        children=[
                                            html.I(className="fas fa-grip-lines mr-2 sortable-handle"),
                                            html.Span("市场深度", className="draggable-item-title")
                                        ]
                                    ),
                                    html.Div(
                                        id="drag-item-trading-signals",
                                        className="quantum-drag-item drag-item",
                                        draggable="true",
                                        children=[
                                            html.I(className="fas fa-grip-lines mr-2 sortable-handle"),
                                            html.Span("交易信号", className="draggable-item-title")
                                        ]
                                    ),
                                    # 添加告警通知面板到可拖拽项
                                    html.Div(
                                        id="drag-item-alerts-panel",
                                        className="quantum-drag-item drag-item",
                                        draggable="true",
                                        children=[
                                            html.I(className="fas fa-grip-lines mr-2 sortable-handle"),
                                            html.Span("告警通知中心", className="draggable-item-title")
                                        ]
                                    )
                                ]
                            )
                        ]
                    ),

                    # 模块显示/隐藏控制
                    html.Div(
                        className="customizer-section",
                        children=[
                            html.H4("模块显示", className="section-subtitle"),
                            html.Div(
                                id="dashboard-sections-checklist",
                                className="quantum-checklist",
                                children=[
                                    html.Div(
                                        className="custom-control custom-switch",
                                        children=[
                                            dcc.Checklist(
                                                id="visibility-market-overview",
                                                options=[{"label": "市场概览", "value": "market-overview"}],
                                                value=["market-overview"],
                                                className="custom-control-input"
                                            )
                                        ]
                                    ),
                                    html.Div(
                                        className="custom-control custom-switch",
                                        children=[
                                            dcc.Checklist(
                                                id="visibility-price-chart",
                                                options=[{"label": "价格走势", "value": "price-chart"}],
                                                value=["price-chart"],
                                                className="custom-control-input"
                                            )
                                        ]
                                    ),
                                    html.Div(
                                        className="custom-control custom-switch",
                                        children=[
                                            dcc.Checklist(
                                                id="visibility-market-depth",
                                                options=[{"label": "市场深度", "value": "market-depth"}],
                                                value=["market-depth"],
                                                className="custom-control-input"
                                            )
                                        ]
                                    ),
                                    html.Div(
                                        className="custom-control custom-switch",
                                        children=[
                                            dcc.Checklist(
                                                id="visibility-trading-signals",
                                                options=[{"label": "交易信号", "value": "trading-signals"}],
                                                value=["trading-signals"],
                                                className="custom-control-input"
                                            )
                                        ]
                                    ),
                                    # 添加告警通知面板的可见性控制
                                    html.Div(
                                        className="custom-control custom-switch",
                                        children=[
                                            dcc.Checklist(
                                                id="visibility-alerts-panel",
                                                options=[{"label": "告警通知中心", "value": "alerts-panel"}],
                                                value=["alerts-panel"],
                                                className="custom-control-input"
                                            )
                                        ]
                                    )
                                ]
                            )
                        ]
                    ),

                    # 保存按钮
                    html.Div(
                        className="action-buttons",
                        children=[
                            html.Button(
                                "保存设置",
                                id="save-dashboard-layout",
                                className="save-btn"
                            ),
                            html.Button(
                                "重置",
                                id="reset-dashboard-layout",
                                className="reset-btn"
                            )
                        ]
                    )
                ]
            ),

            # 添加定时更新组件，确保图表能正确更新
            dcc.Interval(
                id='interval-component',
                interval=5000,  # 每5秒更新一次
                n_intervals=0
            ),

            html.Div([
                # 市场概览部分 - 霓虹风格
                html.Div([
                    html.H3("市场概览", className="section-title neon-blue",
                          style={"paddingTop": "10px", "marginTop": "10px", "zIndex": "60", "position": "relative"}),
                    html.Div([
                        html.Div([
                            html.Span("当前价格", className="label neon-label"),
                            html.Span(id="price-value", className="value highlight neon-value")
                        ], className="metric-item quantum-panel"),
                        html.Div([
                            html.Span("24h成交量", className="label neon-label"),
                            html.Span(id="volume-value", className="value neon-value")
                        ], className="metric-item quantum-panel"),
                        html.Div([
                            html.Span("资金费率", className="label neon-label"),
                            html.Span(id="funding-rate-value", className="value neon-value")
                        ], className="metric-item quantum-panel"),
                        html.Div([
                            html.Span("24h涨跌幅", className="label neon-label"),
                            html.Span(id="price-change-value", className="value neon-value")
                        ], className="metric-item quantum-panel"),
                        html.Div([
                            html.Span("最新更新", className="label neon-label"),
                            html.Span(id="last-update-value", className="value")
                        ], className="metric-item quantum-panel")
                    ], className="metrics-container")
                ], id="market-overview-section", className="dashboard-section"),

                # K线图部分
                html.Div([
                    html.H3("价格走势", className="section-title neon-pink",
                          style={"paddingTop": "10px", "marginTop": "15px", "zIndex": "60", "position": "relative"}),
                    html.Div([
                        dcc.Graph(
                            id="price-chart",
                            config={
                                'displayModeBar': True,
                                'scrollZoom': True,
                                'modeBarButtonsToRemove': ['autoScale2d', 'select2d', 'lasso2d']
                            },
                            className="quantum-chart",
                            style={"height": "400px", "zIndex": "55"}
                        ),
                        html.Div([
                            dcc.RadioItems(
                                id="timeframe-selector",
                                options=[
                                    {'label': '1秒', 'value': '1s'},
                                    {'label': '1分钟', 'value': '1m'},
                                    {'label': '3分钟', 'value': '3m'},
                                    {'label': '5分钟', 'value': '5m'},
                                    {'label': '15分钟', 'value': '15m'},
                                    {'label': '30分钟', 'value': '30m'},
                                    {'label': '1小时', 'value': '1h'},
                                    {'label': '2小时', 'value': '2h'},
                                    {'label': '4小时', 'value': '4h'},
                                    {'label': '6小时', 'value': '6h'},
                                    {'label': '8小时', 'value': '8h'},
                                    {'label': '12小时', 'value': '12h'},
                                    {'label': '1天', 'value': '1d'},
                                    {'label': '3天', 'value': '3d'},
                                    {'label': '1周', 'value': '1w'},
                                    {'label': '1月', 'value': '1M'},
                                ],
                                value='1h',
                                labelStyle={'display': 'inline-block', 'marginRight': '10px'},
                                className="timeframe-selector"
                            )
                        ], className="chart-controls")
                    ], className="chart-container", style={"minHeight": "450px"})
                ], id="price-chart-section", className="chart-section"),

                # 市场深度图
                html.Div([
                    html.H3("市场深度", className="section-title neon-blue",
                          style={"paddingTop": "10px", "marginTop": "15px", "zIndex": "60", "position": "relative"}),
                    dcc.Graph(
                        id="depth-chart",
                        config={
                            'displayModeBar': True,
                            'modeBarButtonsToRemove': ['autoScale2d', 'select2d', 'lasso2d']
                        },
                        className="quantum-chart",
                        style={"height": "350px"}
                    )
                ], id="market-depth-section", className="dashboard-section"),

                # 交易信号
                html.Div([
                    html.H3("交易信号", className="section-title neon-pink",
                          style={"paddingTop": "10px", "marginTop": "15px", "zIndex": "60", "position": "relative"}),
                    html.Div(
                        id="signals-container",
                        className="signals-container"
                    )
                ], id="trading-signals-section", className="dashboard-section"),

                # 告警通知面板
                create_alerts_dashboard_panel()

            ], className="dashboard-content"),

            # 添加调试脚本
            html.Script("""
                document.addEventListener('DOMContentLoaded', function() {
                    console.log("【调试信息】DOM加载完成");

                    // 检查定制按钮
                    var customizeBtn = document.getElementById('dashboard-customize-btn');
                    if (customizeBtn) {
                        console.log("【调试信息】找到定制按钮", {
                            visible: customizeBtn.offsetWidth > 0 && customizeBtn.offsetHeight > 0,
                            position: {
                                top: customizeBtn.offsetTop + 'px',
                                left: customizeBtn.offsetLeft + 'px'
                            },
                            zIndex: getComputedStyle(customizeBtn).zIndex,
                            display: getComputedStyle(customizeBtn).display
                        });

                        // 确保按钮可点击
                        customizeBtn.addEventListener('click', function() {
                            console.log("【调试信息】按钮被点击");
                        });
                    } else {
                        console.error("【调试信息】未找到定制按钮");
                    }

                    // 检查定制面板
                    var customizePanel = document.getElementById('dashboard-customizer-panel');
                    if (customizePanel) {
                        console.log("【调试信息】找到定制面板");
                    } else {
                        console.error("【调试信息】未找到定制面板");
                    }
                });
            """)
        ]
    )

def register_dashboard_callbacks(app):
    """注册仪表盘回调函数"""
    if not app:
        return

    # 注册告警面板组件的回调
    register_alerts_panel_callbacks(app)

    @app.callback(
        [
            Output("price-value", "children"),
            Output("volume-value", "children"),
            Output("funding-rate-value", "children"),
            Output("price-change-value", "children"),
            Output("last-update-value", "children")
        ],
        [Input("interval-component", "n_intervals")]
    )
    def update_market_data(n_intervals):
        """更新市场数据"""
        logger.info(f"Updating market data, interval: {n_intervals}")

        try:
            # 从市场数据服务获取最新数据
            from core.market_data_service import MarketDataService
            market_service = MarketDataService()

            # 获取BTC/USDT的最新价格
            ticker = market_service.get_ticker("BTC/USDT")

            # 格式化数据
            current_price = f"${ticker['price']:.2f}"

            # 获取24小时成交量
            volume_24h = f"${ticker.get('volume', 0):.2f}"

            # 获取资金费率（如果有）
            funding_rate = f"{ticker.get('funding_rate', 0):.4f}%"

            # 获取24小时涨跌幅（如果有）
            change_24h = f"{ticker.get('change_24h', 0):.2f}%"

            # 更新时间
            last_update = datetime.fromtimestamp(ticker['timestamp']/1000).strftime("%H:%M:%S") if 'timestamp' in ticker else datetime.now().strftime("%H:%M:%S")

            logger.info(f"获取到最新市场数据: 价格={current_price}, 成交量={volume_24h}, 资金费率={funding_rate}, 涨跌幅={change_24h}")
        except Exception as e:
            logger.error(f"获取市场数据失败: {e}")
            # 如果获取失败，返回空数据
            current_price = "$0.00"
            volume_24h = "$0.00"
            funding_rate = "0.0000%"
            change_24h = "0.00%"
            last_update = datetime.now().strftime("%H:%M:%S")

        return current_price, volume_24h, funding_rate, change_24h, last_update

    @app.callback(
        Output("price-chart", "figure"),
        [
            Input("interval-component", "n_intervals"),
            Input("timeframe-selector", "value")
        ]
    )
    def update_price_chart(n_intervals, timeframe):
        """更新价格图表"""
        logger.info(f"Updating price chart, interval: {n_intervals}, timeframe: {timeframe}")

        try:
            # 从市场数据服务获取K线数据
            from core.market_data_service import MarketDataService
            market_service = MarketDataService()

            # 根据时间框架确定获取数量
            if timeframe == '5m':
                limit = 60  # 5小时
            elif timeframe == '15m':
                limit = 48  # 12小时
            elif timeframe == '1h':
                limit = 48  # 2天
            elif timeframe == '4h':
                limit = 42  # 一周
            else:  # 1d
                limit = 30  # 一个月

            # 获取K线数据
            klines = market_service.get_klines("BTC/USDT", timeframe, limit=limit)

            # 提取数据
            timestamps = [datetime.fromtimestamp(k['timestamp']/1000) for k in klines]
            opens = [k['open'] for k in klines]
            highs = [k['high'] for k in klines]
            lows = [k['low'] for k in klines]
            closes = [k['close'] for k in klines]

            logger.info(f"获取到 {len(klines)} 根K线数据")
        except Exception as e:
            logger.error(f"获取K线数据失败: {e}")
            # 如果获取失败，生成空数据
            end_time = datetime.now()

            # 根据时间框架确定时间间隔
            if timeframe == '5m':
                n_points = 60
                time_delta = 5  # 5分钟
            elif timeframe == '15m':
                n_points = 48
                time_delta = 15  # 15分钟
            elif timeframe == '1h':
                n_points = 48
                time_delta = 60  # 1小时
            elif timeframe == '4h':
                n_points = 42
                time_delta = 240  # 4小时
            else:  # 1d
                n_points = 30
                time_delta = 1440  # 1天

            # 生成时间序列
            timestamps = [(end_time - timedelta(minutes=i*time_delta)) for i in range(n_points)]
            timestamps.reverse()  # 从早到晚排序

            # 生成空数据
            opens = [0] * n_points
            highs = [0] * n_points
            lows = [0] * n_points
            closes = [0] * n_points

        # 创建蜡烛图
        fig = go.Figure()

        # 添加K线图
        fig.add_trace(go.Candlestick(
            x=timestamps,
            open=opens,
            high=highs,
            low=lows,
            close=closes,
            increasing_line_color='#00F7FF',  # 上涨蓝色 - 量子风格
            decreasing_line_color='#FF00E4',  # 下跌粉色 - 量子风格
            name='BTC/USDT'
        ))

        # 添加均线
        ma7 = np.convolve(closes, np.ones(7)/7, mode='valid')
        ma7_padded = np.pad(ma7, (6, 0), 'constant', constant_values=np.nan)

        ma25 = np.convolve(closes, np.ones(25)/25, mode='valid')
        ma25_padded = np.pad(ma25, (24, 0), 'constant', constant_values=np.nan)

        fig.add_trace(go.Scatter(
            x=timestamps,
            y=ma7_padded,
            line=dict(color='rgba(0, 247, 255, 0.8)', width=1.5),
            name='MA7'
        ))

        fig.add_trace(go.Scatter(
            x=timestamps,
            y=ma25_padded,
            line=dict(color='rgba(255, 0, 228, 0.8)', width=1.5),
            name='MA25'
        ))

        # 设置图表布局 - 量子科技风格
        fig.update_layout(
            title=f'BTC/USDT {timeframe} K线图',
            xaxis_title='时间',
            yaxis_title='价格 (USDT)',
            template='plotly_dark',
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(
                family="Arial, monospace",
                size=12,
                color="#cccccc"
            ),
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="center",
                x=0.5,
                font=dict(size=10)
            ),
            xaxis=dict(
                showgrid=True,
                gridcolor='rgba(80, 80, 100, 0.3)'
            ),
            yaxis=dict(
                showgrid=True,
                gridcolor='rgba(80, 80, 100, 0.3)'
            ),
            margin=dict(l=10, r=10, t=50, b=10)
        )

        return fig

    @app.callback(
        Output("depth-chart", "figure"),
        [Input("interval-component", "n_intervals")]
    )
    def update_depth_chart(n_intervals):
        """更新深度图表"""
        logger.info(f"Updating depth chart, interval: {n_intervals}")

        try:
            # 从市场数据服务获取最新价格
            from core.market_data_service import MarketDataService
            market_service = MarketDataService()

            # 获取BTC/USDT的最新价格
            ticker = market_service.get_ticker("BTC/USDT")
            current_price = ticker['price']

            # 在真实实现中，应该从交易所API获取深度数据
            # 由于深度数据需要特殊的API调用，这里使用基于当前价格的模拟数据
            # 在实际实现中，应该使用交易所的orderbook API

            price_range = 0.05  # 价格范围 ±5%

            # 生成卖单价格和数量
            ask_prices = np.linspace(current_price, current_price * (1 + price_range), 50)
            ask_sizes = []
            for i in range(50):
                # 模拟卖单深度，越远离当前价格，深度越小
                factor = 1 - ((ask_prices[i] - current_price) / (current_price * price_range))
                size = 10 * np.exp(4 * factor) * (1 + 0.1 * np.random.randn())
                ask_sizes.append(size)

            # 生成买单价格和数量
            bid_prices = np.linspace(current_price * (1 - price_range), current_price, 50)
            bid_sizes = []
            for i in range(50):
                # 模拟买单深度，越远离当前价格，深度越小
                factor = 1 - ((current_price - bid_prices[i]) / (current_price * price_range))
                size = 10 * np.exp(4 * factor) * (1 + 0.1 * np.random.randn())
                bid_sizes.append(size)

            # 累积深度
            ask_cumulative = np.cumsum(ask_sizes)
            bid_cumulative = np.cumsum(bid_sizes[::-1])[::-1]

            logger.info(f"生成深度数据，当前价格: {current_price}")
        except Exception as e:
            logger.error(f"获取深度数据失败: {e}")
            # 如果获取失败，生成空数据
            current_price = 0

            # 生成空数据
            ask_prices = np.linspace(current_price, current_price * 1.05, 50)
            bid_prices = np.linspace(current_price * 0.95, current_price, 50)
            ask_cumulative = np.zeros(50)
            bid_cumulative = np.zeros(50)

        # 创建深度图
        fig = go.Figure()

        # 添加卖单深度
        fig.add_trace(go.Scatter(
            x=ask_prices,
            y=ask_cumulative,
            fill='tozeroy',
            mode='lines',
            line=dict(color='#FF00E4', width=2),
            fillcolor='rgba(255, 0, 228, 0.2)',
            name='卖单'
        ))

        # 添加买单深度
        fig.add_trace(go.Scatter(
            x=bid_prices,
            y=bid_cumulative,
            fill='tozeroy',
            mode='lines',
            line=dict(color='#00F7FF', width=2),
            fillcolor='rgba(0, 247, 255, 0.2)',
            name='买单'
        ))

        # 添加当前价格线
        fig.add_trace(go.Scatter(
            x=[current_price, current_price],
            y=[0, max(max(ask_cumulative), max(bid_cumulative))],
            mode='lines',
            line=dict(color='rgba(255, 255, 255, 0.5)', width=1, dash='dash'),
            name='当前价格'
        ))

        # 设置图表布局 - 量子科技风格
        fig.update_layout(
            title='市场深度图',
            xaxis_title='价格 (USDT)',
            yaxis_title='累积数量 (BTC)',
            template='plotly_dark',
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(
                family="Arial, monospace",
                size=12,
                color="#cccccc"
            ),
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="center",
                x=0.5,
                font=dict(size=10)
            ),
            xaxis=dict(
                showgrid=True,
                gridcolor='rgba(80, 80, 100, 0.3)'
            ),
            yaxis=dict(
                showgrid=True,
                gridcolor='rgba(80, 80, 100, 0.3)'
            ),
            margin=dict(l=10, r=10, t=50, b=10)
        )

        return fig

    @app.callback(
        Output("signals-container", "children"),
        [Input("interval-component", "n_intervals")]
    )
    def update_trading_signals(n_intervals):
        """更新交易信号"""
        logger.info(f"Updating trading signals, interval: {n_intervals}")

        try:
            # 从信号API获取最新信号
            import requests

            # 尝试从信号API获取数据
            try:
                response = requests.get("http://localhost:8000/api/v1/signals/realtime", timeout=2)
                if response.status_code == 200:
                    data = response.json()
                    if 'data' in data and isinstance(data['data'], list):
                        signal_data = []
                        for signal in data['data']:
                            # 转换为需要的格式
                            signal_item = {
                                "type": signal.get("action", "未知"),
                                "symbol": signal.get("symbol", "未知"),
                                "strength": signal.get("confidence", 0) * 100,  # 假设置信度是0-1的浮点数
                                "timeframe": signal.get("timeframe", "未知"),
                                "time": datetime.fromtimestamp(signal.get("timestamp", 0)/1000).strftime("%H:%M:%S")
                            }
                            signal_data.append(signal_item)

                        logger.info(f"从信号API获取到 {len(signal_data)} 个信号")
                    else:
                        raise ValueError("响应数据格式不正确")
                else:
                    raise ValueError(f"响应状态码: {response.status_code}")
            except Exception as e:
                logger.warning(f"从信号API获取数据失败: {e}")
                # 如果API调用失败，尝试从数据库获取
                try:
                    # 导入数据库模型
                    from backend.app.models.signal import Signal
                    from backend.app.db.database import SessionLocal

                    # 创建数据库会话
                    db = SessionLocal()

                    # 查询最新的信号
                    signals = db.query(Signal).order_by(Signal.timestamp.desc()).limit(5).all()

                    # 转换为需要的格式
                    signal_data = []
                    for signal in signals:
                        signal_item = {
                            "type": signal.action,
                            "symbol": signal.symbol,
                            "strength": signal.confidence * 100 if signal.confidence else 0,
                            "timeframe": signal.timeframe,
                            "time": signal.timestamp.strftime("%H:%M:%S") if signal.timestamp else "未知"
                        }
                        signal_data.append(signal_item)

                    # 关闭数据库会话
                    db.close()

                    logger.info(f"从数据库获取到 {len(signal_data)} 个信号")
                except Exception as db_error:
                    logger.error(f"从数据库获取信号失败: {db_error}")
                    # 如果数据库调用也失败，使用空数据
                    signal_data = []
        except Exception as e:
            logger.error(f"获取交易信号失败: {e}")
            # 如果获取失败，使用空数据
            signal_data = []

        # 创建信号卡片
        signal_cards = []
        for signal in signal_data:
            # 根据信号类型设置颜色
            signal_class = ""
            if signal["type"] == "买入":
                signal_class = "buy-signal"
            elif signal["type"] == "卖出":
                signal_class = "sell-signal"

            card = html.Div(
                className=f"signal-card {signal_class}",
                children=[
                    html.Div(signal["type"], className="signal-type"),
                    html.Div(signal["symbol"], className="signal-symbol"),
                    html.Div([
                        html.Span("信号强度: "),
                        html.Span(f"{signal['strength']}%", className="signal-strength-value")
                    ], className="signal-info"),
                    html.Div([
                        html.Span("时间框架: "),
                        html.Span(signal["timeframe"])
                    ], className="signal-info"),
                    html.Div(signal["time"], className="signal-time")
                ]
            )
            signal_cards.append(card)

        return signal_cards