<template>
  <div class="metrics-card">
    <div v-if="loading" class="loading-overlay">
      <i class="el-icon-loading"></i>
      <p>加载数据中...</p>
    </div>

    <div v-else-if="error" class="error-message">
      <i class="el-icon-warning"></i>
      <p>{{ error }}</p>
    </div>

    <div v-else-if="!metricsData || Object.keys(metricsData).length === 0" class="empty-data">
      <i class="el-icon-data-line"></i>
      <p>暂无指标数据</p>
    </div>

    <div v-else class="metrics-grid">
      <!-- 总体评分 -->
      <div class="metric-card overall-score" v-if="metricsData.overall_score !== undefined">
        <div class="metric-value" :class="getScoreClass(metricsData.overall_score)">
          {{ formatScore(metricsData.overall_score) }}
        </div>
        <div class="metric-label">总体评分</div>
        <div class="metric-trend" v-if="metricsData.trend">
          <i :class="getTrendIcon(metricsData.trend)"></i>
          <span>{{ getTrendText(metricsData.trend) }}</span>
        </div>
      </div>

      <!-- 完整性评分 -->
      <div class="metric-card" v-if="metricsData.completeness_score !== undefined">
        <div class="metric-icon">
          <i class="el-icon-document-checked"></i>
        </div>
        <div class="metric-content">
          <div class="metric-label">完整性</div>
          <div class="metric-value" :class="getScoreClass(metricsData.completeness_score)">
            {{ formatScore(metricsData.completeness_score) }}
          </div>
          <el-progress
            :percentage="metricsData.completeness_score"
            :status="getProgressStatus(metricsData.completeness_score)"
            :stroke-width="8"
          ></el-progress>
        </div>
      </div>

      <!-- 连续性评分 -->
      <div class="metric-card" v-if="metricsData.continuity_score !== undefined">
        <div class="metric-icon">
          <i class="el-icon-connection"></i>
        </div>
        <div class="metric-content">
          <div class="metric-label">连续性</div>
          <div class="metric-value" :class="getScoreClass(metricsData.continuity_score)">
            {{ formatScore(metricsData.continuity_score) }}
          </div>
          <el-progress
            :percentage="metricsData.continuity_score"
            :status="getProgressStatus(metricsData.continuity_score)"
            :stroke-width="8"
          ></el-progress>
        </div>
      </div>

      <!-- 准确性评分 -->
      <div class="metric-card" v-if="metricsData.accuracy_score !== undefined">
        <div class="metric-icon">
          <i class="el-icon-aim"></i>
        </div>
        <div class="metric-content">
          <div class="metric-label">准确性</div>
          <div class="metric-value" :class="getScoreClass(metricsData.accuracy_score)">
            {{ formatScore(metricsData.accuracy_score) }}
          </div>
          <el-progress
            :percentage="metricsData.accuracy_score"
            :status="getProgressStatus(metricsData.accuracy_score)"
            :stroke-width="8"
          ></el-progress>
        </div>
      </div>

      <!-- 一致性评分 -->
      <div class="metric-card" v-if="metricsData.consistency_score !== undefined">
        <div class="metric-icon">
          <i class="el-icon-finished"></i>
        </div>
        <div class="metric-content">
          <div class="metric-label">一致性</div>
          <div class="metric-value" :class="getScoreClass(metricsData.consistency_score)">
            {{ formatScore(metricsData.consistency_score) }}
          </div>
          <el-progress
            :percentage="metricsData.consistency_score"
            :status="getProgressStatus(metricsData.consistency_score)"
            :stroke-width="8"
          ></el-progress>
        </div>
      </div>

      <!-- 时效性评分 -->
      <div class="metric-card" v-if="metricsData.timeliness_score !== undefined">
        <div class="metric-icon">
          <i class="el-icon-timer"></i>
        </div>
        <div class="metric-content">
          <div class="metric-label">时效性</div>
          <div class="metric-value" :class="getScoreClass(metricsData.timeliness_score)">
            {{ formatScore(metricsData.timeliness_score) }}
          </div>
          <el-progress
            :percentage="metricsData.timeliness_score"
            :status="getProgressStatus(metricsData.timeliness_score)"
            :stroke-width="8"
          ></el-progress>
        </div>
      </div>

      <!-- 数据延迟 -->
      <div class="metric-card" v-if="metricsData.data_delay !== undefined">
        <div class="metric-icon">
          <i class="el-icon-time"></i>
        </div>
        <div class="metric-content">
          <div class="metric-label">数据延迟</div>
          <div class="metric-value" :class="getDelayClass(metricsData.data_delay)">
            {{ formatDelay(metricsData.data_delay) }}
          </div>
        </div>
      </div>

      <!-- 最后更新时间 -->
      <div class="metric-card last-update" v-if="metricsData.timestamp">
        <div class="metric-icon">
          <i class="el-icon-refresh"></i>
        </div>
        <div class="metric-content">
          <div class="metric-label">最后更新</div>
          <div class="metric-value">
            {{ formatDateTime(metricsData.timestamp) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'MetricsCardComponent',
  props: {
    dataSource: {
      type: Object,
      required: true
    },
    metrics: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      error: null,
      metricsData: {}
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      this.error = null;

      try {
        // 克隆dataSource以避免修改原始对象
        const dataSourceConfig = JSON.parse(JSON.stringify(this.dataSource));

        console.log('MetricsCardComponent - 原始dataSource:', this.dataSource);

        // 直接使用fetchQualityMetrics方法获取数据
        // 这样可以确保使用正确的参数格式
        const marketDataUrl = process.env.VUE_APP_MARKET_DATA_URL || 'http://localhost:8005';

        // 从URL中提取数据源ID
        let sourceId = 18; // 默认使用ID 18，这是从日志中看到的有效ID
        if (dataSourceConfig.params && dataSourceConfig.params.source_id) {
          sourceId = dataSourceConfig.params.source_id;
        }

        // 从URL中提取交易所
        let exchange = 'binance';
        if (dataSourceConfig.params && dataSourceConfig.params.exchange) {
          exchange = dataSourceConfig.params.exchange;
        }

        // 从URL中提取交易对
        let symbol = 'BTCUSDT';
        if (dataSourceConfig.params && dataSourceConfig.params.symbol) {
          symbol = dataSourceConfig.params.symbol;
          // 确保使用不带斜杠的格式
          if (symbol.includes('/')) {
            symbol = symbol.replace('/', '');
          }
        }

        // 从URL中提取时间周期
        let timeframe = '1h';
        if (dataSourceConfig.params && dataSourceConfig.params.timeframe) {
          timeframe = dataSourceConfig.params.timeframe;
        }

        // 构建请求参数
        const params = {
          source_id: sourceId,
          exchange: exchange,
          symbol: symbol,
          timeframe: timeframe
        };

        console.log('MetricsCardComponent - 修正后的请求参数:', params);

        // 发送请求
        const response = await axios.get(`${marketDataUrl}/api/v1/monitoring/quality-metrics`, { params });

        if (response.data) {
          // 处理不同的数据格式
          if (response.data.data) {
            this.metricsData = response.data.data;
          } else {
            this.metricsData = response.data;
          }

          // 确保所有需要的字段都存在
          this.ensureMetricsFields();
        } else {
          this.metricsData = {};
        }
      } catch (error) {
        console.error('获取指标数据失败:', error);
        this.error = '获取数据失败: ' + (error.response?.data?.message || error.message);
        this.metricsData = {};
      } finally {
        this.loading = false;
      }
    },

    ensureMetricsFields() {
      // 确保所有需要的字段都存在，如果不存在则设置为默认值
      const requiredFields = [
        'overall_score',
        'completeness_score',
        'continuity_score',
        'accuracy_score',
        'consistency_score',
        'timeliness_score'
      ];

      requiredFields.forEach(field => {
        if (this.metricsData[field] === undefined) {
          // 如果字段不存在，检查是否有替代字段
          const alternativeFields = {
            'overall_score': ['quality_score', 'overall_quality'],
            'completeness_score': ['completeness', 'data_completeness'],
            'continuity_score': ['continuity', 'data_continuity'],
            'accuracy_score': ['accuracy', 'data_accuracy'],
            'consistency_score': ['consistency', 'data_consistency'],
            'timeliness_score': ['timeliness', 'data_timeliness']
          };

          // 尝试使用替代字段
          if (alternativeFields[field]) {
            for (const alt of alternativeFields[field]) {
              if (this.metricsData[alt] !== undefined) {
                this.metricsData[field] = this.metricsData[alt];
                break;
              }
            }
          }
        }
      });
    },

    formatScore(score) {
      if (score === undefined || score === null) return '暂无数据';
      return score.toFixed(1);
    },

    formatDelay(delay) {
      if (delay === undefined || delay === null) return '暂无数据';

      // 假设delay是秒数
      if (delay < 60) {
        return `${delay}秒`;
      } else if (delay < 3600) {
        return `${Math.floor(delay / 60)}分钟`;
      } else {
        return `${Math.floor(delay / 3600)}小时`;
      }
    },

    formatDateTime(timestamp) {
      if (!timestamp) return '暂无数据';

      try {
        const date = new Date(timestamp);
        return date.toLocaleString();
      } catch (e) {
        return timestamp;
      }
    },

    getScoreClass(score) {
      if (score === undefined || score === null) return '';
      if (score >= 90) return 'score-excellent';
      if (score >= 75) return 'score-good';
      if (score >= 60) return 'score-average';
      return 'score-poor';
    },

    getDelayClass(delay) {
      if (delay === undefined || delay === null) return '';
      if (delay < 60) return 'delay-good';
      if (delay < 300) return 'delay-average';
      return 'delay-poor';
    },

    getProgressStatus(score) {
      if (score === undefined || score === null) return 'info';
      if (score >= 90) return 'success';
      if (score >= 75) return 'warning';
      return 'exception';
    },

    getTrendIcon(trend) {
      switch (trend) {
        case 'up': return 'el-icon-top';
        case 'down': return 'el-icon-bottom';
        case 'stable': return 'el-icon-right';
        default: return 'el-icon-minus';
      }
    },

    getTrendText(trend) {
      switch (trend) {
        case 'up': return '上升趋势';
        case 'down': return '下降趋势';
        case 'stable': return '保持稳定';
        default: return '无趋势数据';
      }
    }
  }
};
</script>

<style scoped>
.metrics-card {
  position: relative;
  height: 100%;
  min-height: 300px;
}

.loading-overlay, .error-message, .empty-data {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(20, 30, 60, 0.7);
  color: #EAEAEA;
  z-index: 1;
}

.loading-overlay i, .error-message i, .empty-data i {
  font-size: 32px;
  margin-bottom: 10px;
}

.error-message i {
  color: #F56C6C;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  height: 100%;
}

.metric-card {
  background-color: rgba(30, 40, 70, 0.5);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  border: 1px solid rgba(0, 247, 255, 0.1);
  transition: all 0.3s ease;
}

.metric-card:hover {
  box-shadow: 0 0 10px rgba(0, 247, 255, 0.2);
  border-color: rgba(0, 247, 255, 0.3);
}

.overall-score {
  grid-column: 1 / -1;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  padding: 20px;
}

.overall-score .metric-value {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 5px;
}

.metric-icon {
  font-size: 24px;
  margin-right: 15px;
  color: #00F7FF;
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 14px;
  color: #AAAAAA;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.metric-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #AAAAAA;
}

.metric-trend i {
  margin-right: 5px;
}

.last-update {
  grid-column: 1 / -1;
}

.last-update .metric-value {
  font-size: 16px;
}

/* 评分颜色 */
.score-excellent {
  color: #67C23A;
}

.score-good {
  color: #409EFF;
}

.score-average {
  color: #E6A23C;
}

.score-poor {
  color: #F56C6C;
}

/* 延迟颜色 */
.delay-good {
  color: #67C23A;
}

.delay-average {
  color: #E6A23C;
}

.delay-poor {
  color: #F56C6C;
}

/* 适配暗色主题 */
:deep(.el-progress-bar__outer) {
  background-color: rgba(255, 255, 255, 0.1);
}

:deep(.el-progress-bar__inner) {
  transition: all 0.5s ease;
}

:deep(.el-progress__text) {
  color: #EAEAEA;
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
}
</style>
