#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试CORS的简单FastAPI服务
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import time

app = FastAPI(title="测试CORS的API")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["Content-Type", "Authorization", "X-Requested-With", "Accept"],
    expose_headers=["Content-Length", "X-Process-Time"],
    max_age=86400,
)

# 添加请求计时中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    # 处理OPTIONS预检请求
    if request.method == "OPTIONS":
        response = JSONResponse(content={})
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With, Accept"
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Max-Age"] = "86400"
        return response

    # 处理正常请求
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

@app.get("/api/v1/market/combined-kline")
async def get_combined_kline(symbol: str, timeframe: str, limit: int = 100):
    """
    测试组合K线数据接口
    """
    # 返回模拟数据
    return {
        "success": True,
        "symbol": symbol,
        "timeframe": timeframe,
        "data": [
            [1620000000000, 50000, 51000, 49000, 50500, 100, False],
            [1620003600000, 50500, 52000, 50000, 51500, 150, False],
            [1620007200000, 51500, 53000, 51000, 52500, 200, False],
            [1620010800000, 52500, 54000, 52000, 53500, 250, False],
            [1620014400000, 53500, 55000, 53000, 54500, 300, False],
        ],
        "count": 5,
        "has_current_kline": True
    }

@app.get("/health")
async def health_check():
    """
    健康检查接口
    """
    return {
        "status": "healthy",
        "service": "test-cors"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8003)
