# 交易时间设置功能说明

## 🎯 功能概述

交易时间设置功能允许您为策略配置具体的交易时间窗口，确保策略只在指定的时间段内执行交易操作。这对于不同市场类型和交易策略都非常重要。

## 📍 功能位置

### 前端界面位置
- **路径**: 策略创建向导 → 第2步：配置参数 → 交易时间标签页
- **具体位置**: `/strategy/wizard` → 点击"交易时间"标签

### 界面截图说明
您看到的界面包含以下部分：
1. **策略参数** - 策略特定参数配置
2. **风险管理** - 止损止盈设置
3. **交易时间** - 交易时间控制设置 ⭐

## ⚙️ 功能详细说明

### 🕐 交易时间控制

#### 1. 启用交易时间限制
- **开关控制**: 启用后策略只在指定时间段内执行交易
- **默认状态**: 关闭（策略24小时运行）
- **建议**: 根据市场类型和策略特点决定是否启用

#### 2. 交易时间设置（启用后显示）

**交易开始时间**
- 格式: HH:MM (24小时制)
- 默认: 09:00
- 说明: 策略开始执行交易的时间

**交易结束时间**
- 格式: HH:MM (24小时制)  
- 默认: 17:00
- 说明: 策略停止执行交易的时间

**交易日期选择**
- 选项: 周一至周日
- 默认: 周一到周五
- 说明: 策略执行交易的星期

**避开重要新闻事件**
- 开关控制: 在重要经济数据发布前后暂停交易
- 默认: 启用
- 说明: 避免新闻事件导致的市场剧烈波动

### 🌍 市场时区设置

#### 交易时区
- **北京时间 (UTC+8)** - 默认选项，适合亚洲市场
- **纽约时间 (UTC-5/-4)** - 美国市场时间
- **伦敦时间 (UTC+0/+1)** - 欧洲市场时间
- **东京时间 (UTC+9)** - 日本市场时间
- **协调世界时 (UTC)** - 标准时间基准

#### 市场类型
- **加密货币市场 (24/7)** - 7×24小时交易，无休市时间
- **外汇市场 (周一-周五)** - 周一早上到周六早上连续交易
- **股票市场 (交易时段)** - 通常为工作日的特定时间段
- **期货市场 (扩展时段)** - 有夜盘和日盘，交易时间较长

## 💾 数据存储结构

交易时间设置存储在策略的`parameters`字段中，JSON格式如下：

```json
{
  "trading_schedule": {
    "enable_schedule": false,
    "trading_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
    "start_time": "09:00",
    "end_time": "17:00",
    "avoid_news_events": true,
    "timezone": "Asia/Shanghai",
    "market_type": "crypto"
  }
}
```

## 🔧 技术实现

### 前端实现
- **文件位置**: `frontend/src/views/strategy/StrategyWizard.vue`
- **数据绑定**: `strategyForm.trading_schedule`
- **界面组件**: 量子科技美学风格的表单控件

### 后端存储
- **数据库表**: `strategies`
- **字段**: `parameters` (JSON类型)
- **模型文件**: `backend/app/models/strategy.py`

### API接口
- **创建策略**: `POST /api/v1/strategies`
- **更新策略**: `PUT /api/v1/strategies/{id}`
- **数据传输**: 交易时间设置作为策略参数的一部分传输

## 📋 使用场景示例

### 场景1: 股票市场策略
```json
{
  "enable_schedule": true,
  "trading_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
  "start_time": "09:30",
  "end_time": "15:00",
  "timezone": "America/New_York",
  "market_type": "stock"
}
```

### 场景2: 外汇市场策略
```json
{
  "enable_schedule": true,
  "trading_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
  "start_time": "08:00",
  "end_time": "17:00",
  "timezone": "Europe/London",
  "market_type": "forex"
}
```

### 场景3: 加密货币策略（24/7）
```json
{
  "enable_schedule": false,
  "market_type": "crypto"
}
```

## ⚠️ 重要注意事项

1. **时区一致性**: 确保设置的时区与您的交易所时区一致
2. **市场休市**: 不同市场有不同的休市时间，需要相应配置
3. **新闻事件**: 启用"避开重要新闻事件"可以减少风险
4. **策略逻辑**: 交易时间限制只影响新订单，不影响已有持仓的管理
5. **回测影响**: 交易时间设置也会影响回测结果的准确性

## 🔄 后续优化计划

1. **节假日支持**: 添加各国节假日的自动识别
2. **动态时区**: 支持夏令时的自动调整
3. **新闻日历**: 集成经济日历，自动避开重要事件
4. **多时段支持**: 支持一天内多个交易时段
5. **可视化界面**: 添加时间轴可视化选择器

## 📞 技术支持

如果您在使用交易时间设置功能时遇到问题，请检查：
1. 时区设置是否正确
2. 交易时间是否符合市场规则
3. 策略代码是否正确处理时间限制
4. 后端日志是否有相关错误信息
