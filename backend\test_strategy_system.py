#!/usr/bin/env python3
"""
策略管理系统完整测试脚本
测试所有API端点和功能
"""

import requests
import json
import time
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_api_endpoint(method, endpoint, data=None, expected_status=200):
    """测试API端点"""
    url = f"{API_BASE}{endpoint}"

    try:
        if method.upper() == 'GET':
            response = requests.get(url)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data)
        elif method.upper() == 'DELETE':
            response = requests.delete(url)
        else:
            print(f"❌ 不支持的HTTP方法: {method}")
            return False

        print(f"📡 {method.upper()} {endpoint}")
        print(f"   状态码: {response.status_code}")

        if response.status_code == expected_status:
            try:
                result = response.json()
                if isinstance(result, dict) and result.get('success'):
                    print(f"   ✅ 成功: {result.get('message', '无消息')}")
                    if 'data' in result:
                        if isinstance(result['data'], list):
                            print(f"   📊 返回数据: {len(result['data'])} 条记录")
                        else:
                            print(f"   📊 返回数据: {type(result['data']).__name__}")
                    return True
                else:
                    print(f"   ⚠️  响应: {result}")
                    return True
            except json.JSONDecodeError:
                print(f"   📄 响应: {response.text[:100]}...")
                return True
        else:
            print(f"   ❌ 状态码错误，期望: {expected_status}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data}")
            except:
                print(f"   错误响应: {response.text[:100]}...")
            return False

    except requests.exceptions.ConnectionError:
        print(f"   ❌ 连接失败: 无法连接到 {url}")
        return False
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试策略管理系统")
    print("=" * 50)

    # 测试计数器
    total_tests = 0
    passed_tests = 0

    # 1. 测试策略模板API
    print("\n📋 测试策略模板API")
    print("-" * 30)

    tests = [
        ("GET", "/strategy-templates", None, 200),
        ("GET", "/strategy-templates?type=trend_following", None, 200),
        ("GET", "/strategy-templates?category=dual_ma_cross", None, 200),
    ]

    for method, endpoint, data, expected in tests:
        total_tests += 1
        if test_api_endpoint(method, endpoint, data, expected):
            passed_tests += 1

    # 2. 测试策略管理API
    print("\n📈 测试策略管理API")
    print("-" * 30)

    tests = [
        ("GET", "/strategies", None, 200),
        ("GET", "/strategies/stats", None, 200),
        ("GET", "/strategies?type=trend_following", None, 200),
        ("GET", "/strategies?status=active", None, 200),
    ]

    for method, endpoint, data, expected in tests:
        total_tests += 1
        if test_api_endpoint(method, endpoint, data, expected):
            passed_tests += 1

    # 3. 测试策略创建
    print("\n➕ 测试策略创建")
    print("-" * 30)

    new_strategy = {
        "name": f"测试策略_{int(time.time())}",
        "type": "trend_following",
        "category": "dual_ma_cross",
        "description": "自动化测试创建的策略",
        "code_type": "python",
        "code_content": """
def initialize(context):
    context.short_period = 5
    context.long_period = 20
    context.position = 0

def handle_bar(context, data):
    return {'action': 'HOLD'}
""",
        "parameters": {
            "short_period": 5,
            "long_period": 20
        },
        "symbol": "BTCUSDT",
        "timeframe": "1h",
        "is_active": False
    }

    total_tests += 1
    if test_api_endpoint("POST", "/strategies", new_strategy, 200):
        passed_tests += 1

    # 4. 测试其他API端点
    print("\n🔧 测试其他API端点")
    print("-" * 30)

    other_tests = [
        ("GET", "/strategies/types", None, 200),
        ("GET", "/strategies/categories", None, 200),
    ]

    for method, endpoint, data, expected in other_tests:
        total_tests += 1
        if test_api_endpoint(method, endpoint, data, expected):
            passed_tests += 1

    # 5. 测试数据库连接
    print("\n💾 测试数据库状态")
    print("-" * 30)

    try:
        import sqlite3
        conn = sqlite3.connect('app/database.db')
        cursor = conn.cursor()

        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"   📊 数据库表: {len(tables)} 个")

        # 检查策略模板数量
        cursor.execute("SELECT COUNT(*) FROM strategy_templates")
        template_count = cursor.fetchone()[0]
        print(f"   📋 策略模板: {template_count} 个")

        # 检查策略数量
        cursor.execute("SELECT COUNT(*) FROM strategies")
        strategy_count = cursor.fetchone()[0]
        print(f"   📈 策略记录: {strategy_count} 个")

        conn.close()
        print("   ✅ 数据库连接正常")

    except Exception as e:
        print(f"   ❌ 数据库错误: {str(e)}")

    # 测试结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {(passed_tests/total_tests*100):.1f}%")

    if passed_tests == total_tests:
        print("🎉 所有测试通过！策略管理系统运行正常")
    else:
        print("⚠️  部分测试失败，请检查系统状态")

    print("\n🌐 前端访问地址:")
    print(f"   主页: http://localhost:8080")
    print(f"   策略管理: http://localhost:8080/#/strategy/management")
    print(f"   策略向导: http://localhost:8080/#/strategy/wizard")

if __name__ == "__main__":
    main()
