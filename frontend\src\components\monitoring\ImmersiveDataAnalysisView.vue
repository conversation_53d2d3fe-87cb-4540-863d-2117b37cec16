<template>
  <div class="immersive-data-analysis" :class="{'fullscreen-mode': isFullscreen}">
    <div class="view-header">
      <h2 class="view-title">{{ title }}</h2>

      <div class="view-controls">
        <el-select
          v-model="selectedDataSource"
          placeholder="选择数据源"
          @change="onDataSourceChange"
          size="small"
          class="data-source-selector"
        >
          <el-option
            v-for="source in dataSources"
            :key="source.id"
            :label="source.name"
            :value="source.id"
          >
          </el-option>
        </el-select>

        <el-select
          v-model="selectedTimeframe"
          placeholder="时间周期"
          @change="onTimeframeChange"
          size="small"
          class="timeframe-selector"
        >
          <el-option label="1秒" value="1s"></el-option>
          <el-option label="1分钟" value="1m"></el-option>
          <el-option label="3分钟" value="3m"></el-option>
          <el-option label="5分钟" value="5m"></el-option>
          <el-option label="15分钟" value="15m"></el-option>
          <el-option label="30分钟" value="30m"></el-option>
          <el-option label="1小时" value="1h"></el-option>
          <el-option label="2小时" value="2h"></el-option>
          <el-option label="4小时" value="4h"></el-option>
          <el-option label="6小时" value="6h"></el-option>
          <el-option label="8小时" value="8h"></el-option>
          <el-option label="12小时" value="12h"></el-option>
          <el-option label="1天" value="1d"></el-option>
          <el-option label="3天" value="3d"></el-option>
          <el-option label="1周" value="1w"></el-option>
          <el-option label="1月" value="1M"></el-option>
        </el-select>

        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          size="small"
          class="date-range-picker"
          @change="onDateRangeChange"
        >
        </el-date-picker>

        <el-button
          type="primary"
          size="small"
          icon="el-icon-refresh-right"
          @click="refreshData"
          :loading="isLoading"
        >
          刷新
        </el-button>

        <el-button
          :type="isFullscreen ? 'danger' : 'primary'"
          size="small"
          :icon="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"
          @click="toggleFullscreen"
          class="fullscreen-button"
        >
          {{ isFullscreen ? '退出全屏' : '全屏模式' }}
        </el-button>
      </div>
    </div>

    <!-- 全局加载状态 -->
    <div class="global-loading" v-if="isLoading">
      <div class="loading-content">
        <i class="el-icon-loading"></i>
        <p>数据加载中，请稍候...</p>
      </div>
    </div>

    <!-- 仪表板网格 -->
    <div class="dashboard-grid"
         :style="{
           gridTemplateColumns: `repeat(${layout.options?.columns || 2}, 1fr)`,
           gap: layout.options?.compactMode ? '10px' : '20px'
         }"
    >
      <!-- 无数据提示 -->
      <div class="no-data-message" v-if="visibleModules.length === 0">
        <i class="el-icon-data-board"></i>
        <h3>暂无可用的数据模块</h3>
        <p>请通过仪表板自定义工具添加数据模块</p>
        <el-button type="primary" size="small" @click="$refs.dashboardCustomizer.showPanel = true">
          自定义仪表板
        </el-button>
      </div>

      <!-- 数据模块 -->
      <div
        v-for="module in visibleModules"
        :key="module.id"
        class="dashboard-module"
        :class="{'with-grid': layout.options?.showGrid}"
      >
        <div class="module-header">
          <h3>{{ module.title }}</h3>
          <div class="module-actions">
            <el-tooltip content="刷新模块" placement="top">
              <i class="el-icon-refresh" @click="refreshModule(module.id)"></i>
            </el-tooltip>
            <el-tooltip content="模块设置" placement="top">
              <i class="el-icon-setting" @click="openModuleSettings(module.id)"></i>
            </el-tooltip>
          </div>
        </div>

        <div class="module-content">
          <!-- 图表模块 -->
          <interactive-chart-component
            v-if="module.type === 'chart'"
            :ref="`module_${module.id}`"
            :data-source="getDataSourceConfig(module)"
            :initial-chart-type="module.settings?.chartType || 'line'"
            :chart-title="module.title"
            :height="module.settings?.height || '350px'"
            :width="'100%'"
            :auto-refresh="module.settings?.autoRefresh"
            :refresh-interval="module.settings?.refreshInterval || 60000"
          ></interactive-chart-component>

          <!-- 数据表格模块 -->
          <data-table-component
            v-else-if="module.type === 'table'"
            :ref="`module_${module.id}`"
            :data-source="getDataSourceConfig(module)"
            :columns="module.settings?.columns"
            :page-size="module.settings?.pageSize || 10"
            :height="module.settings?.height || '350px'"
          ></data-table-component>

          <!-- 指标卡模块 -->
          <metrics-card-component
            v-else-if="module.type === 'metrics'"
            :ref="`module_${module.id}`"
            :data-source="getDataSourceConfig(module)"
            :metrics="module.settings?.metrics"
          ></metrics-card-component>

          <!-- 默认占位符 -->
          <div v-else class="module-placeholder">
            <i class="el-icon-warning"></i>
            <p>未知模块类型</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义仪表板组件 -->
    <dashboard-customizer
      ref="dashboardCustomizer"
      :modules="dashboardModules"
      :dashboard-id="dashboardId"
      @layout-change="onLayoutChange"
    ></dashboard-customizer>

    <!-- 模块设置对话框 -->
    <el-dialog
      title="模块设置"
      :visible.sync="moduleSettingsVisible"
      width="500px"
    >
      <div v-if="currentModule" class="module-settings-content">
        <el-form :model="currentModuleSettings" label-position="top">
          <!-- 通用设置 -->
          <el-form-item label="模块标题">
            <el-input v-model="currentModuleSettings.title"></el-input>
          </el-form-item>

          <el-form-item label="高度设置">
            <el-input v-model="currentModuleSettings.height" placeholder="例如: 350px">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>

          <el-form-item label="自动刷新">
            <el-switch v-model="currentModuleSettings.autoRefresh"></el-switch>
          </el-form-item>

          <el-form-item v-if="currentModuleSettings.autoRefresh" label="刷新间隔">
            <el-input-number
              v-model="currentModuleSettings.refreshInterval"
              :min="5000"
              :max="300000"
              :step="5000"
            ></el-input-number>
            <span style="margin-left: 10px;">毫秒</span>
          </el-form-item>

          <!-- 图表特定设置 -->
          <template v-if="currentModule.type === 'chart'">
            <el-form-item label="图表类型">
              <el-select v-model="currentModuleSettings.chartType">
                <el-option label="折线图" value="line"></el-option>
                <el-option label="柱状图" value="bar"></el-option>
                <el-option label="面积图" value="area"></el-option>
                <el-option label="散点图" value="scatter"></el-option>
                <el-option label="K线图" value="candlestick"></el-option>
              </el-select>
            </el-form-item>
          </template>

          <!-- 表格特定设置 -->
          <template v-if="currentModule.type === 'table'">
            <el-form-item label="每页行数">
              <el-input-number
                v-model="currentModuleSettings.pageSize"
                :min="5"
                :max="100"
                :step="5"
              ></el-input-number>
            </el-form-item>
          </template>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="moduleSettingsVisible = false">取消</el-button>
        <el-button type="primary" @click="saveModuleSettings">保存设置</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import InteractiveChartComponent from './InteractiveChartComponent.vue';
import DashboardCustomizer from './DashboardCustomizer.vue';
import axios from 'axios';

import DataTableComponent from './DataTableComponent.vue';
import MetricsCardComponent from './MetricsCardComponent.vue';

export default {
  name: 'ImmersiveDataAnalysisView',
  components: {
    InteractiveChartComponent,
    DashboardCustomizer,
    DataTableComponent,
    MetricsCardComponent
  },
  props: {
    title: {
      type: String,
      default: '沉浸式数据分析视图'
    },
    dashboardId: {
      type: String,
      default: 'default-dashboard'
    }
  },
  data() {
    return {
      // 数据源相关
      dataSources: [],
      selectedDataSource: null,
      selectedTimeframe: '1h',
      dateRange: [
        new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 过去7天
        new Date() // 今天
      ],

      // 日期选择器配置
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },

      // 仪表板模块
      dashboardModules: [
        {
          id: 'price-chart',
          title: '价格走势图',
          description: '显示资产价格走势',
          type: 'chart',
          endpoint: '/api/v1/data/ohlcv', // 使用市场数据服务的OHLCV数据API
          settings: {
            chartType: 'candlestick', // 使用K线图显示价格数据
            height: '350px',
            autoRefresh: false,
            refreshInterval: 60000
          }
        },
        {
          id: 'volume-chart',
          title: '交易量分析',
          description: '显示交易量变化',
          type: 'chart',
          endpoint: '/api/v1/data/ohlcv', // 使用市场数据服务的OHLCV数据API，从中提取交易量数据
          settings: {
            chartType: 'bar',
            height: '350px',
            autoRefresh: false,
            refreshInterval: 60000
          }
        },
        {
          id: 'data-quality',
          title: '数据质量指标',
          description: '展示数据质量相关指标',
          type: 'metrics',
          endpoint: '/api/v1/monitoring/quality-metrics', // 使用正确的数据质量指标API
          settings: {
            metrics: ['completeness', 'accuracy', 'timeliness'],
            autoRefresh: false,
            refreshInterval: 120000
          }
        },
        {
          id: 'latest-data',
          title: '最新数据记录',
          description: '显示最新的数据记录',
          type: 'table',
          endpoint: '/api/v1/data/ohlcv', // 使用市场数据服务的OHLCV数据API作为最新数据
          settings: {
            pageSize: 10,
            height: '350px',
            autoRefresh: true,
            refreshInterval: 30000
          }
        }
      ],

      // 布局相关
      layout: {
        order: [],
        visibility: {},
        options: {
          showGrid: true,
          compactMode: false,
          columns: 2
        }
      },

      // 状态标志
      isLoading: false,
      isFullscreen: false,

      // 模块设置相关
      moduleSettingsVisible: false,
      currentModule: null,
      currentModuleSettings: {},

      // 定时刷新
      refreshTimers: {}
    };
  },
  computed: {
    // 可见的模块列表（根据可见性和排序）
    visibleModules() {
      if (!this.layout.order || this.layout.order.length === 0) {
        // 如果没有定义顺序，使用原始顺序
        return this.dashboardModules.filter(m =>
          this.layout.visibility[m.id] !== false
        );
      }

      // 根据定义的顺序和可见性过滤和排序
      return this.layout.order
        .map(id => this.dashboardModules.find(m => m.id === id))
        .filter(m => m && this.layout.visibility[m.id] !== false);
    }
  },
  async created() {
    // 获取数据源列表
    await this.fetchDataSources();

    // 如果有数据源，选择第一个
    if (this.dataSources.length > 0) {
      this.selectedDataSource = this.dataSources[0].id;
    }

    // 初始化布局
    this.initLayout();

    // 获取初始数据
    this.refreshData();
  },
  beforeDestroy() {
    // 清除所有刷新计时器
    this.clearAllRefreshTimers();
  },
  methods: {
    // 获取数据源列表
    async fetchDataSources() {
      try {
        this.isLoading = true;
        // 使用市场数据服务的数据源API
        const marketDataUrl = process.env.VUE_APP_MARKET_DATA_URL || 'http://localhost:8005';
        const response = await axios.get(`${marketDataUrl}/api/v1/data/sources`);

        if (response.data && Array.isArray(response.data)) {
          // 直接使用返回的数组
          this.dataSources = response.data;
          console.log('获取到数据源列表:', this.dataSources);
        } else if (response.data && response.data.data) {
          // 兼容旧格式
          this.dataSources = response.data.data;
          console.log('获取到数据源列表(旧格式):', this.dataSources);
        } else {
          console.warn('数据源返回格式不符合预期:', response.data);
          this.dataSources = [];
        }
      } catch (error) {
        console.error('获取数据源失败', error);
        this.$message.error('获取数据源列表失败');
        // 设置一些默认数据源以便测试
        this.dataSources = [
          { id: 18, name: 'Binance BTC/USDT', symbol: 'BTC/USDT' },
          { id: 19, name: 'Binance ETH/USDT', symbol: 'ETH/USDT' }
        ];
      } finally {
        this.isLoading = false;
      }
    },

    // 初始化布局
    initLayout() {
      // 设置默认顺序
      this.layout.order = this.dashboardModules.map(m => m.id);

      // 设置默认可见性（全部可见）
      this.layout.visibility = {};
      this.dashboardModules.forEach(m => {
        this.layout.visibility[m.id] = true;
      });
    },

    // 获取模块的数据源配置
    getDataSourceConfig(module) {
      // 获取市场数据服务URL
      const marketDataUrl = process.env.VUE_APP_MARKET_DATA_URL || 'http://localhost:8005';

      // 获取选中的数据源信息
      const dataSource = this.dataSources.find(ds => ds.id === this.selectedDataSource);

      // 构建请求参数
      let params = {};
      let url = '';

      // 根据模块类型和端点构建不同的请求参数
      if (module.endpoint === '/api/v1/data/ohlcv') {
        // OHLCV数据API
        let symbol = dataSource ? dataSource.symbol : 'BTC/USDT';
        // 确保使用不带斜杠的格式
        symbol = symbol.replace('/', '');
        params = {
          symbol: symbol,
          timeframe: this.selectedTimeframe,
          limit: 100 // 减少数据量，避免API限制
        };

        // 如果有日期范围，添加日期参数
        if (this.dateRange && this.dateRange.length === 2) {
          if (this.dateRange[0]) {
            params.start_time = new Date(this.dateRange[0]).toISOString();
          }
          if (this.dateRange[1]) {
            params.end_time = new Date(this.dateRange[1]).toISOString();
          }
        }

        url = `${marketDataUrl}${module.endpoint}`;
      } else if (module.endpoint === '/api/v1/monitoring/quality-metrics') {
        // 数据质量指标API
        let symbol = dataSource ? dataSource.symbol : 'BTC/USDT';
        // 确保使用不带斜杠的格式
        symbol = symbol.replace('/', '');

        params = {
          source_id: this.selectedDataSource,
          exchange: dataSource ? dataSource.exchange || 'binance' : 'binance',
          symbol: symbol,
          timeframe: this.selectedTimeframe
        };

        url = `${marketDataUrl}${module.endpoint}`;
      } else {
        // 其他API
        params = {
          source_id: this.selectedDataSource,
          timeframe: this.selectedTimeframe
        };

        // 如果有日期范围，添加日期参数
        if (this.dateRange && this.dateRange.length === 2) {
          params.start_date = this.formatDate(this.dateRange[0]);
          params.end_date = this.formatDate(this.dateRange[1]);
        }

        url = `${marketDataUrl}${module.endpoint}`;
      }

      return {
        url: url,
        method: 'get',
        params
      };
    },

    // 格式化日期为 YYYY-MM-DD
    formatDate(date) {
      if (!date) return '';

      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    },

    // 数据源变化处理
    onDataSourceChange() {
      this.refreshData();
    },

    // 时间周期变化处理
    onTimeframeChange() {
      this.refreshData();
    },

    // 日期范围变化处理
    onDateRangeChange() {
      this.refreshData();
    },

    // 获取数据质量指标
    async fetchQualityMetrics() {
      try {
        // 获取市场数据服务URL
        const marketDataUrl = process.env.VUE_APP_MARKET_DATA_URL || 'http://localhost:8005';

        // 获取选中的数据源信息
        const dataSource = this.dataSources.find(ds => ds.id === this.selectedDataSource);

        // 构建请求参数
        let symbol = dataSource ? dataSource.symbol : 'BTC/USDT';
        // 确保使用不带斜杠的格式
        symbol = symbol.replace('/', '');

        const params = {
          source_id: this.selectedDataSource,
          exchange: dataSource ? dataSource.exchange || 'binance' : 'binance',
          symbol: symbol,
          timeframe: this.selectedTimeframe
        };

        console.log('获取数据质量指标，参数:', params);

        // 发送请求
        const response = await axios.get(`${marketDataUrl}/api/v1/monitoring/quality-metrics`, { params });

        // 处理响应
        if (response.data && response.data.success && response.data.data) {
          console.log('获取到数据质量指标:', response.data.data);
          return response.data.data;
        } else {
          console.warn('数据质量指标返回格式不符合预期:', response.data);
          return null;
        }
      } catch (error) {
        console.error('获取数据质量指标失败:', error);
        this.$message.error('获取数据质量指标失败');
        return null;
      }
    },

    // 刷新所有数据
    async refreshData() {
      this.isLoading = true;

      try {
        // 获取数据质量指标
        const qualityMetrics = await this.fetchQualityMetrics();

        // 更新数据质量指标模块
        if (qualityMetrics) {
          const qualityModule = this.dashboardModules.find(m => m.id === 'data-quality');
          if (qualityModule) {
            qualityModule.data = qualityMetrics;
          }
        }

        // 刷新所有模块
        const refreshPromises = this.dashboardModules.map(module => {
          return this.refreshModule(module.id).catch(err => {
            console.error(`刷新模块 ${module.id} 失败:`, err);
            return null;
          });
        });

        await Promise.all(refreshPromises);

        this.$message.success('数据已刷新');
      } catch (error) {
        console.error('刷新数据失败:', error);
        this.$message.error('刷新数据失败');
      } finally {
        this.isLoading = false;

        // 设置自动刷新
        this.setupAutoRefresh();
      }
    },

    // 刷新单个模块
    async refreshModule(moduleId) {
      const module = this.dashboardModules.find(m => m.id === moduleId);
      if (!module) return;

      try {
        // 根据模块类型执行不同的刷新逻辑
        if (module.type === 'metrics' && module.endpoint === '/api/v1/monitoring/quality-metrics') {
          // 刷新数据质量指标
          const qualityMetrics = await this.fetchQualityMetrics();
          if (qualityMetrics) {
            module.data = qualityMetrics;
          }
        } else {
          // 其他模块类型，通过重新获取数据源配置来刷新
          // 这会触发组件内部的fetchData方法
          const dataSourceConfig = this.getDataSourceConfig(module);
          module.dataSourceConfig = dataSourceConfig;

          // 通知组件刷新数据
          this.$nextTick(() => {
            // 查找对应的组件实例
            const componentRefs = this.$refs[`module_${module.id}`];
            if (componentRefs && componentRefs.fetchData) {
              componentRefs.fetchData();
            }
          });
        }

        this.$message.success(`模块 "${module.title}" 已刷新`);
      } catch (error) {
        console.error(`刷新模块 "${module.title}" 失败:`, error);
        this.$message.error(`刷新模块失败: ${error.message}`);
      }
    },

    // 设置自动刷新
    setupAutoRefresh() {
      // 清除现有定时器
      this.clearAllRefreshTimers();

      // 为每个开启了自动刷新的模块设置定时器
      this.dashboardModules.forEach(module => {
        if (module.settings && module.settings.autoRefresh) {
          const interval = module.settings.refreshInterval || 60000;

          this.refreshTimers[module.id] = setInterval(() => {
            this.refreshModule(module.id);
          }, interval);
        }
      });
    },

    // 清除所有刷新定时器
    clearAllRefreshTimers() {
      Object.keys(this.refreshTimers).forEach(key => {
        clearInterval(this.refreshTimers[key]);
      });
      this.refreshTimers = {};
    },

    // 布局变化处理
    onLayoutChange(layout) {
      this.layout = layout;

      // 根据新布局更新自动刷新设置
      this.setupAutoRefresh();
    },

    // 切换全屏模式
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;

      if (this.isFullscreen) {
        // 进入全屏模式的额外处理
        document.body.style.overflow = 'hidden';
      } else {
        // 退出全屏模式的额外处理
        document.body.style.overflow = '';
      }
    },

    // 打开模块设置
    openModuleSettings(moduleId) {
      const module = this.dashboardModules.find(m => m.id === moduleId);
      if (!module) return;

      this.currentModule = module;
      this.currentModuleSettings = JSON.parse(JSON.stringify(module.settings || {}));

      // 添加标题设置
      this.currentModuleSettings.title = module.title;

      this.moduleSettingsVisible = true;
    },

    // 保存模块设置
    saveModuleSettings() {
      if (!this.currentModule) return;

      // 更新模块标题
      this.currentModule.title = this.currentModuleSettings.title;

      // 更新模块设置
      this.currentModule.settings = { ...this.currentModuleSettings };

      // 如果设置了自动刷新，更新刷新定时器
      this.setupAutoRefresh();

      this.moduleSettingsVisible = false;
      this.$message.success('设置已保存');
    }
  }
};
</script>

<style scoped>
.immersive-data-analysis {
  position: relative;
  background-color: #0d1b34;
  border-radius: 10px;
  padding: 20px;
  color: #EAEAEA;
  min-height: 700px;
  transition: all 0.3s ease;
}

.immersive-data-analysis.fullscreen-mode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  border-radius: 0;
  padding: 20px 30px;
  overflow-y: auto;
  background-color: #081428;
}

.view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 247, 255, 0.3);
}

.view-title {
  color: #00F7FF;
  margin: 0;
  text-shadow: 0 0 10px rgba(0, 247, 255, 0.5);
  font-size: 22px;
}

.view-controls {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.dashboard-module {
  background-color: rgba(20, 30, 60, 0.5);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 247, 255, 0.2);
}

.dashboard-module:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2), 0 0 10px rgba(0, 247, 255, 0.2);
}

.dashboard-module.with-grid {
  background-image: linear-gradient(rgba(0, 247, 255, 0.05) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(0, 247, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: rgba(10, 20, 50, 0.7);
  border-bottom: 1px solid rgba(0, 247, 255, 0.2);
}

.module-header h3 {
  margin: 0;
  font-size: 16px;
  color: #00F7FF;
}

.module-actions {
  display: flex;
  gap: 12px;
}

.module-actions i {
  cursor: pointer;
  color: #AAAAAA;
  transition: color 0.3s ease;
}

.module-actions i:hover {
  color: #00F7FF;
}

.module-content {
  padding: 15px;
  min-height: 300px;
}

.module-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 300px;
  color: #AAAAAA;
}

.module-placeholder i {
  font-size: 36px;
  margin-bottom: 10px;
}

/* 全局加载状态 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content {
  background-color: #fff;
  padding: 30px 50px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
}

.loading-content i {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 20px;
}

.loading-content p {
  font-size: 16px;
  color: #606266;
  margin: 0;
}

/* 无数据提示 */
.no-data-message {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 50px 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
  text-align: center;
}

.no-data-message i {
  font-size: 64px;
  color: #DCDFE6;
  margin-bottom: 20px;
}

.no-data-message h3 {
  font-size: 20px;
  color: #606266;
  margin: 0 0 10px 0;
}

.no-data-message p {
  font-size: 14px;
  color: #909399;
  margin: 0 0 20px 0;
}

.data-source-selector,
.timeframe-selector {
  width: 140px;
}

.date-range-picker {
  width: 260px;
}

.fullscreen-button {
  margin-left: auto;
}

.module-settings-content {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 10px;
}

@media (max-width: 1200px) {
  .view-controls {
    flex-wrap: wrap;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .view-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .view-controls {
    margin-top: 15px;
    width: 100%;
  }

  .data-source-selector,
  .timeframe-selector,
  .date-range-picker {
    width: 100%;
    margin-bottom: 10px;
  }

  .fullscreen-button {
    margin-left: 0;
  }
}
</style>