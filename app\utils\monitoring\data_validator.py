#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据验证器模块
用于验证数据源之间的数据一致性和质量
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class DataValidator:
    """
    数据验证器，用于验证数据源之间的数据一致性和质量
    """
    
    def __init__(self):
        """初始化数据验证器"""
        logger.info("数据验证器已初始化")
        
    def validate_cross_source(self, 
                             primary_df: pd.DataFrame, 
                             secondary_dfs: List[pd.DataFrame],
                             timestamp_column: str = "timestamp",
                             price_column: str = "close",
                             volume_column: str = "volume",
                             source_names: List[str] = None) -> Dict[str, Any]:
        """
        交叉验证多个数据源之间的数据
        
        Args:
            primary_df: 主数据源DataFrame
            secondary_dfs: 次要数据源DataFrame列表
            timestamp_column: 时间戳列名
            price_column: 价格列名
            volume_column: 成交量列名
            source_names: 数据源名称列表
            
        Returns:
            验证结果
        """
        try:
            logger.info(f"开始交叉验证数据源: 主数据源={len(primary_df)}行, 次要数据源={[len(df) for df in secondary_dfs]}行")
            
            # 如果没有提供数据源名称，使用默认名称
            if not source_names:
                source_names = [f"数据源{i}" for i in range(len(secondary_dfs) + 1)]
                
            # 确保数据源名称数量正确
            if len(source_names) != len(secondary_dfs) + 1:
                logger.warning(f"数据源名称数量({len(source_names)})与数据源数量({len(secondary_dfs) + 1})不匹配")
                source_names = [f"数据源{i}" for i in range(len(secondary_dfs) + 1)]
            
            # 检查数据帧是否为空
            if primary_df.empty:
                logger.error("主数据源为空")
                return {
                    "validation_score": 0,
                    "validation_status": "failed",
                    "message": "主数据源为空"
                }
                
            # 检查次要数据源是否为空
            empty_secondary = [i for i, df in enumerate(secondary_dfs) if df.empty]
            if empty_secondary:
                logger.warning(f"次要数据源 {[source_names[i+1] for i in empty_secondary]} 为空")
                
            # 找出所有数据源的共同时间范围
            all_dfs = [primary_df] + secondary_dfs
            common_start = max([df[timestamp_column].min() for df in all_dfs if not df.empty])
            common_end = min([df[timestamp_column].max() for df in all_dfs if not df.empty])
            
            logger.info(f"共同时间范围: {common_start} 到 {common_end}")
            
            # 筛选共同时间范围内的数据
            filtered_primary = primary_df[(primary_df[timestamp_column] >= common_start) & 
                                         (primary_df[timestamp_column] <= common_end)]
            
            filtered_secondary = []
            for df in secondary_dfs:
                if not df.empty:
                    filtered_df = df[(df[timestamp_column] >= common_start) & 
                                    (df[timestamp_column] <= common_end)]
                    filtered_secondary.append(filtered_df)
                else:
                    filtered_secondary.append(df)
            
            # 计算价格偏差
            price_deviations = []
            volume_deviations = []
            
            for i, sec_df in enumerate(filtered_secondary):
                if sec_df.empty:
                    continue
                    
                # 合并数据帧，按时间戳对齐
                merged = pd.merge(
                    filtered_primary[[timestamp_column, price_column, volume_column]],
                    sec_df[[timestamp_column, price_column, volume_column]],
                    on=timestamp_column,
                    suffixes=('_primary', f'_secondary_{i}')
                )
                
                # 计算价格偏差
                merged[f'price_deviation_{i}'] = (
                    (merged[f'{price_column}_secondary_{i}'] - merged[f'{price_column}_primary']) / 
                    merged[f'{price_column}_primary'] * 100
                )
                
                # 计算成交量偏差
                merged[f'volume_deviation_{i}'] = (
                    (merged[f'{volume_column}_secondary_{i}'] - merged[f'{volume_column}_primary']) / 
                    (merged[f'{volume_column}_primary'] + 1) * 100  # 加1避免除以0
                )
                
                # 计算偏差统计
                price_dev_stats = {
                    'source_name': source_names[i+1],
                    'mean_deviation_pct': merged[f'price_deviation_{i}'].mean(),
                    'max_deviation_pct': merged[f'price_deviation_{i}'].abs().max(),
                    'std_deviation_pct': merged[f'price_deviation_{i}'].std(),
                    'anomaly_count': len(merged[merged[f'price_deviation_{i}'].abs() > 2]),  # 偏差超过2%视为异常
                    'anomaly_percentage': len(merged[merged[f'price_deviation_{i}'].abs() > 2]) / len(merged) * 100,
                    'total_data_points': len(merged)
                }
                
                volume_dev_stats = {
                    'source_name': source_names[i+1],
                    'mean_deviation_pct': merged[f'volume_deviation_{i}'].mean(),
                    'max_deviation_pct': merged[f'volume_deviation_{i}'].abs().max(),
                    'std_deviation_pct': merged[f'volume_deviation_{i}'].std(),
                    'anomaly_count': len(merged[merged[f'volume_deviation_{i}'].abs() > 10]),  # 偏差超过10%视为异常
                    'anomaly_percentage': len(merged[merged[f'volume_deviation_{i}'].abs() > 10]) / len(merged) * 100,
                    'total_data_points': len(merged)
                }
                
                price_deviations.append(price_dev_stats)
                volume_deviations.append(volume_dev_stats)
            
            # 计算总体验证分数
            # 基于价格偏差和成交量偏差的加权平均
            validation_score = 100
            for price_dev in price_deviations:
                # 价格偏差超过2%每个点扣1分，最多扣20分
                validation_score -= min(price_dev['anomaly_percentage'], 20)
                
            for volume_dev in volume_deviations:
                # 成交量偏差超过10%每个点扣0.5分，最多扣10分
                validation_score -= min(volume_dev['anomaly_percentage'] * 0.5, 10)
                
            validation_score = max(0, validation_score)
            validation_score = min(100, validation_score)
            
            # 确定验证状态
            validation_status = "passed"
            if validation_score < 60:
                validation_status = "failed"
            elif validation_score < 80:
                validation_status = "warning"
                
            # 构建验证结果
            result = {
                "validation_score": round(validation_score, 1),
                "validation_status": validation_status,
                "common_period": {
                    "start": common_start.isoformat() if isinstance(common_start, datetime) else common_start,
                    "end": common_end.isoformat() if isinstance(common_end, datetime) else common_end,
                    "data_points": len(filtered_primary)
                },
                "price_deviation": {
                    "threshold": 2.0,  # 价格偏差阈值
                    "overall_statistics": {
                        "sources": len(price_deviations),
                        "total_anomalies": sum(dev['anomaly_count'] for dev in price_deviations),
                        "max_deviation_source": max(price_deviations, key=lambda x: x['max_deviation_pct'])['source_name'] if price_deviations else None,
                        "max_deviation_pct": max(dev['max_deviation_pct'] for dev in price_deviations) if price_deviations else 0
                    },
                    "source_statistics": price_deviations
                },
                "volume_deviation": {
                    "threshold": 10.0,  # 成交量偏差阈值
                    "overall_statistics": {
                        "sources": len(volume_deviations),
                        "total_anomalies": sum(dev['anomaly_count'] for dev in volume_deviations),
                        "max_deviation_source": max(volume_deviations, key=lambda x: x['max_deviation_pct'])['source_name'] if volume_deviations else None,
                        "max_deviation_pct": max(dev['max_deviation_pct'] for dev in volume_deviations) if volume_deviations else 0
                    },
                    "source_statistics": volume_deviations
                }
            }
            
            logger.info(f"交叉验证完成: 验证分数={validation_score}, 状态={validation_status}")
            return result
            
        except Exception as e:
            logger.error(f"交叉验证数据源失败: {str(e)}")
            return {
                "validation_score": 0,
                "validation_status": "error",
                "message": f"验证失败: {str(e)}"
            }
