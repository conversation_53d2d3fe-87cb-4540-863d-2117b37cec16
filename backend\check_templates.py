import sqlite3

conn = sqlite3.connect('app.db')
cursor = conn.cursor()

# 检查所有策略模板
cursor.execute('SELECT name, category, type FROM strategy_templates')
results = cursor.fetchall()

print('所有策略模板:')
for row in results:
    print(f'  {row[0]} - 类型:{row[2]} - 分类:{row[1]}')

# 特别检查MACD和布林带相关的
print('\nMACD相关策略:')
cursor.execute("SELECT name, category, type FROM strategy_templates WHERE name LIKE '%MACD%'")
macd_results = cursor.fetchall()
for row in macd_results:
    print(f'  {row[0]} - 类型:{row[2]} - 分类:{row[1]}')

print('\n布林带相关策略:')
cursor.execute("SELECT name, category, type FROM strategy_templates WHERE name LIKE '%布林%'")
bollinger_results = cursor.fetchall()
for row in bollinger_results:
    print(f'  {row[0]} - 类型:{row[2]} - 分类:{row[1]}')

conn.close()
