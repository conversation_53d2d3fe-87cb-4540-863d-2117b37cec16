#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json

def test_file_write():
    """测试文件写入权限"""

    # 配置文件路径
    config_file = os.path.join('configs', 'system_config.json')

    print(f"测试文件写入权限...")
    print(f"配置文件路径: {config_file}")
    print(f"绝对路径: {os.path.abspath(config_file)}")
    print(f"文件是否存在: {os.path.exists(config_file)}")

    # 读取现有配置
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            existing_config = json.load(f)
        print("✅ 文件读取成功")
        print(f"当前配置: {json.dumps(existing_config, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return

    # 修改配置
    test_config = existing_config.copy()
    test_config['trading']['enable_live_trading'] = True
    test_config['trading']['risk_level'] = 'high'
    test_config['trading']['max_position_size'] = 0.5

    # 尝试写入文件
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=4)
        print("✅ 文件写入成功")
    except Exception as e:
        print(f"❌ 文件写入失败: {e}")
        return

    # 验证写入
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            updated_config = json.load(f)
        print("✅ 文件验证读取成功")

        if updated_config['trading']['enable_live_trading'] == True:
            print("✅ 配置更新成功！")
        else:
            print("❌ 配置更新失败！")

        print(f"更新后配置: {json.dumps(updated_config, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"❌ 文件验证读取失败: {e}")

if __name__ == "__main__":
    test_file_write()
