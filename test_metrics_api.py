#!/usr/bin/env python3
"""
测试metrics API端点
"""

import requests
import json

def test_metrics_api():
    """测试metrics API端点"""
    
    # 获取认证token
    print("🔐 获取认证token...")
    auth_response = requests.post('http://localhost:8000/api/v1/auth/token', 
                                 json={'username': 'admin', 'password': 'admin123'})
    
    if auth_response.status_code != 200:
        print(f"❌ 认证失败: {auth_response.status_code}")
        return
    
    token = auth_response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    print(f"✅ 认证成功")
    
    # 测试metrics端点
    categories = ['cpu', 'memory', 'api', 'cache']
    
    print("\n📊 测试metrics API端点...")
    for category in categories:
        print(f"\n测试 {category} metrics:")
        
        try:
            response = requests.get(f'http://localhost:8000/api/v1/performance/metrics/{category}?limit=10', 
                                   headers=headers)
            
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"  Success: {data.get('success', False)}")
                print(f"  Data length: {len(data.get('data', []))}")
                
                if data.get('data'):
                    sample = data['data'][0]
                    print(f"  Sample keys: {list(sample.keys())}")
                    print(f"  Sample data: {sample}")
                else:
                    print("  ⚠️ 没有数据")
            else:
                print(f"  ❌ 错误: {response.text}")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")

if __name__ == "__main__":
    test_metrics_api()
