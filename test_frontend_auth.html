<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端认证测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .token-display {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            max-height: 100px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端认证测试</h1>
        <p>此页面用于测试前端认证机制是否正常工作。</p>

        <!-- 认证测试 -->
        <div class="test-section">
            <h3>1. 用户认证测试</h3>
            <button onclick="testLogin()">登录测试</button>
            <button onclick="checkStoredToken()">检查存储的Token</button>
            <button onclick="clearToken()">清除Token</button>
            <div id="auth-result" class="test-result info">等待测试...</div>
            <div id="token-display" class="token-display" style="display: none;"></div>
        </div>

        <!-- API调用测试 -->
        <div class="test-section">
            <h3>2. API调用测试</h3>
            <button onclick="testApiWithToken()">使用Token调用API</button>
            <button onclick="testApiWithoutToken()">不使用Token调用API</button>
            <div id="api-result" class="test-result info">等待测试...</div>
        </div>

        <!-- 前端service测试 -->
        <div class="test-section">
            <h3>3. 前端service模块测试</h3>
            <button onclick="testFrontendService()">测试前端service</button>
            <div id="service-result" class="test-result info">等待测试...</div>
        </div>

        <!-- 快速修复 -->
        <div class="test-section">
            <h3>4. 快速修复</h3>
            <button onclick="quickFix()">一键修复认证问题</button>
            <button onclick="openPerformancePage()">打开性能优化页面</button>
            <div id="fix-result" class="test-result info">等待操作...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let authToken = null;
        const API_BASE = 'http://localhost:8000/api/v1';

        // 登录测试
        async function testLogin() {
            const resultDiv = document.getElementById('auth-result');
            const tokenDiv = document.getElementById('token-display');

            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在登录...';

            try {
                const response = await axios.post(`${API_BASE}/auth/token`, {
                    username: 'admin',
                    password: 'admin123'
                });

                if (response.data.access_token) {
                    authToken = response.data.access_token;

                    // 存储到localStorage（模拟前端行为）
                    localStorage.setItem('token', authToken);

                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ 登录成功！Token已获取并存储';

                    tokenDiv.style.display = 'block';
                    tokenDiv.textContent = `Token: ${authToken}`;
                } else {
                    throw new Error('未获取到访问令牌');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 登录失败: ${error.message}`;
                tokenDiv.style.display = 'none';
            }
        }

        // 检查存储的Token
        function checkStoredToken() {
            const resultDiv = document.getElementById('auth-result');
            const tokenDiv = document.getElementById('token-display');

            const storedToken = localStorage.getItem('token');

            if (storedToken) {
                authToken = storedToken;
                resultDiv.className = 'test-result success';
                resultDiv.textContent = '✓ 找到存储的Token';

                tokenDiv.style.display = 'block';
                tokenDiv.textContent = `Stored Token: ${storedToken}`;
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '✗ 未找到存储的Token';
                tokenDiv.style.display = 'none';
            }
        }

        // 清除Token
        function clearToken() {
            const resultDiv = document.getElementById('auth-result');
            const tokenDiv = document.getElementById('token-display');

            localStorage.removeItem('token');
            authToken = null;

            resultDiv.className = 'test-result info';
            resultDiv.textContent = 'Token已清除';
            tokenDiv.style.display = 'none';
        }

        // 使用Token调用API
        async function testApiWithToken() {
            const resultDiv = document.getElementById('api-result');

            if (!authToken) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '✗ 请先登录获取Token';
                return;
            }

            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在调用API...';

            try {
                const response = await axios.get(`${API_BASE}/performance/summary`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ API调用成功！返回了性能数据';
                } else {
                    throw new Error(response.data.error || 'API调用失败');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ API调用失败: ${error.response?.status} ${error.response?.statusText || error.message}`;
            }
        }

        // 不使用Token调用API
        async function testApiWithoutToken() {
            const resultDiv = document.getElementById('api-result');

            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在调用API（无Token）...';

            try {
                const response = await axios.get(`${API_BASE}/performance/summary`);

                resultDiv.className = 'test-result error';
                resultDiv.textContent = '✗ 意外成功：API应该返回401错误';
            } catch (error) {
                if (error.response?.status === 401) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ 正确：API返回401未认证错误';
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = `✗ 意外错误: ${error.response?.status} ${error.response?.statusText || error.message}`;
                }
            }
        }

        // 测试前端service模块
        async function testFrontendService() {
            const resultDiv = document.getElementById('service-result');

            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在测试前端service模块...';

            try {
                // 尝试访问前端应用来检查service模块
                const frontendResponse = await fetch('http://localhost:8081/');

                if (frontendResponse.ok) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ 前端应用正在运行，可以进一步测试service模块';
                } else {
                    throw new Error(`前端应用响应错误: ${frontendResponse.status}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 前端应用测试失败: ${error.message}`;
            }
        }

        // 快速修复认证问题
        async function quickFix() {
            const resultDiv = document.getElementById('fix-result');

            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在修复认证问题...';

            try {
                // 1. 先登录获取token
                await testLogin();

                // 2. 设置用户角色
                localStorage.setItem('userRole', 'admin');

                // 3. 设置用户信息
                const userInfo = {
                    id: 1,
                    username: 'admin',
                    email: '<EMAIL>',
                    role: 'admin'
                };
                localStorage.setItem('userInfo', JSON.stringify(userInfo));

                resultDiv.className = 'test-result success';
                resultDiv.textContent = '✓ 认证问题已修复！现在可以访问性能优化页面了';
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 修复失败: ${error.message}`;
            }
        }

        // 打开性能优化页面
        function openPerformancePage() {
            const resultDiv = document.getElementById('fix-result');

            // 检查是否有token
            const token = localStorage.getItem('token');
            if (!token) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '✗ 请先执行一键修复认证问题';
                return;
            }

            // 打开性能优化页面
            window.open('http://localhost:8081/#/performance', '_blank');

            resultDiv.className = 'test-result success';
            resultDiv.textContent = '✓ 已打开性能优化页面';
        }

        // 页面加载完成后自动检查存储的Token
        window.addEventListener('load', function() {
            setTimeout(checkStoredToken, 1000);
        });
    </script>
</body>
</html>
