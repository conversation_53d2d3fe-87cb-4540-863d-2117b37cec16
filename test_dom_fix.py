#!/usr/bin/env python3
"""
测试DOM渲染修复
验证API趋势图DOM元素是否能正确渲染
"""

import requests

def test_dom_fix():
    """测试DOM修复功能"""

    print("🎯 测试API趋势图DOM渲染修复...")

    # 1. 登录获取token
    print("\n1. 登录获取认证token...")
    login_url = "http://localhost:8000/api/v1/auth/token"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }

    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    # 2. 测试API趋势数据
    print("\n2. 测试API趋势数据...")
    trend_url = "http://localhost:8000/api/v1/performance/metrics/api?limit=20"

    try:
        response = requests.get(trend_url, headers=headers, timeout=10)

        if response.status_code == 200:
            trend_data = response.json()
            print(f"✅ API请求成功")

            if trend_data.get("success") and trend_data.get("data"):
                data_points = trend_data["data"]
                print(f"✅ 数据格式正确")
                print(f"   - 数据点数量: {len(data_points)}")

                if len(data_points) > 0:
                    sample = data_points[0]
                    print(f"   - 示例数据点: {sample}")

                    # 检查必需字段
                    required_fields = ['timestamp', 'avg_time', 'max_time']
                    missing_fields = [f for f in required_fields if f not in sample]

                    if missing_fields:
                        print(f"   ❌ 缺少必需字段: {missing_fields}")
                        return False
                    else:
                        print(f"   ✅ 包含所有必需字段")

                        # 验证数据值
                        avg_time = sample.get('avg_time')
                        max_time = sample.get('max_time')

                        if avg_time is not None and max_time is not None:
                            print(f"   ✅ 数据值正常: avg={avg_time}, max={max_time}")
                        else:
                            print(f"   ❌ 数据值异常: avg={avg_time}, max={max_time}")
                            return False
                else:
                    print(f"   ❌ 没有数据点")
                    return False
            else:
                print(f"❌ API响应格式错误")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return False

    # 3. 测试API端点获取
    print("\n3. 测试API端点获取...")
    endpoints_url = "http://localhost:8000/api/v1/performance/api/endpoints"

    try:
        response = requests.get(endpoints_url, headers=headers, timeout=10)

        if response.status_code == 200:
            endpoints_data = response.json()
            print(f"✅ 端点API请求成功")

            # 检查响应格式
            endpoints = []
            if endpoints_data.get("success") and endpoints_data.get("data"):
                # 标准格式
                endpoints = endpoints_data["data"]
                print(f"✅ 使用标准格式获取端点")
            elif isinstance(endpoints_data, list):
                # 直接数组格式
                endpoints = endpoints_data
                print(f"✅ 使用数组格式获取端点")

            if len(endpoints) > 0:
                print(f"✅ 成功获取 {len(endpoints)} 个端点")
                print(f"   - 示例端点: {endpoints[:3]}")
                return True
            else:
                print(f"❌ 未获取到端点数据")
                return False
        else:
            print(f"❌ 端点API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 端点API请求异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 API趋势图DOM渲染修复验证测试")
    print("=" * 60)

    success = test_dom_fix()

    print("\n" + "=" * 60)
    if success:
        print("🎉 DOM渲染修复验证成功！")
        print("✅ API数据获取正常")
        print("✅ 数据格式完整正确")
        print("✅ 数据值在合理范围内")
        print("✅ API端点获取正常")
        print("\n📋 修复内容:")
        print("  - 修复了DOM元素渲染时机问题")
        print("  - 在chartLoading变为false后等待DOM渲染")
        print("  - 使用setTimeout确保DOM完全渲染")
        print("  - 增强了图表初始化的错误处理")
        print("  - 修复了API端点响应格式兼容性问题")
        print("  - 支持标准格式和数组格式的API响应")
        print("\n🎊 前端图表应该能正常显示！")
        print("请检查浏览器页面:")
        print("1. 确认'API调用时间趋势'区域不再显示'加载中...'")
        print("2. 确认图表容器存在且有内容")
        print("3. 确认浏览器控制台没有DOM元素不存在的错误")
        print("4. 确认没有API响应格式异常的警告")
    else:
        print("❌ DOM渲染修复验证失败！")
        print("需要进一步检查问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
