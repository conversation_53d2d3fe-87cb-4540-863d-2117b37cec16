{"name": "btc-trading-system-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "node -e \"const { execSync } = require('child_process'); try { const netstat = execSync('netstat -ano | findstr :8080', { encoding: 'utf8' }); const lines = netstat.split('\\n').filter(line => line.includes('LISTENING')); if (lines.length > 0) { const pid = lines[0].trim().split(/\\s+/).pop(); console.log('关闭占用8080端口的进程:', pid); execSync(`taskkill /F /PID ${pid}`, { stdio: 'inherit' }); console.log('等待端口释放...'); setTimeout(() => {}, 2000); } } catch (e) { console.log('8080端口未被占用'); }\" && vue-cli-service serve --open", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.1", "chart.js": "^3.9.1", "core-js": "^3.8.3", "echarts": "^5.2.2", "element-ui": "^2.15.14", "three": "^0.175.0", "vue": "^2.6.14", "vue-router": "^3.5.1", "vuedraggable": "^2.24.3", "vuex": "^3.6.2"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-nullish-coalescing-operator": "^7.26.6", "@babel/plugin-transform-optional-chaining": "^7.25.9", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.14"}}