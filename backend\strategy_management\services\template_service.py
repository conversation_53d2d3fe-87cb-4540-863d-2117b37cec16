"""
策略模板管理服务
负责策略模板的管理和操作
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
import sys
import os

# 添加正确的路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(os.path.dirname(current_dir))
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

try:
    from backend.app.models.strategy import StrategyTemplate
    from backend.app.schemas.strategy import StrategyTemplateCreate, StrategyTemplateBase
except ImportError:
    # 如果无法导入，使用简化的模型
    class StrategyTemplate:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
            self.id = None
            self.created_at = None

    class StrategyTemplateCreate:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    class StrategyTemplateBase:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
import logging
import json

logger = logging.getLogger(__name__)

class TemplateService:
    """策略模板服务类"""

    def __init__(self, db: Session):
        self.db = db

    def create_template(self, template_data: StrategyTemplateCreate) -> StrategyTemplate:
        """创建策略模板"""
        try:
            # 检查模板名称唯一性
            existing = self.db.query(StrategyTemplate).filter(
                StrategyTemplate.name == template_data.name
            ).first()
            if existing:
                raise ValueError(f"模板名称 '{template_data.name}' 已存在")

            template = StrategyTemplate(
                name=template_data.name,
                type=template_data.type.value,
                category=template_data.category.value,
                description=template_data.description,
                code_template=template_data.code_template,
                parameter_schema=template_data.parameter_schema,
                default_parameters=template_data.default_parameters,
                is_builtin=False
            )

            self.db.add(template)
            self.db.commit()
            self.db.refresh(template)

            logger.info(f"成功创建模板: {template.name} (ID: {template.id})")
            return template

        except Exception as e:
            self.db.rollback()
            logger.error(f"创建模板失败: {str(e)}")
            raise

    def get_template(self, template_id: int) -> Optional[StrategyTemplate]:
        """获取模板详情"""
        return self.db.query(StrategyTemplate).filter(
            StrategyTemplate.id == template_id
        ).first()

    def get_templates(
        self,
        strategy_type: Optional[str] = None,
        category: Optional[str] = None,
        is_builtin: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[StrategyTemplate]:
        """获取模板列表"""
        query = self.db.query(StrategyTemplate)

        if strategy_type:
            query = query.filter(StrategyTemplate.type == strategy_type)
        if category:
            query = query.filter(StrategyTemplate.category == category)
        if is_builtin is not None:
            query = query.filter(StrategyTemplate.is_builtin == is_builtin)

        return query.order_by(StrategyTemplate.created_at.desc()).offset(skip).limit(limit).all()

    def get_templates_by_type(self, strategy_type: str) -> List[StrategyTemplate]:
        """根据策略类型获取模板"""
        return self.db.query(StrategyTemplate).filter(
            StrategyTemplate.type == strategy_type
        ).all()

    def get_builtin_templates(self) -> List[StrategyTemplate]:
        """获取内置模板"""
        return self.db.query(StrategyTemplate).filter(
            StrategyTemplate.is_builtin == True
        ).all()

    def update_template(self, template_id: int, template_data: dict) -> Optional[StrategyTemplate]:
        """更新模板"""
        try:
            template = self.get_template(template_id)
            if not template:
                return None

            # 内置模板不允许修改
            if template.is_builtin:
                raise ValueError("内置模板不允许修改")

            for field, value in template_data.items():
                if hasattr(template, field):
                    setattr(template, field, value)

            self.db.commit()
            self.db.refresh(template)

            logger.info(f"成功更新模板: {template.name} (ID: {template.id})")
            return template

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新模板失败: {str(e)}")
            raise

    def delete_template(self, template_id: int) -> bool:
        """删除模板"""
        try:
            template = self.get_template(template_id)
            if not template:
                return False

            # 内置模板不允许删除
            if template.is_builtin:
                raise ValueError("内置模板不允许删除")

            # 检查是否有策略在使用此模板
            try:
                from backend.app.models.strategy import Strategy
            except ImportError:
                # 如果无法导入，跳过检查
                Strategy = None
            strategies_using = self.db.query(Strategy).filter(
                Strategy.template_id == template_id
            ).count()

            if strategies_using > 0:
                raise ValueError(f"有 {strategies_using} 个策略正在使用此模板，无法删除")

            self.db.delete(template)
            self.db.commit()

            logger.info(f"成功删除模板: {template.name} (ID: {template.id})")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"删除模板失败: {str(e)}")
            raise
