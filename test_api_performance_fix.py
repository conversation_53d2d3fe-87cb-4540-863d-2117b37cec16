#!/usr/bin/env python3
"""
测试API性能监控修复效果
验证前端能否正确显示API数据
"""

import requests
import json
import time

def test_api_performance_fix():
    """测试API性能监控修复效果"""
    print("🎯 测试API性能监控修复效果...")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 1. 获取认证令牌
    print("\n1. 🔐 获取认证令牌...")
    auth_response = requests.post(
        f"{base_url}/api/v1/auth/token",
        json={"username": "admin", "password": "admin123"}
    )
    
    if auth_response.status_code != 200:
        print(f"❌ 认证失败: {auth_response.status_code}")
        return False
    
    token = auth_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 认证成功")
    
    # 2. 测试API统计端点
    print("\n2. 📊 测试API统计端点...")
    stats_response = requests.get(f"{base_url}/api/v1/performance/api/stats", headers=headers)
    
    if stats_response.status_code != 200:
        print(f"❌ API统计获取失败: {stats_response.status_code}")
        return False
    
    stats_data = stats_response.json()
    print("✅ API统计获取成功")
    
    # 验证数据结构
    if not stats_data.get("success"):
        print(f"❌ API响应失败: {stats_data}")
        return False
    
    data = stats_data.get("data", {})
    required_fields = ["avg", "count", "max", "min", "recent", "endpoints"]
    
    for field in required_fields:
        if field not in data:
            print(f"❌ 缺少必需字段: {field}")
            return False
    
    print(f"   ✅ 平均响应时间: {data['avg']:.3f}s")
    print(f"   ✅ 请求数量: {data['count']}")
    print(f"   ✅ 端点数量: {len(data['endpoints'])}")
    
    # 3. 测试端点列表
    print("\n3. 🔗 测试端点列表...")
    endpoints_response = requests.get(f"{base_url}/api/v1/performance/api/endpoints", headers=headers)
    
    if endpoints_response.status_code != 200:
        print(f"❌ 端点列表获取失败: {endpoints_response.status_code}")
        return False
    
    endpoints_data = endpoints_response.json()
    if endpoints_data.get("success"):
        print(f"✅ 端点列表获取成功，共 {len(endpoints_data['data'])} 个端点")
    else:
        print(f"❌ 端点列表响应失败: {endpoints_data}")
        return False
    
    # 4. 生成一些API调用来增加数据
    print("\n4. 🔄 生成更多API调用数据...")
    for i in range(3):
        test_response = requests.get(f"{base_url}/api/v1/performance/summary", headers=headers)
        if test_response.status_code == 200:
            print(f"   ✅ 第{i+1}次测试调用成功")
        time.sleep(0.5)
    
    # 5. 再次检查API统计
    print("\n5. 🔍 验证数据更新...")
    updated_stats_response = requests.get(f"{base_url}/api/v1/performance/api/stats", headers=headers)
    updated_stats_data = updated_stats_response.json()
    
    if updated_stats_data.get("success"):
        updated_data = updated_stats_data.get("data", {})
        old_count = data.get("count", 0)
        new_count = updated_data.get("count", 0)
        
        print(f"   ✅ 更新后请求数量: {new_count} (之前: {old_count})")
        print(f"   ✅ 更新后平均响应时间: {updated_data.get('avg', 0):.3f}s")
        
        if new_count > old_count:
            print("   🎉 数据正在正确更新！")
        else:
            print("   ⚠️ 数据可能未更新")
    
    print("\n" + "=" * 50)
    print("✅ API性能监控测试完成！")
    
    print("\n🎯 前端验证指南:")
    print("1. 访问 http://localhost:8080/#/performance")
    print("2. 点击'API性能'标签页")
    print("3. 应该看到以下数据:")
    print(f"   - 平均响应时间: {updated_data.get('avg', 0):.3f}s")
    print(f"   - 请求数量: {updated_data.get('count', 0)}")
    print(f"   - 端点统计: {len(updated_data.get('endpoints', {}))}")
    print("4. 如果仍显示0值，请检查浏览器控制台日志")
    
    return True

if __name__ == "__main__":
    success = test_api_performance_fix()
    if success:
        print("\n🎉 测试通过！API性能监控应该能正常显示数据。")
    else:
        print("\n❌ 测试失败，请检查后端服务状态。")
