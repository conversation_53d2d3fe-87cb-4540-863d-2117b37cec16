#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import jsonify, request, Blueprint, g
import logging
from datetime import datetime
import uuid
import jwt

# JWT密钥和算法
SECRET_KEY = "your-secret-key-for-testing-only"
ALGORITHM = "HS256"

# 创建蓝图
alert_rules_bp = Blueprint('alert_rules', __name__)

# 认证装饰器
def token_required(f):
    """验证JWT令牌的装饰器"""
    def decorated(*args, **kwargs):
        token = None

        # 从请求头获取token
        auth_header = request.headers.get('Authorization')
        if auth_header:
            token_parts = auth_header.split()
            if len(token_parts) == 2 and token_parts[0].lower() == 'bearer':
                token = token_parts[1]

        if not token:
            logger.warning("未提供认证令牌")
            return jsonify({"detail": "Not authenticated"}), 401

        try:
            # 验证token
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            user_id = int(payload.get("sub"))
            g.user_id = user_id
        except Exception as e:
            logger.error(f"JWT验证错误: {str(e)}")
            return jsonify({"detail": "Invalid token"}), 401

        return f(*args, **kwargs)

    decorated.__name__ = f.__name__
    return decorated

# 设置日志记录
logging.basicConfig(level=logging.DEBUG,
                  format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                  handlers=[
                      logging.StreamHandler(),
                      logging.FileHandler('alert_rules_api.log')
                  ])
logger = logging.getLogger(__name__)

# 告警规则API函数
@alert_rules_bp.route('/api/v1/alert-rules-list', methods=['GET'])
@alert_rules_bp.route('/api/v1/alert-rules', methods=['GET'])
@alert_rules_bp.route('/api/v1/notifications/alert-rules', methods=['GET'])
@alert_rules_bp.route('/api/v1/notifications/rules', methods=['GET'])
@token_required
def get_alert_rules_list():
    """获取告警规则列表"""
    user_id = g.user_id
    logger.info(f"获取告警规则列表请求，用户ID: {user_id}")

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    page_size = request.args.get('page_size', 20, type=int)
    status = request.args.get('status', 'all')
    rule_type = request.args.get('type', 'all')

    # 生成告警规则数据
    rules = [
        {
            "id": "rule-001",
            "name": "BTC价格异常波动监控",
            "type": "price",
            "description": "监控BTC价格短时间内的异常波动",
            "level": "warning",
            "notify_channels": ["app", "email", "sound"],
            "conditions": [
                {
                    "field": "price.change.hourly",
                    "operator": ">",
                    "value": "3",
                    "logic": "OR"
                },
                {
                    "field": "price.change.hourly",
                    "operator": "<",
                    "value": "-3",
                    "logic": "AND"
                }
            ],
            "enabled": True,
            "created_at": "2023-05-20T08:30:00Z",
            "updated_at": "2023-05-25T14:20:00Z"
        },
        {
            "id": "rule-002",
            "name": "系统性能监控",
            "type": "system",
            "description": "监控系统关键性能指标",
            "level": "error",
            "notify_channels": ["app", "email"],
            "conditions": [
                {
                    "field": "system.cpu_usage",
                    "operator": ">",
                    "value": "90",
                    "unit": "%",
                    "logic": "OR"
                },
                {
                    "field": "system.memory_usage",
                    "operator": ">",
                    "value": "85",
                    "unit": "%",
                    "logic": "OR"
                },
                {
                    "field": "system.disk_usage",
                    "operator": ">",
                    "value": "95",
                    "unit": "%",
                    "logic": None
                }
            ],
            "enabled": True,
            "created_at": "2023-04-10T10:15:00Z",
            "updated_at": "2023-05-20T09:45:00Z"
        },
        {
            "id": "rule-003",
            "name": "策略盈亏监控",
            "type": "performance",
            "description": "监控策略盈亏情况",
            "level": "info",
            "notify_channels": ["app"],
            "conditions": [
                {
                    "field": "strategy.daily_pnl",
                    "operator": "<",
                    "value": "-5",
                    "unit": "%",
                    "logic": None
                }
            ],
            "enabled": False,
            "created_at": "2023-06-01T15:30:00Z",
            "updated_at": "2023-06-01T15:30:00Z"
        }
    ]

    # 根据状态过滤
    if status != 'all':
        is_enabled = status == 'enabled'
        rules = [rule for rule in rules if rule['enabled'] == is_enabled]

    # 根据类型过滤
    if rule_type != 'all':
        rules = [rule for rule in rules if rule['type'] == rule_type]

    # 计算分页
    total = len(rules)
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total)
    paginated_rules = rules[start_idx:end_idx]

    # 返回结果
    return jsonify(paginated_rules)

@alert_rules_bp.route('/api/v1/alert-rules/<string:rule_id>', methods=['GET'])
@alert_rules_bp.route('/api/v1/notifications/alert-rules/<string:rule_id>', methods=['GET'])
@alert_rules_bp.route('/api/v1/notifications/rules/<string:rule_id>', methods=['GET'])
@token_required
def get_alert_rule_detail(rule_id):
    """获取单个告警规则详情"""
    user_id = g.user_id
    logger.info(f"获取告警规则详情请求: {rule_id}，用户ID: {user_id}")

    # 模拟查找规则
    rules = {
        "rule-001": {
            "id": "rule-001",
            "name": "BTC价格异常波动监控",
            "type": "price",
            "description": "监控BTC价格短时间内的异常波动",
            "level": "warning",
            "notify_channels": ["app", "email", "sound"],
            "conditions": [
                {
                    "field": "price.change.hourly",
                    "operator": ">",
                    "value": "3",
                    "logic": "OR"
                },
                {
                    "field": "price.change.hourly",
                    "operator": "<",
                    "value": "-3",
                    "logic": "AND"
                }
            ],
            "enabled": True,
            "created_at": "2023-05-20T08:30:00Z",
            "updated_at": "2023-05-25T14:20:00Z"
        },
        "rule-002": {
            "id": "rule-002",
            "name": "系统性能监控",
            "type": "system",
            "description": "监控系统关键性能指标",
            "level": "error",
            "notify_channels": ["app", "email"],
            "conditions": [
                {
                    "field": "system.cpu_usage",
                    "operator": ">",
                    "value": "90",
                    "unit": "%",
                    "logic": "OR"
                },
                {
                    "field": "system.memory_usage",
                    "operator": ">",
                    "value": "85",
                    "unit": "%",
                    "logic": "OR"
                },
                {
                    "field": "system.disk_usage",
                    "operator": ">",
                    "value": "95",
                    "unit": "%",
                    "logic": None
                }
            ],
            "enabled": True,
            "created_at": "2023-04-10T10:15:00Z",
            "updated_at": "2023-05-20T09:45:00Z"
        },
        "rule-003": {
            "id": "rule-003",
            "name": "策略盈亏监控",
            "type": "performance",
            "description": "监控策略盈亏情况",
            "level": "info",
            "notify_channels": ["app"],
            "conditions": [
                {
                    "field": "strategy.daily_pnl",
                    "operator": "<",
                    "value": "-5",
                    "unit": "%",
                    "logic": None
                }
            ],
            "enabled": False,
            "created_at": "2023-06-01T15:30:00Z",
            "updated_at": "2023-06-01T15:30:00Z"
        }
    }

    # 查找规则
    rule = rules.get(rule_id)
    if not rule:
        return jsonify({"error": "规则不存在"}), 404

    # 返回规则详情
    return jsonify(rule)

@alert_rules_bp.route('/api/v1/alert-rules', methods=['POST'])
@alert_rules_bp.route('/api/v1/notifications/alert-rules', methods=['POST'])
@alert_rules_bp.route('/api/v1/notifications/rules', methods=['POST'])
@token_required
def create_alert_rule():
    """创建告警规则"""
    user_id = g.user_id
    logger.info(f"创建告警规则请求，用户ID: {user_id}")

    # 获取请求数据
    data = request.json
    if not data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Invalid request data"}), 400

    # 检查必要字段
    required_fields = ["name", "type", "level", "conditions"]
    for field in required_fields:
        if field not in data:
            return jsonify({"detail": f"Missing required field: {field}"}), 400

    # 生成规则ID
    rule_id = "rule-" + str(uuid.uuid4())[:8]

    # 返回成功响应
    return jsonify({
        "success": True,
        "message": "告警规则已创建",
        "rule_id": rule_id
    })

@alert_rules_bp.route('/api/v1/alert-rules/<string:rule_id>', methods=['PUT'])
@alert_rules_bp.route('/api/v1/notifications/alert-rules/<string:rule_id>', methods=['PUT'])
@alert_rules_bp.route('/api/v1/notifications/rules/<string:rule_id>', methods=['PUT'])
@token_required
def update_alert_rule(rule_id):
    """更新告警规则"""
    user_id = g.user_id
    logger.info(f"更新告警规则请求: {rule_id}，用户ID: {user_id}")

    # 获取请求数据
    data = request.json
    if not data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Invalid request data"}), 400

    # 返回成功响应
    return jsonify({
        "success": True,
        "message": f"告警规则 {rule_id} 已更新"
    })

@alert_rules_bp.route('/api/v1/alert-rules/<string:rule_id>', methods=['DELETE'])
@alert_rules_bp.route('/api/v1/notifications/alert-rules/<string:rule_id>', methods=['DELETE'])
@alert_rules_bp.route('/api/v1/notifications/rules/<string:rule_id>', methods=['DELETE'])
@token_required
def delete_alert_rule(rule_id):
    """删除告警规则"""
    user_id = g.user_id
    logger.info(f"删除告警规则请求: {rule_id}，用户ID: {user_id}")

    # 返回成功响应
    return jsonify({
        "success": True,
        "message": f"告警规则 {rule_id} 已删除"
    })

@alert_rules_bp.route('/api/v1/alert-rules/test', methods=['POST'])
@alert_rules_bp.route('/api/v1/notifications/alert-rules/test', methods=['POST'])
@alert_rules_bp.route('/api/v1/notifications/rules/test', methods=['POST'])
@token_required
def test_alert_rule():
    """测试告警规则"""
    user_id = g.user_id
    logger.info(f"测试告警规则请求，用户ID: {user_id}")

    # 获取请求数据
    data = request.json
    if not data:
        logger.warning("无效的请求数据")
        return jsonify({"detail": "Invalid request data"}), 400

    # 检查必要字段
    required_fields = ["name", "type", "conditions"]
    for field in required_fields:
        if field not in data:
            return jsonify({"detail": f"Missing required field: {field}"}), 400

    # 模拟测试结果
    test_results = {
        "success": True,
        "rule_matched": True,
        "conditions_results": [
            {
                "condition": data["conditions"][0],
                "matched": True,
                "current_value": "4.5" if data["conditions"][0].get("operator") == ">" else "-4.5",
                "timestamp": datetime.now().isoformat()
            }
        ],
        "message": "规则条件已满足，将触发通知"
    }

    # 返回测试结果
    return jsonify(test_results)
