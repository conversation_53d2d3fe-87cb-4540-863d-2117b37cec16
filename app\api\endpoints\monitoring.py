#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Depends, HTTPException, Query, Path, BackgroundTasks, Body
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import asyncio
import logging

from app.db.database import get_db
from sqlalchemy.orm import Session
from app.utils.monitoring.monitor_manager import MonitorManager
from app.utils.data_quality_processor import DataQualityProcessor
from app.services.quality_history_service import QualityHistoryService

router = APIRouter()
logger = logging.getLogger(__name__)

# 监控管理器依赖
def get_monitor_manager():
    return MonitorManager()

# 数据质量处理器依赖
def get_quality_processor():
    return DataQualityProcessor()

# 修复选项模型
class RepairOptions(BaseModel):
    fill_gaps: bool = True
    remove_outliers: bool = True
    sanitize_values: bool = True
    detect_price: bool = True
    detect_volume: bool = True
    check_consistency: bool = True
    save_data: bool = True
    limit: Optional[int] = None

# API路由
@router.get("/dashboard")
async def get_dashboard_data(
    source_ids: str = Query(None, description="数据源ID列表，以逗号分隔"),
    timeframe: str = Query("1d", description="时间级别"),
    start_date: Optional[str] = Query(None, description="开始日期，格式YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式YYYY-MM-DD"),
    include_stats: bool = Query(True, description="是否包含统计数据"),
    include_trends: bool = Query(True, description="是否包含趋势数据"),
    db: Session = Depends(get_db)
):
    """
    获取数据质量监控仪表板数据
    """
    try:
        # 处理数据源ID列表
        source_id_list = []
        if source_ids:
            source_id_list = [int(x) for x in source_ids.split(",")]

        # 处理日期范围
        end_datetime = datetime.now()
        if end_date:
            end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
        
        start_datetime = end_datetime - timedelta(days=30)  # 默认30天
        if start_date:
            start_datetime = datetime.strptime(start_date, "%Y-%m-%d")

        # 创建数据质量处理器
        quality_processor = DataQualityProcessor()

        # 初始化仪表板数据
        dashboard_data = {
            "sources": []
        }

        # 如果没有指定数据源ID，获取所有活跃的数据源
        if not source_id_list:
            # 从数据库获取活跃数据源
            from app.models.data import DataSource
            active_sources = db.query(DataSource).filter(DataSource.is_active == True).all()
            source_id_list = [source.id for source in active_sources]

        # 获取每个数据源的数据
        total_quality_score = 0
        total_completeness = 0
        total_accuracy = 0
        total_timeliness = 0
        anomaly_count = 0
        source_count = 0
        overall_trend = "stable"  # 默认趋势

        for source_id in source_id_list:
            try:
                # 获取数据源信息
                from app.models.data import DataSource
                source = db.query(DataSource).filter(DataSource.id == source_id).first()
                if not source:
                    continue

                # 获取数据源的质量评分
                quality_score = await quality_processor.get_source_quality_score(
                    db, source_id, timeframe, start_datetime, end_datetime
                )

                # 获取数据缺口信息
                gap_data = await quality_processor.get_source_gap_summary(
                    db, source_id, timeframe, start_datetime, end_datetime
                )

                # 获取异常数据信息
                anomaly_data = await quality_processor.get_source_anomaly_summary(
                    db, source_id, timeframe, start_datetime, end_datetime
                )

                # 计算总体指标
                source_count += 1
                total_quality_score += quality_score.get("overall", 0)
                total_completeness += quality_score.get("completeness", 0)
                total_accuracy += quality_score.get("accuracy", 0)
                total_timeliness += quality_score.get("timeliness", 0)
                anomaly_count += anomaly_data.get("total_anomalies", 0)

                # 添加数据源信息到仪表板
                dashboard_data["sources"].append({
                    "source_id": source_id,
                    "name": source.name,
                    "symbol": source.symbol,
                    "exchange": source.source_type,
                    "timeframe": timeframe,
                    "health_summary": {
                        "quality_score": quality_score.get("overall", 0),
                        "completeness": quality_score.get("completeness", 0),
                        "accuracy": quality_score.get("accuracy", 0),
                        "timeliness": quality_score.get("timeliness", 0),
                        "status": "good" if quality_score.get("overall", 0) >= 80 else 
                                 "warning" if quality_score.get("overall", 0) >= 60 else "critical",
                        "last_check": datetime.now().isoformat()
                    },
                    "issues": []  # 可以添加检测到的问题
                })

            except Exception as e:
                logger.error(f"处理数据源 {source_id} 时出错: {str(e)}")
                continue

        # 计算平均分
        if source_count > 0:
            total_quality_score /= source_count
            total_completeness /= source_count
            total_accuracy /= source_count
            total_timeliness /= source_count

            # 添加摘要数据
            dashboard_data["summary"] = {
                "avg_quality_score": round(total_quality_score, 1),
                "completeness_score": round(total_completeness, 1),
                "accuracy_score": round(total_accuracy, 1),
                "timeliness_score": round(total_timeliness, 1),
                "total_gaps": gap_data.get("total_gaps", 0) if 'gap_data' in locals() else 0,
                "total_anomalies": anomaly_count,
                "avg_delay": gap_data.get("avg_delay_minutes", 0) if 'gap_data' in locals() else 0,
                "trend": overall_trend
            }

        # 获取质量趋势数据
        if include_trends:
            trends_data = await QualityHistoryService.get_dashboard_trends(
                db, source_id_list, timeframe,
                (end_datetime - start_datetime).days
            )

            if trends_data:
                dashboard_data["quality_trends"] = trends_data

        return {
            "success": True,
            "data": dashboard_data,
            "message": "数据质量仪表板数据获取成功"
        }
    except Exception as e:
        logger.error(f"获取仪表板数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取仪表板数据失败: {str(e)}")
