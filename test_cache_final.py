#!/usr/bin/env python3
"""
最终测试缓存系统
"""

import requests
import json
import time

def test_cache_final():
    """最终测试缓存系统"""
    
    print("🔍 最终测试缓存系统...")
    print("=" * 60)
    
    # 获取认证token
    print("\n1. 🔐 获取认证令牌...")
    try:
        auth_response = requests.post('http://localhost:8000/api/v1/auth/token', 
                                     json={'username': 'admin', 'password': 'admin123'})
        
        if auth_response.status_code != 200:
            print(f"❌ 认证失败: {auth_response.status_code}")
            return
        
        token = auth_response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print(f"✅ 认证成功")
        
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return
    
    # 检查初始缓存状态
    print("\n2. 📊 检查初始缓存状态...")
    response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
    if response.status_code == 200:
        initial_stats = response.json()['data']
        print(f"   初始缓存统计:")
        print(f"      大小: {initial_stats['size']}")
        print(f"      命中: {initial_stats['hits']}")
        print(f"      未命中: {initial_stats['misses']}")
        print(f"      命中率: {initial_stats['hit_rate']:.2%}")
    else:
        print(f"   ❌ 获取缓存统计失败: {response.status_code}")
        return
    
    # 多次调用有缓存的API
    print("\n3. 🔄 多次调用有缓存的API...")
    
    # 调用性能摘要API 5次
    print("   调用性能摘要API 5次...")
    for i in range(5):
        response = requests.get('http://localhost:8000/api/v1/performance/summary', headers=headers)
        if response.status_code == 200:
            print(f"      第{i+1}次: ✅")
        else:
            print(f"      第{i+1}次: ❌ {response.status_code}")
        time.sleep(0.2)
    
    # 调用性能分析API 3次
    print("   调用性能分析API 3次...")
    for i in range(3):
        response = requests.get('http://localhost:8000/api/v1/performance/analysis', headers=headers)
        if response.status_code == 200:
            print(f"      第{i+1}次: ✅")
        else:
            print(f"      第{i+1}次: ❌ {response.status_code}")
        time.sleep(0.2)
    
    # 调用API统计API 3次
    print("   调用API统计API 3次...")
    for i in range(3):
        response = requests.get('http://localhost:8000/api/v1/performance/api/stats', headers=headers)
        if response.status_code == 200:
            print(f"      第{i+1}次: ✅")
        else:
            print(f"      第{i+1}次: ❌ {response.status_code}")
        time.sleep(0.2)
    
    # 检查缓存状态变化
    print("\n4. 📈 检查缓存状态变化...")
    response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
    if response.status_code == 200:
        final_stats = response.json()['data']
        print(f"   最终缓存统计:")
        print(f"      大小: {final_stats['size']}")
        print(f"      命中: {final_stats['hits']}")
        print(f"      未命中: {final_stats['misses']}")
        print(f"      命中率: {final_stats['hit_rate']:.2%}")
        
        # 计算变化
        size_change = final_stats['size'] - initial_stats['size']
        hits_change = final_stats['hits'] - initial_stats['hits']
        misses_change = final_stats['misses'] - initial_stats['misses']
        
        print(f"\n   📊 缓存活动分析:")
        print(f"      缓存大小变化: +{size_change}")
        print(f"      命中次数变化: +{hits_change}")
        print(f"      未命中次数变化: +{misses_change}")
        
        if size_change > 0:
            print(f"   ✅ 缓存系统正在工作！新增了 {size_change} 个缓存项")
        elif hits_change > 0:
            print(f"   ✅ 缓存系统正在工作！产生了 {hits_change} 次缓存命中")
        else:
            print(f"   ❌ 缓存系统可能没有被使用")
            
    else:
        print(f"   ❌ 获取最终缓存统计失败: {response.status_code}")
    
    # 测试缓存清空功能
    print("\n5. 🧹 测试缓存清空功能...")
    response = requests.post('http://localhost:8000/api/v1/performance/cache/clear', headers=headers)
    if response.status_code == 200:
        print("   ✅ 缓存清空成功")
        
        # 检查清空后的状态
        response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
        if response.status_code == 200:
            cleared_stats = response.json()['data']
            print(f"   清空后缓存统计:")
            print(f"      大小: {cleared_stats['size']}")
            print(f"      命中: {cleared_stats['hits']}")
            print(f"      未命中: {cleared_stats['misses']}")
            
            if cleared_stats['size'] == 0:
                print("   ✅ 缓存已成功清空")
            else:
                print("   ❌ 缓存清空可能失败")
    else:
        print(f"   ❌ 缓存清空失败: {response.status_code}")
    
    # 再次调用API验证缓存重建
    print("\n6. 🔄 再次调用API验证缓存重建...")
    response = requests.get('http://localhost:8000/api/v1/performance/summary', headers=headers)
    if response.status_code == 200:
        print("   ✅ API调用成功")
        
        # 检查缓存是否重建
        response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
        if response.status_code == 200:
            rebuilt_stats = response.json()['data']
            print(f"   重建后缓存统计:")
            print(f"      大小: {rebuilt_stats['size']}")
            print(f"      命中: {rebuilt_stats['hits']}")
            print(f"      未命中: {rebuilt_stats['misses']}")
            
            if rebuilt_stats['size'] > 0:
                print("   ✅ 缓存已成功重建")
            else:
                print("   ❌ 缓存重建可能失败")
    
    print("\n" + "=" * 60)
    print("🔍 缓存系统最终测试完成")

if __name__ == "__main__":
    test_cache_final()
