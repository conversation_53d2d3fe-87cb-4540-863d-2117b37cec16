#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

def test_backup_functionality():
    """测试备份功能"""
    
    base_url = "http://localhost:8000/api/v1/unified-config"
    
    print("🔧 测试备份功能...")
    print("=" * 50)
    
    # 1. 获取当前备份列表
    print("\n1. 获取当前备份列表...")
    try:
        response = requests.get(f"{base_url}/backups")
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取备份列表成功")
            print(f"当前备份数量: {len(result['data'])}")
            for backup in result['data']:
                print(f"  - {backup['filename']} ({backup['size']}, {backup['type']})")
        else:
            print(f"❌ 获取备份列表失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 获取备份列表失败: {e}")
        return
    
    # 2. 创建新备份
    print("\n2. 创建新备份...")
    try:
        backup_data = {"type": "full"}
        response = requests.post(f"{base_url}/backups", json=backup_data)
        if response.status_code == 200:
            result = response.json()
            print("✅ 创建备份成功")
            print(f"备份ID: {result['data']['backup_id']}")
            print(f"备份类型: {result['data']['type']}")
            print(f"总大小: {result['data']['total_size_mb']}")
            print("创建的文件:")
            for file_info in result['data']['created_files']:
                print(f"  - {file_info['filename']} ({file_info['size']}, {file_info['type']})")
        else:
            print(f"❌ 创建备份失败: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"❌ 创建备份失败: {e}")
        return
    
    # 3. 再次获取备份列表验证
    print("\n3. 验证备份是否创建成功...")
    try:
        response = requests.get(f"{base_url}/backups")
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取备份列表成功")
            print(f"更新后备份数量: {len(result['data'])}")
            for backup in result['data'][:3]:  # 只显示前3个
                print(f"  - {backup['filename']} ({backup['size']}, {backup['type']})")
        else:
            print(f"❌ 获取备份列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取备份列表失败: {e}")
    
    # 4. 测试缓存大小功能
    print("\n4. 测试缓存大小功能...")
    try:
        response = requests.get(f"{base_url}/cache-size")
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取缓存大小成功")
            print(f"总大小: {result['data']['total_size_mb']}")
            print("数据库文件:")
            for db_name, size in result['data']['database_files'].items():
                print(f"  - {db_name}: {size}")
            if result['data']['log_files']:
                print("日志文件:")
                for log_name, size in result['data']['log_files'].items():
                    print(f"  - {log_name}: {size}")
        else:
            print(f"❌ 获取缓存大小失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取缓存大小失败: {e}")
    
    # 5. 测试清理缓存功能
    print("\n5. 测试清理缓存功能...")
    try:
        response = requests.post(f"{base_url}/clear-cache")
        if response.status_code == 200:
            result = response.json()
            print("✅ 清理缓存成功")
            print(f"删除缓存文件: {result['data']['cache_files_deleted']} 个")
            print(f"清理日志文件: {result['data']['log_files_cleaned']} 个")
            print(f"释放空间: {result['data']['total_space_freed_mb']}")
            if result['data']['details']:
                print("清理详情:")
                for detail in result['data']['details'][:5]:  # 只显示前5个
                    print(f"  - {detail}")
        else:
            print(f"❌ 清理缓存失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 清理缓存失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 备份功能测试完成！")

if __name__ == "__main__":
    test_backup_functionality()
