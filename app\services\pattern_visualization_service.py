#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging
import numpy as np
import pandas as pd
import json
import base64
from io import BytesIO

logger = logging.getLogger(__name__)

class PatternVisualizationService:
    """
    模式可视化服务，用于生成异常模式的可视化图表
    """
    
    def __init__(self):
        """初始化模式可视化服务"""
        logger.info("模式可视化服务初始化")
    
    async def create_pattern_visualization(
        self,
        pattern_data: Dict[str, Any],
        market_data: List[Dict[str, Any]],
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        为特定异常模式创建可视化
        
        Args:
            pattern_data: 异常模式数据
            market_data: 市场数据
            options: 可视化选项
            
        Returns:
            可视化结果
        """
        try:
            # 默认选项
            default_options = {
                "include_price": True,
                "include_volume": True,
                "include_indicators": True,
                "highlight_pattern": True,
                "chart_type": "candlestick",
                "theme": "light"
            }
            
            # 合并选项
            viz_options = {**default_options, **(options or {})}
            
            # 转换市场数据为DataFrame
            df = pd.DataFrame(market_data)
            
            # 确保时间列是日期时间类型
            if "timestamp" in df.columns:
                df["timestamp"] = pd.to_datetime(df["timestamp"])
            
            # 获取模式时间范围
            pattern_start = datetime.fromisoformat(pattern_data.get("timestamps", {}).get("start", None))
            pattern_end = datetime.fromisoformat(pattern_data.get("timestamps", {}).get("end", None))
            pattern_peak = datetime.fromisoformat(pattern_data.get("timestamps", {}).get("peak", None))
            
            # 准备图表数据
            chart_data = {
                "timestamps": df["timestamp"].dt.strftime("%Y-%m-%d %H:%M:%S").tolist(),
                "pattern": {
                    "id": pattern_data.get("id"),
                    "type": pattern_data.get("type"),
                    "severity": pattern_data.get("severity"),
                    "timestamps": {
                        "start": pattern_start.strftime("%Y-%m-%d %H:%M:%S") if pattern_start else None,
                        "peak": pattern_peak.strftime("%Y-%m-%d %H:%M:%S") if pattern_peak else None,
                        "end": pattern_end.strftime("%Y-%m-%d %H:%M:%S") if pattern_end else None
                    },
                    "description": pattern_data.get("description")
                }
            }
            
            # 添加价格数据
            if viz_options["include_price"]:
                chart_data["price"] = {
                    "open": df["open"].tolist() if "open" in df.columns else None,
                    "high": df["high"].tolist() if "high" in df.columns else None,
                    "low": df["low"].tolist() if "low" in df.columns else None,
                    "close": df["close"].tolist() if "close" in df.columns else None
                }
            
            # 添加交易量数据
            if viz_options["include_volume"] and "volume" in df.columns:
                chart_data["volume"] = df["volume"].tolist()
            
            # 添加指标数据
            if viz_options["include_indicators"]:
                # 计算移动平均线
                if len(df) > 0 and "close" in df.columns:
                    df["ma7"] = df["close"].rolling(window=7).mean()
                    df["ma25"] = df["close"].rolling(window=25).mean()
                    
                    chart_data["indicators"] = {
                        "ma7": df["ma7"].tolist(),
                        "ma25": df["ma25"].tolist()
                    }
            
            # 生成图表配置
            chart_config = {
                "type": viz_options["chart_type"],
                "theme": viz_options["theme"],
                "title": f"{pattern_data.get('type', '未知')}异常模式可视化",
                "subtitle": f"严重程度: {pattern_data.get('severity', '未知')}",
                "highlight_pattern": viz_options["highlight_pattern"]
            }
            
            # 返回可视化结果
            return {
                "success": True,
                "data": {
                    "chart_data": chart_data,
                    "chart_config": chart_config
                }
            }
        except Exception as e:
            logger.error(f"创建模式可视化失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def create_multi_pattern_visualization(
        self,
        patterns_data: List[Dict[str, Any]],
        market_data: List[Dict[str, Any]],
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建多个异常模式的组合可视化
        
        Args:
            patterns_data: 异常模式数据列表
            market_data: 市场数据
            options: 可视化选项
            
        Returns:
            可视化结果
        """
        try:
            # 默认选项
            default_options = {
                "include_price": True,
                "include_volume": True,
                "include_indicators": True,
                "highlight_patterns": True,
                "chart_type": "candlestick",
                "theme": "light"
            }
            
            # 合并选项
            viz_options = {**default_options, **(options or {})}
            
            # 转换市场数据为DataFrame
            df = pd.DataFrame(market_data)
            
            # 确保时间列是日期时间类型
            if "timestamp" in df.columns:
                df["timestamp"] = pd.to_datetime(df["timestamp"])
            
            # 准备图表数据
            chart_data = {
                "timestamps": df["timestamp"].dt.strftime("%Y-%m-%d %H:%M:%S").tolist(),
                "patterns": []
            }
            
            # 添加每个模式的数据
            for pattern in patterns_data:
                pattern_start = datetime.fromisoformat(pattern.get("timestamps", {}).get("start", None))
                pattern_end = datetime.fromisoformat(pattern.get("timestamps", {}).get("end", None))
                pattern_peak = datetime.fromisoformat(pattern.get("timestamps", {}).get("peak", None))
                
                pattern_info = {
                    "id": pattern.get("id"),
                    "type": pattern.get("type"),
                    "severity": pattern.get("severity"),
                    "timestamps": {
                        "start": pattern_start.strftime("%Y-%m-%d %H:%M:%S") if pattern_start else None,
                        "peak": pattern_peak.strftime("%Y-%m-%d %H:%M:%S") if pattern_peak else None,
                        "end": pattern_end.strftime("%Y-%m-%d %H:%M:%S") if pattern_end else None
                    },
                    "description": pattern.get("description")
                }
                
                chart_data["patterns"].append(pattern_info)
            
            # 添加价格数据
            if viz_options["include_price"]:
                chart_data["price"] = {
                    "open": df["open"].tolist() if "open" in df.columns else None,
                    "high": df["high"].tolist() if "high" in df.columns else None,
                    "low": df["low"].tolist() if "low" in df.columns else None,
                    "close": df["close"].tolist() if "close" in df.columns else None
                }
            
            # 添加交易量数据
            if viz_options["include_volume"] and "volume" in df.columns:
                chart_data["volume"] = df["volume"].tolist()
            
            # 添加指标数据
            if viz_options["include_indicators"]:
                # 计算移动平均线
                if len(df) > 0 and "close" in df.columns:
                    df["ma7"] = df["close"].rolling(window=7).mean()
                    df["ma25"] = df["close"].rolling(window=25).mean()
                    
                    chart_data["indicators"] = {
                        "ma7": df["ma7"].tolist(),
                        "ma25": df["ma25"].tolist()
                    }
            
            # 生成图表配置
            chart_config = {
                "type": viz_options["chart_type"],
                "theme": viz_options["theme"],
                "title": f"多模式组合可视化 ({len(patterns_data)}个模式)",
                "highlight_patterns": viz_options["highlight_patterns"]
            }
            
            # 返回可视化结果
            return {
                "success": True,
                "data": {
                    "chart_data": chart_data,
                    "chart_config": chart_config
                }
            }
        except Exception as e:
            logger.error(f"创建多模式组合可视化失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
