<template>
  <div class="strategy-optimization-container">
    <div class="page-header">
      <h1 class="page-title">策略优化建议</h1>
      <p class="page-description">
        分析、优化和比较您的交易策略，提高绩效并降低风险
      </p>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="strategy-selection-card">
          <template #header>
            <div class="card-header">
              <span>选择策略</span>
            </div>
          </template>
          <div class="strategy-selection">
            <el-select
              v-model="selectedStrategyId"
              placeholder="请选择策略"
              class="strategy-select"
              :loading="loadingStrategies"
            >
              <el-option
                v-for="strategy in strategies"
                :key="strategy.id"
                :label="strategy.name"
                :value="strategy.id"
              />
            </el-select>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="dateRangeShortcuts"
              value-format="yyyy-MM-dd"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-tabs v-model="activeTab" type="border-card" class="optimization-tabs">
      <el-tab-pane name="performance" label="绩效分析">
        <div v-if="selectedStrategyId">
          <PerformanceAnalysis
            :strategy-id="selectedStrategyId"
            :date-range="formattedDateRange"
            @data-loaded="handlePerformanceDataLoaded"
          />
        </div>
        <div v-else class="empty-placeholder">
          <div class="empty-container">
            <i class="el-icon-data-analysis empty-icon"></i>
            <p class="empty-text">请选择策略进行分析</p>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane name="optimization" label="优化建议">
        <div v-if="selectedStrategyId">
          <OptimizationSuggestions
            :strategy-id="selectedStrategyId"
            @optimization-applied="handleOptimizationApplied"
          />
        </div>
        <div v-else class="empty-placeholder">
          <div class="empty-container">
            <i class="el-icon-magic-stick empty-icon"></i>
            <p class="empty-text">请选择策略获取优化建议</p>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane name="comparison" label="策略比较">
        <StrategyComparison :strategies="strategies" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import PerformanceAnalysis from '@/components/strategy-optimization/PerformanceAnalysis.vue';
import OptimizationSuggestions from '@/components/strategy-optimization/OptimizationSuggestions.vue';
import StrategyComparison from '@/components/strategy-optimization/StrategyComparison.vue';
import { Message } from 'element-ui';
import { strategyAPI } from '@/api/index';

export default {
  name: 'StrategyOptimization',
  components: {
    PerformanceAnalysis,
    OptimizationSuggestions,
    StrategyComparison
  },
  data() {
    return {
      activeTab: 'performance',
      selectedStrategyId: '',
      loadingStrategies: true,
      strategies: [],
      dateRange: []
    };
  },
  computed: {
    // 格式化日期范围为API请求格式
    formattedDateRange() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        return { startDate: null, endDate: null };
      }
      return {
        startDate: this.dateRange[0],
        endDate: this.dateRange[1]
      };
    },
    // 日期范围快捷选项
    dateRangeShortcuts() {
      return {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      };
    }
  },
  methods: {
    // 从API加载真实策略列表
    async loadStrategies() {
      this.loadingStrategies = true;
      try {
        // 从API获取真实策略数据
        const response = await strategyAPI.getStrategies();

        console.log('API响应原始数据:', response);

        // 处理响应数据格式 - 根据实际API返回结构
        let strategiesData = [];

        // strategyAPI使用原始axios，返回完整的axios响应对象
        // 响应结构: { data: { data: [...], success: true, message: "...", total: 8 }, status: 200, ... }
        if (response && response.data && response.data.data && Array.isArray(response.data.data)) {
          strategiesData = response.data.data;
          console.log('使用原始axios响应格式: response.data.data');
        }
        // 如果是经过request.js处理的格式 (response.data是数组)
        else if (response && response.data && Array.isArray(response.data)) {
          strategiesData = response.data;
          console.log('使用request.js处理后的格式: response.data');
        }
        // 如果response直接是数组（备用情况）
        else if (Array.isArray(response)) {
          strategiesData = response;
          console.log('使用直接数组格式');
        }
        // 如果都不匹配，记录错误信息
        else {
          console.error('无法解析策略数据格式:', response);
          strategiesData = [];
        }

        console.log('处理后的策略数据:', strategiesData);

        // 格式化策略数据，确保ID和name字段正确
        this.strategies = strategiesData.map(strategy => ({
          id: String(strategy.id), // 确保ID是字符串类型
          name: strategy.name || '未命名策略',
          description: strategy.description || '',
          status: strategy.status || 'unknown',
          type: strategy.type || 'custom',
          category: strategy.category || '',
          symbol: strategy.symbol || '',
          timeframe: strategy.timeframe || ''
        }));

        console.log('成功加载策略列表:', this.strategies);

        if (this.strategies.length === 0) {
          console.warn('策略列表为空，请检查API返回数据');
          console.warn('原始响应结构:', JSON.stringify(response, null, 2));
        }

        // 如果有策略，自动选择第一个
        if (this.strategies.length > 0) {
          this.selectedStrategyId = this.strategies[0].id;
          console.log('自动选择第一个策略:', this.selectedStrategyId);
        }
      } catch (error) {
        console.error('加载策略列表失败:', error);
        console.error('错误详情:', error.response || error.message);
        Message.error('加载策略列表失败: ' + (error.message || '未知错误'));

        // 如果API调用失败，使用空数组
        this.strategies = [];
      } finally {
        this.loadingStrategies = false;
      }
    },
    // 处理绩效数据加载完成
    handlePerformanceDataLoaded(data) {
      console.log('绩效数据加载完成:', data);
    },
    // 处理优化应用完成
    handleOptimizationApplied(optimizationData) {
      console.log('策略优化已应用:', optimizationData);
      Message.success(`已成功应用优化参数: ${optimizationData.parameter}`);
    }
  },
  mounted() {
    // 加载策略列表
    this.loadStrategies();

    // 设置默认日期范围为最近3个月
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);

    // 格式化为YYYY-MM-DD
    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    this.dateRange = [formatDate(start), formatDate(end)];
  }
};
</script>

<style scoped>
/* 量子科技美学风格 */
.strategy-optimization-container {
  padding: 20px;
  background: linear-gradient(135deg, #0a0f1c 0%, #1a1f2e 50%, #0a0f1c 100%);
  min-height: 100vh;
  color: #e1e1ff;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
  position: relative;
}

.page-header::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00F7FF, transparent);
  box-shadow: 0 0 10px rgba(0,247,255,0.6);
}

.page-title {
  color: #00F7FF;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 12px;
  text-shadow: 0 0 20px rgba(0,247,255,0.6);
  letter-spacing: 1px;
}

.page-description {
  color: rgba(255,255,255,0.8);
  font-size: 18px;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.strategy-selection-card {
  margin-bottom: 32px;
  background: rgba(6,21,46,0.8);
  border: 1px solid rgba(0,247,255,0.3);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,247,255,0.1);
  backdrop-filter: blur(10px);
}

.strategy-selection-card :deep(.el-card__header) {
  background: rgba(0,247,255,0.1);
  border-bottom: 1px solid rgba(0,247,255,0.2);
  padding: 20px 24px;
}

.strategy-selection-card :deep(.el-card__body) {
  padding: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #00F7FF;
  font-weight: 600;
  font-size: 18px;
  text-shadow: 0 0 10px rgba(0,247,255,0.6);
}

.strategy-selection {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.strategy-select {
  min-width: 280px;
}

.strategy-select :deep(.el-input__inner) {
  background: rgba(6,21,46,0.8);
  border: 1px solid rgba(0,247,255,0.3);
  color: #e1e1ff;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.strategy-select :deep(.el-input__inner):focus {
  border-color: #00F7FF;
  box-shadow: 0 0 15px rgba(0,247,255,0.4);
}

.strategy-select :deep(.el-input__inner):hover {
  border-color: rgba(0,247,255,0.5);
}

.strategy-select :deep(.el-select-dropdown) {
  background: rgba(6,21,46,0.95);
  border: 1px solid rgba(0,247,255,0.3);
  backdrop-filter: blur(10px);
}

.strategy-select :deep(.el-option) {
  color: #e1e1ff;
  background: transparent;
}

.strategy-select :deep(.el-option:hover) {
  background: rgba(0,247,255,0.1);
  color: #00F7FF;
}

.strategy-select :deep(.el-option.selected) {
  background: rgba(0,247,255,0.2);
  color: #00F7FF;
}

/* 日期选择器样式 */
:deep(.el-date-editor) {
  background: rgba(6,21,46,0.8);
  border: 1px solid rgba(0,247,255,0.3);
  border-radius: 8px;
}

:deep(.el-date-editor .el-range-input) {
  background: transparent;
  color: #e1e1ff;
}

:deep(.el-date-editor .el-range-separator) {
  color: rgba(255,255,255,0.6);
}

:deep(.el-date-editor:hover) {
  border-color: rgba(0,247,255,0.5);
}

:deep(.el-date-editor.is-active) {
  border-color: #00F7FF;
  box-shadow: 0 0 15px rgba(0,247,255,0.4);
}

.optimization-tabs {
  background: rgba(6,21,46,0.8);
  border: 1px solid rgba(0,247,255,0.3);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,247,255,0.1);
  backdrop-filter: blur(10px);
}

.optimization-tabs :deep(.el-tabs__header) {
  background: rgba(0,247,255,0.05);
  margin: 0;
  border-bottom: 1px solid rgba(0,247,255,0.2);
  border-radius: 12px 12px 0 0;
}

.optimization-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 24px;
}

.optimization-tabs :deep(.el-tabs__item) {
  color: rgba(255,255,255,0.7);
  font-weight: 500;
  font-size: 16px;
  padding: 0 24px;
  height: 50px;
  line-height: 50px;
  border: none;
  transition: all 0.3s ease;
}

.optimization-tabs :deep(.el-tabs__item:hover) {
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
}

.optimization-tabs :deep(.el-tabs__item.is-active) {
  color: #00F7FF;
  text-shadow: 0 0 12px rgba(0,247,255,0.8);
  background: rgba(0,247,255,0.1);
}

.optimization-tabs :deep(.el-tabs__active-bar) {
  background: #00F7FF;
  height: 3px;
  box-shadow: 0 0 10px rgba(0,247,255,0.8);
}

.optimization-tabs :deep(.el-tabs__content) {
  padding: 32px 24px;
  min-height: 400px;
}

.empty-placeholder {
  padding: 80px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.empty-container {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  font-size: 64px;
  color: rgba(0,247,255,0.6);
  margin-bottom: 24px;
  text-shadow: 0 0 20px rgba(0,247,255,0.4);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

.empty-text {
  color: rgba(255,255,255,0.8);
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .strategy-optimization-container {
    padding: 16px;
  }

  .page-title {
    font-size: 28px;
  }

  .page-description {
    font-size: 16px;
  }

  .strategy-selection {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .strategy-select {
    width: 100%;
    min-width: auto;
  }

  .optimization-tabs :deep(.el-tabs__content) {
    padding: 24px 16px;
  }

  .empty-placeholder {
    padding: 60px 16px;
  }

  .empty-icon {
    font-size: 48px;
  }

  .empty-text {
    font-size: 16px;
  }
}

/* 加载状态样式 */
.strategy-select :deep(.el-loading-mask) {
  background: rgba(6,21,46,0.8);
  backdrop-filter: blur(5px);
}

.strategy-select :deep(.el-loading-spinner) {
  color: #00F7FF;
}

/* 滚动条样式 */
:deep(.el-scrollbar__wrap) {
  scrollbar-width: thin;
  scrollbar-color: rgba(0,247,255,0.3) transparent;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar) {
  width: 6px;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-track) {
  background: rgba(6,21,46,0.3);
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb) {
  background: rgba(0,247,255,0.3);
  border-radius: 3px;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb:hover) {
  background: rgba(0,247,255,0.5);
}
</style>