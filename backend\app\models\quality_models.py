#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据质量模型定义
这个模块定义了所有与数据质量相关的数据库模型，避免循环导入问题
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, ForeignKey, JSON, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from backend.app.db.database import Base

class DataQualityRecord(Base):
    """数据质量记录表，存储每个数据源每个时间级别的质量评估历史"""
    __tablename__ = "data_quality_records"
    __table_args__ = (
        Index('idx_quality_source_time', 'source_id', 'timeframe', 'record_time'),
        Index('idx_quality_health', 'health_status', 'record_time'),
        {'extend_existing': True}
    )

    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, nullable=False, index=True)
    exchange = Column(String(50), nullable=False, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    timeframe = Column(String(10), nullable=False, index=True)

    # 质量评估指标
    quality_score = Column(Float, nullable=False, default=0)
    quality_grade = Column(String(2), nullable=False, default="F")
    health_status = Column(String(20), nullable=False, default="unknown")

    # 详细统计信息
    anomaly_stats = Column(JSON, nullable=True)
    gap_stats = Column(JSON, nullable=True)

    # 记录时间
    record_time = Column(DateTime, server_default=func.now(), index=True)
    data_timestamp = Column(DateTime, nullable=False, index=True)  # 数据对应的时间点

    def __repr__(self):
        return f"<DataQualityRecord {self.source_id}:{self.timeframe} score={self.quality_score}>"


class DataQualityAlert(Base):
    """数据质量告警记录表，存储系统生成的所有质量相关告警"""
    __tablename__ = "data_quality_alerts"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, nullable=False, index=True)
    exchange = Column(String(50), nullable=False)
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)

    # 告警信息
    alert_type = Column(String(50), nullable=False, index=True)  # gap, anomaly, consistency, health
    severity = Column(String(20), nullable=False, index=True)  # info, warning, error, critical
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)

    # 状态管理
    is_resolved = Column(Boolean, default=False, index=True)
    resolved_at = Column(DateTime, nullable=True)
    resolution_notes = Column(Text, nullable=True)

    # 记录时间
    created_at = Column(DateTime, server_default=func.now(), index=True)

    def __repr__(self):
        return f"<DataQualityAlert {self.alert_type}:{self.severity} {self.source_id}:{self.timeframe}>"


class DataQualityTrend(Base):
    """数据质量趋势表，存储按天汇总的质量趋势数据"""
    __tablename__ = "data_quality_trends"
    __table_args__ = (
        Index('idx_trend_source_date', 'source_id', 'timeframe', 'date'),
        Index('idx_trend_direction', 'quality_trend', 'date'),
        {'extend_existing': True}
    )

    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, nullable=False, index=True)
    exchange = Column(String(50), nullable=False)
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False, index=True)

    # 趋势统计数据
    date = Column(DateTime, nullable=False, index=True)
    avg_quality_score = Column(Float, nullable=False, default=0)
    min_quality_score = Column(Float, nullable=True)
    max_quality_score = Column(Float, nullable=True)
    quality_trend = Column(String(20), nullable=False, default="stable")  # improving, declining, stable

    # 详细统计数据
    alert_count = Column(Integer, default=0)
    anomaly_rate = Column(Float, default=0)
    gap_count = Column(Integer, default=0)
    data_points = Column(Integer, default=0)  # 当天数据点总数

    # 趋势属性
    trend_strength = Column(Float, default=0)  # 趋势强度，0-1
    volatility = Column(Float, default=0)      # 波动程度，0-100

    # 创建时间
    created_at = Column(DateTime, server_default=func.now())

    def __repr__(self):
        return f"<DataQualityTrend {self.source_id}:{self.timeframe} {self.date.strftime('%Y-%m-%d')} {self.quality_trend}>"


class QualityReportConfig(Base):
    """质量报告配置表，存储报告生成的配置信息"""
    __tablename__ = "quality_report_configs"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)

    # 报告生成配置
    enabled = Column(Boolean, default=True)
    daily_report = Column(Boolean, default=True)
    weekly_report = Column(Boolean, default=True)
    monthly_report = Column(Boolean, default=True)

    # 生成时间配置
    daily_time = Column(String(5), default="01:00")  # HH:MM格式
    weekly_day = Column(Integer, default=1)  # 1-7表示周一到周日
    monthly_day = Column(Integer, default=1)  # 1-28表示每月第几天

    # 内容配置
    include_trends = Column(Boolean, default=True)
    include_alerts = Column(Boolean, default=True)
    include_recommendations = Column(Boolean, default=True)

    # 通知配置
    notify_on_completion = Column(Boolean, default=False)
    notification_recipients = Column(JSON, nullable=True)  # 邮箱或其他通知目标

    # 更新时间
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<QualityReportConfig id={self.id} enabled={self.enabled}>"
