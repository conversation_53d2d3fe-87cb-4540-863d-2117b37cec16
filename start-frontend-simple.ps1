# 简化的前端启动脚本 - 确保启动在8080端口

Write-Host "=== 前端服务启动脚本 ===" -ForegroundColor Green
Write-Host "目标端口: 8080" -ForegroundColor Yellow

# 检查8080端口占用情况
Write-Host "检查8080端口占用情况..." -ForegroundColor Cyan
$port8080 = netstat -ano | findstr ":8080"

if ($port8080) {
    Write-Host "发现8080端口被占用，尝试关闭相关进程..." -ForegroundColor Yellow
    
    # 获取占用8080端口的进程
    $processes = netstat -ano | findstr ":8080" | findstr "LISTENING"
    
    if ($processes) {
        foreach ($line in $processes) {
            $parts = $line -split '\s+' | Where-Object { $_ -ne '' }
            $pid = $parts[-1]
            
            if ($pid -and $pid -match '^\d+$') {
                Write-Host "尝试关闭进程 PID: $pid" -ForegroundColor Yellow
                
                try {
                    $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                    if ($process -and $process.ProcessName -eq "node") {
                        Stop-Process -Id $pid -Force
                        Write-Host "已关闭Node.js进程 PID: $pid" -ForegroundColor Green
                    }
                } catch {
                    Write-Host "无法关闭进程 PID: $pid" -ForegroundColor Red
                }
            }
        }
        
        Start-Sleep -Seconds 3
    }
}

# 检查前端目录
if (-not (Test-Path "frontend")) {
    Write-Host "错误: 未找到frontend目录" -ForegroundColor Red
    exit 1
}

# 进入前端目录
Set-Location frontend

# 检查依赖
if (-not (Test-Path "node_modules")) {
    Write-Host "安装依赖..." -ForegroundColor Yellow
    npm install
}

Write-Host "启动前端开发服务器..." -ForegroundColor Green
Write-Host "目标地址: http://localhost:8080" -ForegroundColor Yellow

# 启动前端服务
npm run serve -- --port 8080
