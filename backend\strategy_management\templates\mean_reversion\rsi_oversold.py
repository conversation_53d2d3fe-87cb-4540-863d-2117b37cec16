"""
RSI超买超卖策略模板
适用于均值回归交易

策略逻辑：
- 当RSI低于超卖水平时，产生买入信号
- 当RSI高于超买水平时，产生卖出信号
- 支持止损和止盈功能
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def initialize(context):
    """策略初始化"""
    context.rsi_period = {{ rsi_period | default(14) }}
    context.overbought_level = {{ overbought_level | default(70.0) }}
    context.oversold_level = {{ oversold_level | default(30.0) }}
    context.use_stop_loss = {{ use_stop_loss | default(True) }}
    context.stop_loss_rate = {{ stop_loss_rate | default(0.03) }}
    context.use_take_profit = {{ use_take_profit | default(True) }}
    context.take_profit_rate = {{ take_profit_rate | default(0.05) }}
    
    # 内部状态
    context.position = 0  # 当前仓位
    context.entry_price = 0  # 入场价格
    context.signals = []  # 信号历史

def handle_bar(context, data):
    """处理每个K线数据"""
    # 获取收盘价
    close_prices = data['close']
    
    # 检查数据长度是否足够
    if len(close_prices) < context.rsi_period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}
    
    # 计算RSI
    rsi = calculate_rsi(close_prices, context.rsi_period)
    current_rsi = rsi.iloc[-1]
    current_price = close_prices.iloc[-1]
    
    # 检查RSI数据有效性
    if pd.isna(current_rsi):
        return {'action': 'HOLD', 'reason': 'RSI数据无效'}
    
    # 超卖买入信号
    if current_rsi < context.oversold_level and context.position <= 0:
        context.position = 1
        context.entry_price = current_price
        signal = {
            'action': 'BUY',
            'price': current_price,
            'reason': f'RSI超卖买入 (RSI: {current_rsi:.2f})',
            'rsi': current_rsi,
            'timestamp': data.index[-1] if hasattr(data, 'index') else None
        }
        context.signals.append(signal)
        return signal
    
    # 超买卖出信号
    if current_rsi > context.overbought_level and context.position >= 0:
        context.position = -1
        signal = {
            'action': 'SELL',
            'price': current_price,
            'reason': f'RSI超买卖出 (RSI: {current_rsi:.2f})',
            'rsi': current_rsi,
            'timestamp': data.index[-1] if hasattr(data, 'index') else None
        }
        if context.position > 0:
            signal['profit_loss'] = current_price - context.entry_price
            signal['profit_loss_pct'] = (current_price - context.entry_price) / context.entry_price * 100
        context.signals.append(signal)
        return signal
    
    # 止损检查
    if context.use_stop_loss and context.position > 0 and context.entry_price > 0:
        loss_rate = (context.entry_price - current_price) / context.entry_price
        if loss_rate > context.stop_loss_rate:
            context.position = 0
            signal = {
                'action': 'SELL',
                'price': current_price,
                'reason': f'止损 (损失{loss_rate:.2%})',
                'profit_loss': current_price - context.entry_price,
                'profit_loss_pct': -loss_rate * 100,
                'rsi': current_rsi,
                'timestamp': data.index[-1] if hasattr(data, 'index') else None
            }
            context.signals.append(signal)
            return signal
    
    # 止盈检查
    if context.use_take_profit and context.position > 0 and context.entry_price > 0:
        profit_rate = (current_price - context.entry_price) / context.entry_price
        if profit_rate > context.take_profit_rate:
            context.position = 0
            signal = {
                'action': 'SELL',
                'price': current_price,
                'reason': f'止盈 (盈利{profit_rate:.2%})',
                'profit_loss': current_price - context.entry_price,
                'profit_loss_pct': profit_rate * 100,
                'rsi': current_rsi,
                'timestamp': data.index[-1] if hasattr(data, 'index') else None
            }
            context.signals.append(signal)
            return signal
    
    return {'action': 'HOLD'}

def get_indicators(context, data):
    """获取技术指标数据（用于图表显示）"""
    close_prices = data['close']
    
    if len(close_prices) < context.rsi_period + 1:
        return {}
    
    rsi = calculate_rsi(close_prices, context.rsi_period)
    
    return {
        'rsi': rsi,
        'overbought_level': context.overbought_level,
        'oversold_level': context.oversold_level,
        'price': close_prices
    }

def get_performance_metrics(context):
    """获取策略绩效指标"""
    if not context.signals:
        return {}
    
    # 计算交易统计
    buy_signals = [s for s in context.signals if s['action'] == 'BUY']
    sell_signals = [s for s in context.signals if s['action'] == 'SELL' and 'profit_loss' in s]
    
    total_trades = len(sell_signals)
    if total_trades == 0:
        return {'total_trades': 0}
    
    # 盈亏统计
    profits = [s['profit_loss'] for s in sell_signals if s['profit_loss'] > 0]
    losses = [s['profit_loss'] for s in sell_signals if s['profit_loss'] < 0]
    
    win_rate = len(profits) / total_trades * 100 if total_trades > 0 else 0
    avg_profit = np.mean(profits) if profits else 0
    avg_loss = np.mean(losses) if losses else 0
    profit_factor = abs(sum(profits) / sum(losses)) if losses and sum(losses) != 0 else float('inf')
    
    # RSI相关统计
    rsi_values = [s.get('rsi', 0) for s in context.signals if 'rsi' in s]
    avg_rsi = np.mean(rsi_values) if rsi_values else 0
    
    return {
        'total_trades': total_trades,
        'win_rate': win_rate,
        'avg_profit': avg_profit,
        'avg_loss': avg_loss,
        'profit_factor': profit_factor,
        'total_profit_loss': sum(s['profit_loss'] for s in sell_signals),
        'avg_rsi': avg_rsi
    }

# 策略元数据
STRATEGY_METADATA = {
    'name': 'RSI超买超卖策略',
    'category': 'mean_reversion',
    'description': '基于RSI指标的均值回归策略',
    'parameters': [
        'rsi_period', 'overbought_level', 'oversold_level', 
        'use_stop_loss', 'stop_loss_rate', 'use_take_profit', 'take_profit_rate'
    ],
    'indicators': ['rsi'],
    'timeframes': ['15m', '1h', '4h'],
    'markets': ['crypto', 'forex', 'stocks']
}
