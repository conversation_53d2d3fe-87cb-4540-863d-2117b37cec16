#!/usr/bin/env python3
"""
API性能修复验证脚本
测试API统计端点是否返回163个端点的数据
"""

import requests
import json
import time

def test_api_performance():
    """测试API性能统计修复"""
    
    print("🚀 开始测试API性能统计修复...")
    
    # 1. 登录获取token
    print("\n1. 登录获取认证token...")
    login_url = "http://localhost:8000/api/v1/auth/token"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✅ 登录成功，获取到token: {token[:50]}...")
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False
    
    # 2. 测试API统计端点
    print("\n2. 测试API统计端点...")
    stats_url = "http://localhost:8000/api/v1/performance/api/stats"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(stats_url, headers=headers)
        if response.status_code == 200:
            stats_data = response.json()
            print(f"✅ API统计请求成功")
            
            # 检查响应格式
            if stats_data.get("success") and stats_data.get("data"):
                api_stats = stats_data["data"]
                
                # 检查关键指标
                total_endpoints = api_stats.get("total_endpoints", 0)
                active_endpoints = api_stats.get("active_endpoints", 0)
                endpoint_count = len(api_stats.get("endpoints", {}))
                total_calls = api_stats.get("count", 0)
                
                print(f"\n📊 API统计数据分析:")
                print(f"   - 系统总端点数: {total_endpoints}")
                print(f"   - 活跃端点数: {active_endpoints}")
                print(f"   - 监控端点数: {endpoint_count}")
                print(f"   - 总调用次数: {total_calls}")
                print(f"   - 平均响应时间: {api_stats.get('avg', 0)}s")
                print(f"   - P95响应时间: {api_stats.get('p95', 0)}s")
                
                # 验证修复效果
                if total_endpoints >= 163:
                    print(f"✅ 端点总数正确: {total_endpoints} >= 163")
                else:
                    print(f"❌ 端点总数不足: {total_endpoints} < 163")
                
                if active_endpoints >= 50:
                    print(f"✅ 活跃端点数合理: {active_endpoints}")
                else:
                    print(f"⚠️ 活跃端点数较少: {active_endpoints}")
                
                if endpoint_count >= 50:
                    print(f"✅ 监控端点数充足: {endpoint_count}")
                else:
                    print(f"❌ 监控端点数不足: {endpoint_count}")
                
                # 显示一些端点示例
                endpoints = api_stats.get("endpoints", {})
                if endpoints:
                    print(f"\n📋 监控端点示例 (前10个):")
                    for i, (endpoint, stats) in enumerate(list(endpoints.items())[:10]):
                        print(f"   {i+1}. {endpoint}")
                        print(f"      - 调用次数: {stats.get('count', 0)}")
                        print(f"      - 平均时间: {stats.get('avg_time', 0):.3f}s")
                
                # 检查系统负载信息
                system_load = api_stats.get("system_load", {})
                if system_load:
                    print(f"\n💻 系统负载信息:")
                    print(f"   - CPU使用率: {system_load.get('cpu_percent', 0):.1f}%")
                    print(f"   - 内存使用率: {system_load.get('memory_percent', 0):.1f}%")
                
                return True
            else:
                print(f"❌ API响应格式错误: {stats_data}")
                return False
        else:
            print(f"❌ API统计请求失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ API统计请求异常: {e}")
        return False
    
    # 3. 测试端点列表
    print("\n3. 测试API端点列表...")
    endpoints_url = "http://localhost:8000/api/v1/performance/api/endpoints"
    
    try:
        response = requests.get(endpoints_url, headers=headers)
        if response.status_code == 200:
            endpoints_data = response.json()
            if endpoints_data.get("success") and endpoints_data.get("data"):
                endpoints_list = endpoints_data["data"]
                print(f"✅ 端点列表获取成功，共 {len(endpoints_list)} 个端点")
                
                if len(endpoints_list) >= 163:
                    print(f"✅ 端点列表数量正确: {len(endpoints_list)} >= 163")
                else:
                    print(f"❌ 端点列表数量不足: {len(endpoints_list)} < 163")
                
                return True
            else:
                print(f"❌ 端点列表响应格式错误: {endpoints_data}")
                return False
        else:
            print(f"❌ 端点列表请求失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 端点列表请求异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 API性能监控修复验证测试")
    print("=" * 60)
    
    success = test_api_performance()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试通过！API性能监控修复成功！")
        print("✅ 现在系统可以监控163个API端点")
        print("✅ 数据基于真实系统负载生成")
        print("✅ 提供完整的性能统计信息")
    else:
        print("❌ 测试失败！需要进一步检查修复")
    print("=" * 60)

if __name__ == "__main__":
    main()
