import request from '@/utils/request'

const dataAPI = {
  // 获取可用交易对
  getSymbols() {
    return request({
      url: '/symbols',
      method: 'get'
    })
  },

  // 获取可用时间周期
  getTimeframes() {
    return request({
      url: '/timeframes',
      method: 'get'
    })
  },

  // 获取K线数据
  getKlineData(params) {
    return request({
      url: '/klines',
      method: 'get',
      params
    })
  },

  // 获取市场数据
  getMarketData(params) {
    return request({
      url: '/market-data',
      method: 'get',
      params
    })
  },

  // 获取深度数据
  getDepthData(params) {
    return request({
      url: '/depth',
      method: 'get',
      params
    })
  },

  // 获取最新价格
  getLatestPrice(symbol) {
    return request({
      url: `/price/${symbol}`,
      method: 'get'
    })
  },

  // 获取24小时统计
  get24hrStats(symbol) {
    return request({
      url: `/24hr-stats/${symbol}`,
      method: 'get'
    })
  }
}

export default dataAPI
