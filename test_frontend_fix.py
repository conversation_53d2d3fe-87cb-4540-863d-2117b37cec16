#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端修复验证脚本
验证SystemSettings页面的API调用和Vue组件是否正常工作
"""

import requests
import time

BASE_URL = "http://localhost:8000"

def test_system_settings_apis():
    """测试系统设置相关的API"""
    print("🔧 测试系统设置页面相关API...")
    
    apis_to_test = [
        ("/api/v1/config/system", "系统配置API"),
        ("/api/v1/config/api-keys", "API密钥配置API"),
        ("/api/v1/config/system-params", "系统参数API"),
        ("/api/v1/config/data-maintenance", "数据维护配置API"),
        ("/api/v1/config/backups", "备份文件列表API"),
        ("/api/v1/config/cache-size", "缓存大小API")
    ]
    
    all_passed = True
    
    for endpoint, name in apis_to_test:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                # 检查响应格式
                if isinstance(data, dict) and data.get('success', True):
                    print(f"   ✅ {name}: 正常 (状态码: {response.status_code})")
                else:
                    print(f"   ⚠️ {name}: 响应格式异常 - {data}")
            else:
                print(f"   ❌ {name}: HTTP {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"   ❌ {name}: 异常 - {str(e)}")
            all_passed = False
    
    return all_passed

def test_deprecated_apis():
    """测试已废弃的API是否正确处理"""
    print("\n🚫 测试已废弃的API处理...")
    
    deprecated_apis = [
        ("/api/v1/unified-config/system", "旧系统配置API"),
        ("/api/v1/unified-config/api-keys", "旧API密钥配置API")
    ]
    
    for endpoint, name in deprecated_apis:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            if response.status_code == 404:
                print(f"   ✅ {name}: 已正确移除 (404)")
            elif response.status_code == 405:
                print(f"   ✅ {name}: 方法不允许 (405) - 正常")
            else:
                print(f"   ⚠️ {name}: 仍可访问 (状态码: {response.status_code})")
        except Exception as e:
            print(f"   ✅ {name}: 连接失败，已移除")

def main():
    """主函数"""
    print("前端修复验证测试")
    print("验证SystemSettings页面的API修复是否成功")
    print("=" * 50)
    
    # 检查后端服务
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except:
        print("❌ 无法连接后端服务")
        return False
    
    # 测试API
    api_test_passed = test_system_settings_apis()
    test_deprecated_apis()
    
    print("\n📊 测试结果:")
    if api_test_passed:
        print("✅ 所有系统设置API正常工作")
        print("✅ API路径修复成功")
        print("✅ 前端页面应该可以正常加载数据")
    else:
        print("❌ 部分API存在问题")
    
    print("\n🌐 前端验证:")
    print("请访问以下页面验证前端是否正常:")
    print("   - 系统设置: http://localhost:8080/#/settings")
    print("   - 检查浏览器控制台是否还有错误")
    print("   - 验证各个标签页是否可以正常切换")
    print("   - 验证数据是否正常加载")
    
    return api_test_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
