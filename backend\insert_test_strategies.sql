-- 插入测试策略数据

INSERT INTO strategies (
    name, type, category, description, code_type, code_content, 
    parameters, symbol, timeframe, status, is_active, 
    created_at, updated_at, creator_id
) VALUES 
(
    '测试双均线策略', 
    'trend_following', 
    'dual_ma_cross', 
    '测试用的双均线交叉策略', 
    'python', 
    'def initialize(context): pass', 
    '{"short_period": 5, "long_period": 20}', 
    'BTCUSDT', 
    '1h', 
    'active', 
    1, 
    datetime("now"), 
    datetime("now"), 
    1
),
(
    '测试RSI策略', 
    'mean_reversion', 
    'rsi_oversold', 
    '测试用的RSI超买超卖策略', 
    'python', 
    'def initialize(context): pass', 
    '{"rsi_period": 14, "overbought_level": 70, "oversold_level": 30}', 
    'ETHUSDT', 
    '4h', 
    'created', 
    0, 
    datetime("now"), 
    datetime("now"), 
    1
),
(
    '测试网格策略', 
    'grid', 
    'fixed_grid', 
    '测试用的固定网格策略', 
    'python', 
    'def initialize(context): pass', 
    '{"grid_size": 0.01, "grid_count": 10}', 
    'BTCUSDT', 
    '15m', 
    'inactive', 
    0, 
    datetime("now"), 
    datetime("now"), 
    1
);
