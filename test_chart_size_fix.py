#!/usr/bin/env python3
"""
测试图表尺寸修复效果
"""

import requests
import json
import time

def test_chart_size_fix():
    """测试图表尺寸修复效果"""
    
    print("🔍 开始测试图表尺寸修复效果...")
    print("=" * 60)
    
    # 获取认证token
    print("\n1. 🔐 获取认证令牌...")
    try:
        auth_response = requests.post('http://localhost:8000/api/v1/auth/token', 
                                     json={'username': 'admin', 'password': 'admin123'})
        
        if auth_response.status_code != 200:
            print(f"❌ 认证失败: {auth_response.status_code}")
            return
        
        token = auth_response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print(f"✅ 认证成功")
        
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return
    
    # 测试所有metrics API端点
    print("\n2. 📊 测试所有图表数据源...")
    categories = ['cpu', 'memory', 'api', 'cache']
    
    all_success = True
    chart_data = {}
    
    for category in categories:
        try:
            response = requests.get(f'http://localhost:8000/api/v1/performance/metrics/{category}?limit=5', 
                                   headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    chart_data[category] = data['data']
                    print(f"   ✅ {category}图表数据: {len(data['data'])}条记录")
                    
                    # 显示数据样本
                    sample = data['data'][0]
                    if category == 'cpu':
                        print(f"      样本: CPU使用率 {sample.get('percent', 0):.1f}%")
                    elif category == 'memory':
                        print(f"      样本: 内存使用率 {sample.get('virtual', {}).get('percent', 0):.1f}%")
                    elif category == 'api':
                        print(f"      样本: API响应时间 {sample.get('avg', 0):.3f}s")
                    elif category == 'cache':
                        print(f"      样本: 缓存命中率 {sample.get('hit_rate', 0)*100:.1f}%")
                else:
                    print(f"   ❌ {category}图表数据: 无数据")
                    all_success = False
            else:
                print(f"   ❌ {category}图表数据: HTTP {response.status_code}")
                all_success = False
                
        except Exception as e:
            print(f"   ❌ {category}图表数据: 异常 {e}")
            all_success = False
    
    # 验证数据完整性
    print("\n3. 🔍 验证图表数据完整性...")
    for category in categories:
        if category in chart_data:
            data_points = chart_data[category]
            print(f"   📈 {category}图表:")
            print(f"      数据点数量: {len(data_points)}")
            
            # 检查数据字段
            if data_points:
                sample = data_points[0]
                required_fields = {
                    'cpu': ['timestamp', 'percent'],
                    'memory': ['timestamp', 'virtual'],
                    'api': ['timestamp', 'avg', 'max'],
                    'cache': ['timestamp', 'hit_rate', 'size']
                }
                
                missing_fields = []
                for field in required_fields[category]:
                    if field not in sample:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"      ❌ 缺少字段: {missing_fields}")
                    all_success = False
                else:
                    print(f"      ✅ 数据字段完整")
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📋 图表尺寸修复测试报告:")
    
    if all_success:
        print("🎉 后端数据测试通过！")
        print("\n✅ 修复验证结果:")
        print("   - 后端API端点全部正常工作")
        print("   - 所有图表数据源都有真实数据")
        print("   - 数据字段完整，格式正确")
        print("\n🔧 前端修复内容:")
        print("   - 添加了强制设置图表DOM尺寸的逻辑")
        print("   - 使用延迟初始化确保Tab切换动画完成")
        print("   - 添加了重试机制处理初始化失败的情况")
        print("   - 优化了CSS样式确保图表容器有正确尺寸")
        print("\n🚀 现在请测试前端页面:")
        print("   1. 访问: http://localhost:8080/#/performance")
        print("   2. 点击不同的Tab页面查看图表")
        print("   3. 每个图表都应该正常显示，不再有尺寸问题")
        print("   4. 控制台应该显示图表初始化成功的日志")
    else:
        print("❌ 部分测试失败")
        print("   请检查后端服务状态和API实现")
    
    print("\n" + "=" * 60)
    print(f"测试完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    test_chart_size_fix()
