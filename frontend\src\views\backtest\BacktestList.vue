<template>
  <div class="backtest-list-container">
    <div class="page-header">
      <h2>回测管理</h2>
      <el-button type="primary" icon="el-icon-plus" @click="$router.push('/backtests/create')">
        创建回测
      </el-button>
    </div>

    <el-card class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="策略">
          <el-select v-model="filterForm.strategyId" placeholder="选择策略" clearable>
            <el-option
              v-for="item in strategies"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-container">
      <div class="table-header">
        <span>回测结果列表</span>
        <el-button icon="el-icon-refresh" circle @click="fetchData"></el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="backtests"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="strategy_name" label="策略名称" width="150"></el-table-column>
        <el-table-column prop="start_date" label="开始日期" width="180">
          <template slot-scope="scope">
            <span>{{ formatDate(scope.row.start_date) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="end_date" label="结束日期" width="180">
          <template slot-scope="scope">
            <span>{{ formatDate(scope.row.end_date) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_profit" label="总收益" width="120">
          <template slot-scope="scope">
            <span :class="scope.row.total_profit >= 0 ? 'profit' : 'loss'">
              {{ formatProfit(scope.row.total_profit) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="win_rate" label="胜率" width="100">
          <template slot-scope="scope">
            <span>{{ formatPercent(scope.row.win_rate) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="max_drawdown" label="最大回撤" width="100">
          <template slot-scope="scope">
            <span>{{ formatPercent(scope.row.max_drawdown) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sharpe_ratio" label="夏普比率" width="100"></el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template slot-scope="scope">
            <span>{{ formatDate(scope.row.created_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="150">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="handleDetail(scope.row)">详情</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'BacktestList',
  data() {
    return {
      loading: false,
      backtests: [],
      strategies: [],
      filterForm: {
        strategyId: ''
      },
      pagination: {
        page: 1,
        size: 10,
        total: 0
      }
    }
  },
  created() {
    this.fetchStrategies()
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      try {
        const strategyId = this.filterForm.strategyId
        const skip = (this.pagination.page - 1) * this.pagination.size
        const limit = this.pagination.size

        const response = await this.$http.get('/backtest/results', {
          params: {
            strategy_id: strategyId || undefined,
            skip,
            limit
          }
        })

        this.backtests = response.data
        this.pagination.total = response.headers['x-total-count'] || this.backtests.length
      } catch (error) {
        console.error('获取回测结果失败:', error)
        this.$message.error('获取回测结果失败')
      } finally {
        this.loading = false
      }
    },
    async fetchStrategies() {
      try {
        // 从策略管理中心获取真实策略列表
        const response = await this.$http.get('/api/v1/strategies')

        if (response.data && response.data.success) {
          // 处理策略数据，确保格式正确
          this.strategies = (response.data.data || []).map(strategy => ({
            id: strategy.id,
            name: strategy.name,
            description: strategy.description,
            type: strategy.type,
            category: strategy.category,
            status: strategy.status
          }))

          console.log('回测列表页面成功获取策略列表:', this.strategies.length, '个策略')
        } else {
          console.warn('策略API返回格式异常:', response.data)
          this.strategies = []
        }
      } catch (error) {
        console.error('获取策略列表失败:', error)
        this.strategies = []
      }
    },
    handleSearch() {
      this.pagination.page = 1
      this.fetchData()
    },
    resetFilter() {
      this.filterForm = {
        strategyId: ''
      }
      this.handleSearch()
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.fetchData()
    },
    handleCurrentChange(page) {
      this.pagination.page = page
      this.fetchData()
    },
    handleDetail(row) {
      this.$router.push(`/backtests/${row.id}`)
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该回测结果吗？此操作将无法恢复', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await this.$http.delete(`/backtest/results/${row.id}`)
        this.$message.success('删除成功')
        this.fetchData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除回测结果失败:', error)
          this.$message.error('删除回测结果失败')
        }
      }
    },
    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString()
    },
    formatProfit(profit) {
      if (profit === undefined || profit === null) return '-'
      return profit >= 0 ? `+${profit.toFixed(2)}%` : `${profit.toFixed(2)}%`
    },
    formatPercent(value) {
      if (value === undefined || value === null) return '-'
      return `${(value * 100).toFixed(2)}%`
    }
  }
}
</script>

<style scoped>
.backtest-list-container {
  padding: 20px;
  background-color: #081428;
  color: #FFFFFF;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

h2 {
  color: #00F7FF;
  text-shadow: 0 0 10px rgba(0,247,255,0.7);
  margin: 0;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  color: #00F7FF;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(0,247,255,0.7);
  font-size: 16px;
}

.table-container {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.batch-actions {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

/* 覆盖Element UI的一些样式 */
::v-deep .el-card {
  background-color: rgba(0, 21, 40, 0.8);
  border: 1px solid #00F7FF;
  box-shadow: 0 0 15px rgba(0,247,255,0.3);
  margin-bottom: 20px;
}

::v-deep .el-card__header {
  background-color: rgba(0, 15, 40, 0.9);
  border-bottom: 1px solid #00F7FF;
  padding: 15px 20px;
}

::v-deep .el-table {
  background-color: rgba(8, 20, 40, 0.9);
  color: #fff;
  border: 1px solid rgba(0, 247, 255, 0.3);
  box-shadow: 0 0 15px rgba(0,247,255,0.2);
}

::v-deep .el-table__body-wrapper,
::v-deep .el-table__header-wrapper {
  background-color: rgba(8, 20, 40, 0.9);
}

::v-deep .el-table tr {
  background-color: rgba(8, 20, 40, 0.9) !important;
}

::v-deep .el-table td {
  color: #FFFFFF;
  background-color: rgba(8, 20, 40, 0.9) !important;
  border-bottom: 1px solid rgba(0, 247, 255, 0.1);
}

::v-deep .el-table th {
  background-color: rgba(0, 21, 40, 0.8) !important;
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
  font-weight: bold;
  border-bottom: 1px solid rgba(0, 247, 255, 0.3);
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(0, 21, 40, 0.7) !important;
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: rgba(0, 247, 255, 0.1) !important;
}

::v-deep .el-table--border,
::v-deep .el-table--group {
  border: 1px solid rgba(0, 247, 255, 0.1);
}

::v-deep .el-table--border td,
::v-deep .el-table--border th {
  border-right: 1px solid rgba(0, 247, 255, 0.1);
}

::v-deep .el-pagination {
  color: #FFFFFF;
  padding: 15px 0;
}

::v-deep .el-pagination button {
  background-color: rgba(0, 21, 40, 0.7);
  color: #00F7FF;
}

::v-deep .el-pagination button:hover {
  color: #FFFFFF;
}

::v-deep .el-pagination button:disabled {
  background-color: rgba(0, 21, 40, 0.3);
  color: rgba(0, 247, 255, 0.4);
}

::v-deep .el-pagination__total {
  color: #00F7FF;
}

::v-deep .el-pagination .el-select .el-input .el-input__inner {
  background-color: rgba(0, 21, 40, 0.7);
  border-color: rgba(0, 247, 255, 0.3);
  color: #00F7FF;
}

::v-deep .el-pagination .el-select .el-input .el-input__inner:focus {
  border-color: #00F7FF;
}

::v-deep .el-pagination .el-select .el-input.is-focus .el-input__inner {
  border-color: #00F7FF;
}

::v-deep .el-pagination.is-background .btn-next,
::v-deep .el-pagination.is-background .btn-prev,
::v-deep .el-pagination.is-background .el-pager li {
  background-color: rgba(0, 21, 40, 0.7);
  color: #00F7FF;
}

::v-deep .el-pagination.is-background .el-pager li:hover {
  color: #FFFFFF;
}

::v-deep .el-pagination.is-background .el-pager li.active {
  background-color: rgba(0, 247, 255, 0.3);
  color: #FFFFFF;
  border: 1px solid #00F7FF;
  box-shadow: 0 0 15px rgba(0,247,255,0.5);
}

::v-deep .el-form-item__label {
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.4);
}

::v-deep .el-input__inner {
  background-color: rgba(0, 21, 40, 0.5);
  border: 1px solid rgba(0, 247, 255, 0.3);
  color: #FFFFFF;
}

::v-deep .el-input__inner:focus,
::v-deep .el-input__inner:hover {
  border-color: #00F7FF;
  box-shadow: 0 0 8px rgba(0,247,255,0.5);
}

::v-deep .el-select .el-input__inner:focus {
  border-color: #00F7FF;
}

::v-deep .el-select-dropdown {
  background-color: rgba(0, 21, 40, 0.9);
  border: 1px solid rgba(0, 247, 255, 0.3);
  box-shadow: 0 0 15px rgba(0,247,255,0.3);
}

::v-deep .el-select-dropdown__item {
  color: #FFFFFF;
}

::v-deep .el-select-dropdown__item.hover,
::v-deep .el-select-dropdown__item:hover {
  background-color: rgba(0, 247, 255, 0.2);
}

::v-deep .el-select-dropdown__item.selected {
  background-color: rgba(0, 247, 255, 0.3);
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
  font-weight: bold;
}

::v-deep .el-date-editor {
  background-color: rgba(0, 21, 40, 0.5);
  border: 1px solid rgba(0, 247, 255, 0.3);
}

::v-deep .el-date-editor .el-input__inner {
  background-color: transparent;
  border: none;
}

::v-deep .el-date-editor .el-input__icon {
  color: #00F7FF;
}

::v-deep .el-range-separator {
  color: #00F7FF;
}

::v-deep .el-date-editor.is-active,
::v-deep .el-date-editor:hover {
  border-color: #00F7FF;
  box-shadow: 0 0 8px rgba(0,247,255,0.5);
}

::v-deep .el-button {
  background-color: rgba(0, 21, 40, 0.7);
  border-color: #00F7FF;
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.8);
  transition: all 0.3s ease;
}

::v-deep .el-button:hover {
  background-color: rgba(0, 247, 255, 0.2);
  box-shadow: 0 0 15px rgba(0,247,255,0.5);
}

::v-deep .el-button--primary {
  background-color: rgba(0, 247, 255, 0.2);
  border-color: #00F7FF;
  color: #00F7FF;
}

::v-deep .el-button--primary:hover {
  background-color: rgba(0, 247, 255, 0.4);
  box-shadow: 0 0 20px rgba(0,247,255,0.7);
}

::v-deep .el-button--danger {
  background-color: rgba(255, 52, 119, 0.2);
  border-color: #FF3477;
  color: #FF3477;
}

::v-deep .el-button--danger:hover {
  background-color: rgba(255, 52, 119, 0.4);
  box-shadow: 0 0 20px rgba(255,52,119,0.7);
  color: #FFFFFF;
}

::v-deep .el-button--success {
  background-color: rgba(0, 255, 148, 0.2);
  border-color: #00FF94;
  color: #00FF94;
}

::v-deep .el-button--success:hover {
  background-color: rgba(0, 255, 148, 0.4);
  box-shadow: 0 0 20px rgba(0,255,148,0.7);
  color: #FFFFFF;
}

::v-deep .el-button--text {
  background: none;
  border: none;
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.5);
}

::v-deep .el-button--text:hover {
  color: #FFFFFF;
  text-shadow: 0 0 10px rgba(0,247,255,0.8);
  background: none;
  box-shadow: none;
}

::v-deep .el-tag--success {
  background-color: rgba(0, 255, 148, 0.2);
  border-color: #00FF94;
  color: #00FF94;
}

::v-deep .el-tag--warning {
  background-color: rgba(255, 170, 0, 0.2);
  border-color: #FFAA00;
  color: #FFAA00;
}

::v-deep .el-tag--danger {
  background-color: rgba(255, 52, 119, 0.2);
  border-color: #FF3477;
  color: #FF3477;
}

::v-deep .el-tag--info {
  background-color: rgba(119, 144, 193, 0.2);
  border-color: #7790C1;
  color: #7790C1;
}

.profit {
  color: #67C23A;
}

.loss {
  color: #F56C6C;
}

/* 表格表头样式优化 */
::v-deep .el-table__header {
  background-color: rgba(0, 21, 40, 0.9) !important;
}

::v-deep .el-table__header-wrapper th {
  background-color: rgba(0, 21, 40, 0.9) !important;
  border-bottom: 1px solid rgba(0, 247, 255, 0.3) !important;
  color: #00F7FF !important;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(0, 247, 255, 0.6);
  padding: 12px 0;
  font-size: 14px;
}

::v-deep .el-table__header-wrapper th .cell {
  line-height: 24px;
}

::v-deep .el-table__column-filter-trigger {
  color: rgba(0, 247, 255, 0.6);
}

::v-deep .el-table--border th {
  border-right: 1px solid rgba(0, 247, 255, 0.2) !important;
}

/* 修复操作列背景色问题 */
::v-deep .el-table__fixed-right {
  background-color: rgba(8, 20, 40, 0.9) !important;
}

::v-deep .el-table__fixed-right td {
  background-color: rgba(8, 20, 40, 0.9) !important;
}

::v-deep .el-table__fixed-right th {
  background-color: rgba(0, 21, 40, 0.9) !important;
}

::v-deep .el-table__fixed-right-patch {
  background-color: rgba(0, 21, 40, 0.9) !important;
}
</style>