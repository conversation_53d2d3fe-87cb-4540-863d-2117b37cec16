<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ECharts 测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div id="main" style="width: 600px;height:400px;"></div>
    <script type="text/javascript">
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('main'));

        // 模拟缓存数据
        var data = [
            { time: new Date('2025-05-26T00:04:32'), hitRate: 0.33, size: 2 },
            { time: new Date('2025-05-26T00:05:36'), hitRate: 0.33, size: 2 },
            { time: new Date('2025-05-26T00:06:40'), hitRate: 0.75, size: 2 }
        ];

        // 指定图表的配置项和数据
        var option = {
            title: {
                text: '缓存性能趋势',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    const time = new Date(params[0].value[0]).toLocaleString();
                    let result = `${time}<br/>`;

                    params.forEach(param => {
                        if (param.seriesName === '命中率') {
                            result += `${param.seriesName}: ${(param.value[1] * 100).toFixed(1)}%<br/>`;
                        } else {
                            result += `${param.seriesName}: ${param.value[1]}<br/>`;
                        }
                    });

                    return result;
                }
            },
            legend: {
                data: ['命中率', '缓存大小'],
                bottom: 0
            },
            xAxis: {
                type: 'time',
                splitLine: {
                    show: false
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '命中率',
                    min: 0,
                    max: 1,
                    axisLabel: {
                        formatter: function(value) {
                            return (value * 100) + '%';
                        }
                    },
                    splitLine: {
                        show: true
                    }
                },
                {
                    type: 'value',
                    name: '缓存大小',
                    splitLine: {
                        show: false
                    }
                }
            ],
            series: [
                {
                    name: '命中率',
                    type: 'line',
                    data: data.map(item => [item.time, item.hitRate]),
                    lineStyle: {
                        width: 2
                    },
                    yAxisIndex: 0
                },
                {
                    name: '缓存大小',
                    type: 'line',
                    data: data.map(item => [item.time, item.size]),
                    lineStyle: {
                        width: 2,
                        type: 'dashed'
                    },
                    yAxisIndex: 1
                }
            ]
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        
        console.log('ECharts 测试图表已创建');
    </script>
</body>
</html>
