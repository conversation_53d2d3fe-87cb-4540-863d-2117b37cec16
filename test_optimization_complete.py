#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化功能完整测试脚本
验证修复后的功能是否完全正常
"""

import requests
import json
import time
import sys

def test_optimization_complete():
    """完整测试策略优化功能"""
    print("=" * 60)
    print("策略优化功能完整测试")
    print("=" * 60)
    
    backend_url = "http://localhost:8000"
    frontend_url = "http://localhost:8080"
    
    # 测试结果统计
    results = {
        "backend_strategies": False,
        "backend_optimization": False,
        "backend_apply": False,
        "frontend_optimization": False,
        "frontend_apply": False,
        "data_changes": False,
        "parameter_persistence": False
    }
    
    strategy_id = None
    
    # 1. 测试后端策略列表
    print("\n1. 测试后端策略列表API...")
    try:
        response = requests.get(f"{backend_url}/api/v1/strategies", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and len(data['data']) > 0:
                strategy_id = data['data'][0]['id']
                print(f"✓ 后端策略列表正常，策略ID: {strategy_id}")
                results["backend_strategies"] = True
            else:
                print("✗ 策略列表为空")
        else:
            print(f"✗ 后端策略列表失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 后端策略列表异常: {e}")
    
    if not strategy_id:
        print("无法获取策略ID，测试终止")
        return results
    
    # 2. 测试后端优化数据API
    print(f"\n2. 测试后端优化数据API...")
    initial_data = None
    try:
        response = requests.get(f"{backend_url}/api/v1/strategies/{strategy_id}/optimization", timeout=10)
        if response.status_code == 200:
            initial_data = response.json()
            suggestions = initial_data.get('optimization_suggestions', [])
            print(f"✓ 后端优化数据正常，建议数量: {len(suggestions)}")
            results["backend_optimization"] = True
            
            # 显示当前数据
            current_perf = initial_data.get('current_performance', {})
            print(f"  当前性能: 收益率{current_perf.get('annualized_return')}%, 夏普{current_perf.get('sharpe_ratio')}, 回撤{current_perf.get('max_drawdown')}%")
        else:
            print(f"✗ 后端优化数据失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 后端优化数据异常: {e}")
    
    # 3. 测试后端应用优化
    if initial_data and initial_data.get('optimization_suggestions'):
        print(f"\n3. 测试后端应用优化...")
        first_suggestion = initial_data['optimization_suggestions'][0]
        try:
            apply_data = {
                'parameter': first_suggestion.get('parameter'),
                'value': first_suggestion.get('suggested_value')
            }
            response = requests.post(f"{backend_url}/api/v1/strategies/{strategy_id}/optimize", 
                                   json=apply_data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✓ 后端应用优化成功: {apply_data['parameter']} = {apply_data['value']}")
                    results["backend_apply"] = True
                else:
                    print(f"✗ 后端应用优化失败: {result.get('message')}")
            else:
                print(f"✗ 后端应用优化请求失败: {response.status_code}")
        except Exception as e:
            print(f"✗ 后端应用优化异常: {e}")
    
    # 4. 测试前端优化数据API
    print(f"\n4. 测试前端优化数据API...")
    try:
        response = requests.get(f"{frontend_url}/api/v1/strategies/{strategy_id}/optimization", timeout=10)
        if response.status_code == 200:
            data = response.json()
            suggestions = data.get('optimization_suggestions', [])
            print(f"✓ 前端优化数据正常，建议数量: {len(suggestions)}")
            results["frontend_optimization"] = True
        else:
            print(f"✗ 前端优化数据失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 前端优化数据异常: {e}")
    
    # 5. 测试前端应用优化
    print(f"\n5. 测试前端应用优化...")
    try:
        apply_data = {
            'parameter': 'position_size',
            'value': 0.18
        }
        response = requests.post(f"{frontend_url}/api/v1/strategies/{strategy_id}/optimize", 
                               json=apply_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✓ 前端应用优化成功: {apply_data['parameter']} = {apply_data['value']}")
                results["frontend_apply"] = True
            else:
                print(f"✗ 前端应用优化失败: {result.get('message')}")
        else:
            print(f"✗ 前端应用优化请求失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 前端应用优化异常: {e}")
    
    # 6. 测试数据变化
    print(f"\n6. 测试数据变化...")
    time.sleep(2)  # 等待数据更新
    try:
        response = requests.get(f"{backend_url}/api/v1/strategies/{strategy_id}/optimization", timeout=10)
        if response.status_code == 200:
            new_data = response.json()
            
            # 比较数据是否变化
            if initial_data:
                initial_perf = initial_data.get('current_performance', {})
                new_perf = new_data.get('current_performance', {})
                
                data_changed = False
                for key in ['annualized_return', 'sharpe_ratio', 'max_drawdown']:
                    if initial_perf.get(key) != new_perf.get(key):
                        data_changed = True
                        break
                
                if data_changed:
                    print("✓ 数据发生变化，优化功能正常")
                    results["data_changes"] = True
                else:
                    print("✗ 数据没有变化")
            else:
                print("✗ 无法比较数据变化")
        else:
            print(f"✗ 获取新数据失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 数据变化测试异常: {e}")
    
    # 7. 测试参数持久化
    print(f"\n7. 测试参数持久化...")
    try:
        # 应用一个特定参数
        test_param = {
            'parameter': 'moving_average_period',
            'value': 42  # 特殊值用于验证
        }
        response = requests.post(f"{backend_url}/api/v1/strategies/{strategy_id}/optimize", 
                               json=test_param, timeout=10)
        if response.status_code == 200:
            time.sleep(1)
            
            # 再次获取优化数据，检查是否反映了新参数
            check_response = requests.get(f"{backend_url}/api/v1/strategies/{strategy_id}/optimization", timeout=10)
            if check_response.status_code == 200:
                check_data = check_response.json()
                suggestions = check_data.get('optimization_suggestions', [])
                
                # 查找moving_average_period建议，检查current_value是否为42
                found_param = False
                for suggestion in suggestions:
                    if suggestion.get('parameter') == 'moving_average_period':
                        if suggestion.get('current_value') == 42:
                            found_param = True
                            break
                
                if found_param:
                    print("✓ 参数持久化正常，优化建议反映了新参数")
                    results["parameter_persistence"] = True
                else:
                    print("✗ 参数持久化失败，优化建议未反映新参数")
            else:
                print("✗ 参数持久化验证失败")
        else:
            print(f"✗ 参数持久化测试失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 参数持久化测试异常: {e}")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:25} : {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！策略优化功能完全正常！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = test_optimization_complete()
    sys.exit(0 if success else 1)
