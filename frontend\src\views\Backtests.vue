<template>
  <div class="backtests-container">
    <div class="page-header">
      <h2>回测管理</h2>
      <el-button type="primary" @click="createBacktest">
        <i class="el-icon-plus"></i> 创建回测
      </el-button>
    </div>

    <div v-loading="loading">
      <!-- 过滤和搜索区域 -->
      <el-card class="filter-card">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="策略名称">
            <el-select v-model="filterForm.strategy_id" placeholder="全部策略" clearable style="width: 180px">
              <el-option
                v-for="strategy in strategies"
                :key="strategy.id"
                :label="strategy.name"
                :value="strategy.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="回测状态">
            <el-select v-model="filterForm.status" placeholder="全部状态" clearable style="width: 140px">
              <el-option label="已完成" value="completed"></el-option>
              <el-option label="运行中" value="running"></el-option>
              <el-option label="失败" value="failed"></el-option>
              <el-option label="待处理" value="pending"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 280px">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchBacktests">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 回测列表 -->
      <el-card class="backtests-list-card mt-20">
        <div slot="header" class="card-header">
          <span>回测列表</span>
          <el-button type="text" @click="refreshList">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>

        <el-table
          :data="backtests"
          stripe
          style="width: 100%"
          v-if="backtests.length > 0"
          border>
          <el-table-column prop="name" label="回测名称" min-width="200">
            <template slot-scope="scope">
              <router-link :to="`/backtests/${scope.row.id}`" class="backtest-name-link">
                {{ scope.row.name }}
              </router-link>
            </template>
          </el-table-column>
          <el-table-column prop="strategy_name" label="策略名称" min-width="180"></el-table-column>
          <el-table-column prop="created_at" label="创建时间" min-width="180" sortable></el-table-column>
          <el-table-column prop="date_range" label="回测周期" min-width="180"></el-table-column>
          <el-table-column prop="status" label="状态" width="120">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status === 'completed'">已完成</el-tag>
              <el-tag type="warning" v-else-if="scope.row.status === 'running'">运行中</el-tag>
              <el-tag type="danger" v-else-if="scope.row.status === 'failed'">失败</el-tag>
              <el-tag type="info" v-else>待处理</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="total_return" label="总收益" width="120">
            <template slot-scope="scope">
              <span :class="{ 'positive': scope.row.total_return > 0, 'negative': scope.row.total_return < 0 }">
                {{ formatPercent(scope.row.total_return) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="viewBacktestResult(scope.row.id)"
                :disabled="scope.row.status !== 'completed'">
                查看结果
              </el-button>
              <el-button
                size="mini"
                type="danger"
                @click="deleteBacktest(scope.row.id)"
                :disabled="scope.row.status === 'running'">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-empty v-else description="暂无回测数据"></el-empty>

        <!-- 分页 -->
        <div class="pagination-container" v-if="backtests.length > 0">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total">
          </el-pagination>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { backtestAPI, strategyAPI } from '@/api/index';

export default {
  name: 'Backtests',
  data() {
    return {
      loading: false,
      backtests: [],
      strategies: [],
      filterForm: {
        strategy_id: '',
        status: '',
        start_date: '',
        end_date: ''
      },
      dateRange: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  created() {
    this.fetchBacktests();
    this.fetchStrategies();
  },
  methods: {
    async fetchBacktests() {
      this.loading = true;
      try {
        // 使用API获取回测列表
        const response = await backtestAPI.getBacktests();
        if (response.data && response.data.success) {
          this.backtests = response.data.data;
          this.pagination.total = response.data.meta ? response.data.meta.total : this.backtests.length;
        } else {
          // 如果响应格式不符合预期，使用模拟数据
          console.warn('回测API返回格式不符合预期，使用模拟数据');
          this.useMockData();
        }
      } catch (error) {
        console.error('获取回测列表失败:', error);
        this.$message.error('获取回测列表失败: ' + (error.message || '未知错误'));
        // 出错时使用模拟数据
        this.useMockData();
      } finally {
        this.loading = false;
      }
    },

    // 使用模拟数据（备用方案）
    useMockData() {
      setTimeout(() => {
        this.backtests = [
          {
            id: '1',
            name: '双均线策略2023年全年回测',
            strategy_id: '1',
            strategy_name: '双均线交叉策略',
            created_at: '2023-09-18 14:30:00',
            date_range: '2023-01-01 至 2023-09-01',
            status: 'completed',
            total_return: 0.253
          },
          {
            id: '2',
            name: 'MACD策略Q2回测',
            strategy_id: '2',
            strategy_name: 'MACD策略',
            created_at: '2023-09-19 10:15:20',
            date_range: '2023-04-01 至 2023-06-30',
            status: 'completed',
            total_return: -0.056
          },
          {
            id: '3',
            name: '布林带策略BTC回测',
            strategy_id: '3',
            strategy_name: '布林带策略',
            created_at: '2023-09-20 09:22:15',
            date_range: '2023-01-01 至 2023-08-31',
            status: 'running',
            total_return: 0
          },
          {
            id: '4',
            name: 'RSI超买超卖策略回测',
            strategy_id: '4',
            strategy_name: 'RSI超买超卖策略',
            created_at: '2023-09-20 16:45:30',
            date_range: '2022-01-01 至 2022-12-31',
            status: 'failed',
            total_return: 0
          }
        ];
        this.pagination.total = this.backtests.length;
      }, 500);
    },
    async fetchStrategies() {
      try {
        // 从策略管理中心获取真实策略列表
        const response = await this.$http.get('/api/v1/strategies');

        if (response.data && response.data.success) {
          // 处理策略数据，确保格式正确
          this.strategies = (response.data.data || []).map(strategy => ({
            id: strategy.id,
            name: strategy.name,
            description: strategy.description,
            type: strategy.type,
            category: strategy.category,
            status: strategy.status
          }));

          console.log('回测页面成功获取策略列表:', this.strategies.length, '个策略');
        } else {
          console.warn('策略API返回格式异常:', response.data);
          this.strategies = [];
        }
      } catch (error) {
        console.error('获取策略列表失败:', error);
        this.strategies = [];
      }
    },
    createBacktest() {
      this.$router.push('/backtests/create');
    },
    viewBacktestResult(id) {
      this.$router.push(`/backtests/${id}`);
    },
    async deleteBacktest(id) {
      try {
        await this.$confirm('此操作将永久删除该回测记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        // 实际项目中应该调用API删除回测
        // await backtestAPI.deleteBacktest(id);

        // 模拟删除成功
        this.$message.success('删除成功');
        this.backtests = this.backtests.filter(item => item.id !== id);
        this.pagination.total = this.backtests.length;
      } catch (error) {
        if (error === 'cancel') return;
        console.error('删除回测失败:', error);
        this.$message.error('删除回测失败: ' + (error.message || '未知错误'));
      }
    },
    searchBacktests() {
      // 处理日期范围
      if (this.dateRange && this.dateRange.length === 2) {
        this.filterForm.start_date = this.dateRange[0];
        this.filterForm.end_date = this.dateRange[1];
      } else {
        this.filterForm.start_date = '';
        this.filterForm.end_date = '';
      }

      // 重置分页
      this.pagination.currentPage = 1;

      // 实际项目中应该调用API进行搜索
      this.$message.info('搜索条件已应用');

      // 模拟搜索结果
      this.fetchBacktests();
    },
    resetFilter() {
      this.filterForm = {
        strategy_id: '',
        status: '',
        start_date: '',
        end_date: ''
      };
      this.dateRange = [];
      this.searchBacktests();
    },
    refreshList() {
      this.fetchBacktests();
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchBacktests();
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.fetchBacktests();
    },
    formatPercent(value) {
      return (value * 100).toFixed(2) + '%';
    }
  }
};
</script>

<style scoped>
.backtests-container {
  padding: 20px;
  background-color: #081428;
  color: #FFFFFF;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

h2 {
  color: #00F7FF;
  text-shadow: 0 0 10px rgba(0,247,255,0.7);
  margin: 0;
}

.mt-20 {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.positive {
  color: #00FF94;
  text-shadow: 0 0 8px rgba(0,255,148,0.7);
  font-weight: bold;
}

.negative {
  color: #FF3477;
  text-shadow: 0 0 8px rgba(255,52,119,0.7);
  font-weight: bold;
}

.backtest-name-link {
  color: #00F7FF;
  text-decoration: none;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
}

.backtest-name-link:hover {
  text-decoration: underline;
  text-shadow: 0 0 10px rgba(0,247,255,0.8);
}

/* 覆盖Element UI的卡片样式 */
::v-deep .el-card {
  background-color: rgba(8, 20, 40, 0.8);
  border: 1px solid #00F7FF;
  box-shadow: 0 0 15px rgba(0,247,255,0.3);
  margin-bottom: 20px;
}

::v-deep .el-card__header {
  background-color: rgba(0, 15, 40, 0.9);
  border-bottom: 1px solid #00F7FF;
  padding: 15px;
}

::v-deep .el-card__header span {
  color: #00F7FF;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(0,247,255,0.7);
}

/* 覆盖Element UI的一些样式 */
::v-deep .el-input__inner,
::v-deep .el-textarea__inner {
  background-color: rgba(0, 21, 40, 0.5);
  border-color: rgba(0, 247, 255, 0.3);
  color: #fff;
}

::v-deep .el-input__inner:focus,
::v-deep .el-textarea__inner:focus {
  border-color: #00F7FF;
  box-shadow: 0 0 5px rgba(0, 247, 255, 0.5);
}

::v-deep .el-select-dropdown {
  background-color: rgba(0, 10, 30, 0.95);
  border: 1px solid #00F7FF;
  box-shadow: 0 0 20px rgba(0,247,255,0.5);
}

::v-deep .el-select-dropdown__item {
  color: #00F7FF;
  background-color: rgba(0, 10, 30, 0.9);
  text-shadow: 0 0 8px rgba(0,247,255,0.8);
}

::v-deep .el-select-dropdown__item.hover,
::v-deep .el-select-dropdown__item:hover {
  background-color: rgba(0, 247, 255, 0.2);
  color: #FFFFFF;
}

::v-deep .el-select-dropdown__item.selected {
  background-color: rgba(0, 247, 255, 0.3);
  color: #FFFFFF;
}

::v-deep .el-form-item__label {
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
}

::v-deep .el-date-editor {
  background-color: rgba(0, 21, 40, 0.5);
}

::v-deep .el-date-editor .el-range-separator,
::v-deep .el-date-editor .el-range-input {
  color: #00F7FF;
  background: transparent;
}

::v-deep .el-date-picker {
  background-color: rgba(0, 10, 30, 0.95);
  border: 1px solid #00F7FF;
  box-shadow: 0 0 20px rgba(0,247,255,0.5);
}

::v-deep .el-date-table th,
::v-deep .el-date-table td.available span,
::v-deep .el-date-picker__header-label {
  color: #00F7FF;
}

::v-deep .el-date-table td.current:not(.disabled) span {
  background-color: #00F7FF;
  color: #081428;
}

::v-deep .el-table {
  background-color: rgba(8, 20, 40, 0.9);
  color: #fff;
  border: 1px solid rgba(0, 247, 255, 0.3);
  box-shadow: 0 0 15px rgba(0,247,255,0.2);
}

::v-deep .el-table__body-wrapper,
::v-deep .el-table__header-wrapper {
  background-color: rgba(8, 20, 40, 0.9);
}

::v-deep .el-table tr {
  background-color: rgba(8, 20, 40, 0.9) !important;
}

::v-deep .el-table td {
  color: #FFFFFF;
  background-color: rgba(8, 20, 40, 0.9) !important;
  border-bottom: 1px solid rgba(0, 247, 255, 0.1);
}

::v-deep .el-table th {
  background-color: rgba(0, 21, 40, 0.8);
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
  font-weight: bold;
  border-bottom: 1px solid rgba(0, 247, 255, 0.3);
}

::v-deep .el-table__header {
  background-color: rgba(0, 21, 40, 0.9) !important;
}

::v-deep .el-table__header-wrapper th {
  background-color: rgba(0, 21, 40, 0.9) !important;
  border-bottom: 1px solid rgba(0, 247, 255, 0.3) !important;
  color: #00F7FF !important;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(0, 247, 255, 0.6);
  padding: 12px 0;
  font-size: 14px;
}

::v-deep .el-table__header-wrapper th .cell {
  line-height: 24px;
}

::v-deep .el-table__column-filter-trigger {
  color: rgba(0, 247, 255, 0.6);
}

::v-deep .el-table--border th {
  border-right: 1px solid rgba(0, 247, 255, 0.2) !important;
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(0, 21, 40, 0.7) !important;
}

::v-deep .el-table--border,
::v-deep .el-table--group {
  border: 1px solid rgba(0, 247, 255, 0.1);
}

::v-deep .el-table--border td,
::v-deep .el-table--border th {
  border-right: 1px solid rgba(0, 247, 255, 0.1);
}

/* 表格背景色应用到整个表格容器 */
::v-deep .el-table::before {
  background-color: transparent;
}

/* 确保表格内部所有元素都有正确的背景色 */
::v-deep .el-table__body {
  background-color: rgba(8, 20, 40, 0.9);
}

/* 确保表单背景色深色 */
::v-deep .filter-card {
  background-color: rgba(8, 20, 40, 0.9) !important;
}

/* 整个过滤区域背景加深 */
.filter-form {
  background-color: rgba(8, 20, 40, 0.9);
}

::v-deep .el-pagination {
  color: #00F7FF;
}

::v-deep .el-pagination button {
  background-color: rgba(0, 21, 40, 0.5);
  color: #00F7FF;
  border: 1px solid rgba(0, 247, 255, 0.3);
}

::v-deep .el-pagination .el-select .el-input {
  width: 100px;
}

::v-deep .el-pagination .el-select .el-input input {
  background-color: rgba(0, 21, 40, 0.5);
  color: #00F7FF;
}

::v-deep .el-pagination .el-pager li {
  background-color: rgba(0, 21, 40, 0.5);
  color: #00F7FF;
  border: 1px solid rgba(0, 247, 255, 0.3);
}

::v-deep .el-pagination .el-pager li.active {
  background-color: rgba(0, 247, 255, 0.3);
  color: #FFF;
  text-shadow: 0 0 8px rgba(0,247,255,0.8);
  border: 1px solid #00F7FF;
  box-shadow: 0 0 10px rgba(0,247,255,0.5);
}

::v-deep .el-empty__description {
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
}

/* 按钮样式 */
::v-deep .el-button {
  background-color: rgba(0, 21, 40, 0.7);
  border-color: #00F7FF;
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.8);
  transition: all 0.3s ease;
}

::v-deep .el-button:hover {
  background-color: rgba(0, 247, 255, 0.2);
  box-shadow: 0 0 15px rgba(0,247,255,0.5);
}

::v-deep .el-button--primary {
  background-color: rgba(0, 247, 255, 0.2);
  border-color: #00F7FF;
  color: #00F7FF;
}

::v-deep .el-button--primary:hover {
  background-color: rgba(0, 247, 255, 0.4);
  box-shadow: 0 0 20px rgba(0,247,255,0.7);
}

::v-deep .el-button--danger {
  background-color: rgba(255, 52, 119, 0.2);
  border-color: #FF3477;
  color: #FF3477;
}

::v-deep .el-button--danger:hover {
  background-color: rgba(255, 52, 119, 0.4);
  box-shadow: 0 0 15px rgba(255,52,119,0.5);
}

::v-deep .el-tag--success {
  background-color: rgba(0, 255, 148, 0.2);
  border-color: #00FF94;
  color: #00FF94;
}

::v-deep .el-tag--warning {
  background-color: rgba(255, 170, 0, 0.2);
  border-color: #FFAA00;
  color: #FFAA00;
}

::v-deep .el-tag--danger {
  background-color: rgba(255, 52, 119, 0.2);
  border-color: #FF3477;
  color: #FF3477;
}

::v-deep .el-tag--info {
  background-color: rgba(119, 144, 193, 0.2);
  border-color: #7790C1;
  color: #7790C1;
}

/* 表格行hover效果 */
::v-deep .el-table__body tr.hover-row > td,
::v-deep .el-table__body tr:hover > td {
  background-color: rgba(0, 247, 255, 0.1) !important;
}

/* 改善搜索和过滤区域 */
::v-deep .filter-card .el-form-item__label {
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
  font-weight: bold;
}

/* 搜索区域的下拉选择样式 */
::v-deep .el-form-item {
  margin-bottom: 15px;
  margin-right: 15px;
}

/* 表格操作区域按钮 */
::v-deep .el-button--mini.is-disabled,
::v-deep .el-button--mini.is-disabled:hover {
  opacity: 0.6;
  background-color: rgba(0, 21, 40, 0.3);
}

/* 按钮样式优化 */
::v-deep .el-button--text {
  color: #00F7FF;
  text-shadow: 0 0 8px rgba(0,247,255,0.7);
  background: transparent;
  border: none;
}

::v-deep .el-button--text:hover {
  color: #FFFFFF;
  text-shadow: 0 0 10px rgba(0,247,255,0.9);
  background: transparent;
  box-shadow: none;
}

/* 分页样式优化 */
::v-deep .el-pagination__total {
  color: #00F7FF;
}

::v-deep .el-pagination__jump {
  color: #00F7FF;
}

/* 修复操作列背景色问题 */
::v-deep .el-table__fixed-right {
  background-color: rgba(8, 20, 40, 0.9) !important;
}

::v-deep .el-table__fixed-right td {
  background-color: rgba(8, 20, 40, 0.9) !important;
}

::v-deep .el-table__fixed-right th {
  background-color: rgba(0, 21, 40, 0.9) !important;
}

::v-deep .el-table__fixed-right-patch {
  background-color: rgba(0, 21, 40, 0.9) !important;
}
</style>