"""
参数验证器
负责验证策略参数的有效性
"""

from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

class ValidationResult:
    """验证结果类"""
    
    def __init__(self, is_valid: bool = True, errors: List[str] = None, warnings: List[str] = None):
        self.is_valid = is_valid
        self.errors = errors or []
        self.warnings = warnings or []

class ParameterValidator:
    """参数验证器"""
    
    def validate_parameters(
        self, 
        parameters: Dict[str, Any], 
        schema: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """验证参数"""
        errors = []
        warnings = []
        
        if not schema:
            return ValidationResult(True, [], ["未提供参数模式，跳过验证"])
        
        try:
            # 获取参数定义
            param_definitions = schema.get('parameters', [])
            validation_rules = schema.get('validation_rules', [])
            
            # 验证必需参数
            for param_def in param_definitions:
                param_name = param_def.get('name')
                is_required = param_def.get('required', False)
                
                if is_required and param_name not in parameters:
                    errors.append(f"缺少必需参数: {param_name}")
                    continue
                
                if param_name in parameters:
                    # 验证参数类型和值
                    param_errors = self._validate_parameter(
                        param_name, parameters[param_name], param_def
                    )
                    errors.extend(param_errors)
            
            # 验证自定义规则
            for rule in validation_rules:
                rule_errors = self._validate_rule(parameters, rule)
                errors.extend(rule_errors)
            
            # 检查未定义的参数
            defined_params = {p.get('name') for p in param_definitions}
            for param_name in parameters:
                if param_name not in defined_params:
                    warnings.append(f"未定义的参数: {param_name}")
            
            is_valid = len(errors) == 0
            return ValidationResult(is_valid, errors, warnings)
            
        except Exception as e:
            logger.error(f"参数验证失败: {str(e)}")
            return ValidationResult(False, [f"验证过程出错: {str(e)}"])
    
    def _validate_parameter(self, name: str, value: Any, definition: Dict[str, Any]) -> List[str]:
        """验证单个参数"""
        errors = []
        
        param_type = definition.get('type')
        min_value = definition.get('min')
        max_value = definition.get('max')
        options = definition.get('options')
        
        # 类型验证
        if param_type == 'integer':
            if not isinstance(value, int):
                try:
                    value = int(value)
                except (ValueError, TypeError):
                    errors.append(f"参数 {name} 必须是整数")
                    return errors
        
        elif param_type == 'float':
            if not isinstance(value, (int, float)):
                try:
                    value = float(value)
                except (ValueError, TypeError):
                    errors.append(f"参数 {name} 必须是数字")
                    return errors
        
        elif param_type == 'string':
            if not isinstance(value, str):
                errors.append(f"参数 {name} 必须是字符串")
                return errors
        
        elif param_type == 'boolean':
            if not isinstance(value, bool):
                errors.append(f"参数 {name} 必须是布尔值")
                return errors
        
        elif param_type == 'enum':
            if options and value not in [opt.get('value') for opt in options]:
                valid_options = [opt.get('value') for opt in options]
                errors.append(f"参数 {name} 的值必须是以下之一: {valid_options}")
                return errors
        
        # 范围验证
        if param_type in ['integer', 'float'] and isinstance(value, (int, float)):
            if min_value is not None and value < min_value:
                errors.append(f"参数 {name} 的值不能小于 {min_value}")
            
            if max_value is not None and value > max_value:
                errors.append(f"参数 {name} 的值不能大于 {max_value}")
        
        # 字符串长度验证
        if param_type == 'string' and isinstance(value, str):
            min_length = definition.get('min_length')
            max_length = definition.get('max_length')
            
            if min_length is not None and len(value) < min_length:
                errors.append(f"参数 {name} 的长度不能少于 {min_length} 个字符")
            
            if max_length is not None and len(value) > max_length:
                errors.append(f"参数 {name} 的长度不能超过 {max_length} 个字符")
        
        return errors
    
    def _validate_rule(self, parameters: Dict[str, Any], rule: Dict[str, Any]) -> List[str]:
        """验证自定义规则"""
        errors = []
        
        rule_expression = rule.get('rule')
        rule_message = rule.get('message', '自定义规则验证失败')
        
        if not rule_expression:
            return errors
        
        try:
            # 简单的规则验证（实际项目中可能需要更复杂的表达式解析）
            if self._evaluate_rule_expression(rule_expression, parameters):
                pass  # 规则通过
            else:
                errors.append(rule_message)
        
        except Exception as e:
            logger.warning(f"规则验证失败: {rule_expression}, 错误: {str(e)}")
            errors.append(f"规则验证出错: {rule_message}")
        
        return errors
    
    def _evaluate_rule_expression(self, expression: str, parameters: Dict[str, Any]) -> bool:
        """评估规则表达式"""
        # 简化的表达式评估器
        # 实际项目中应该使用更安全的表达式解析器
        
        # 替换参数名为实际值
        eval_expression = expression
        for param_name, param_value in parameters.items():
            eval_expression = eval_expression.replace(param_name, str(param_value))
        
        try:
            # 注意：在生产环境中不应该使用eval，这里仅作演示
            # 应该使用专门的表达式解析库如 simpleeval
            return eval(eval_expression)
        except:
            return False

class ParameterSchemaGenerator:
    """参数模式生成器"""
    
    @staticmethod
    def generate_dual_ma_schema() -> Dict[str, Any]:
        """生成双均线策略参数模式"""
        return {
            "parameters": [
                {
                    "name": "short_period",
                    "type": "integer",
                    "default": 5,
                    "min": 1,
                    "max": 50,
                    "description": "短期均线周期",
                    "required": True
                },
                {
                    "name": "long_period",
                    "type": "integer",
                    "default": 20,
                    "min": 2,
                    "max": 200,
                    "description": "长期均线周期",
                    "required": True
                },
                {
                    "name": "price_type",
                    "type": "enum",
                    "default": "close",
                    "options": [
                        {"label": "开盘价", "value": "open"},
                        {"label": "最高价", "value": "high"},
                        {"label": "最低价", "value": "low"},
                        {"label": "收盘价", "value": "close"}
                    ],
                    "description": "计算均线的价格类型",
                    "required": True
                },
                {
                    "name": "use_stop_loss",
                    "type": "boolean",
                    "default": True,
                    "description": "是否启用止损",
                    "required": False
                },
                {
                    "name": "stop_loss_rate",
                    "type": "float",
                    "default": 0.02,
                    "min": 0.001,
                    "max": 0.1,
                    "description": "止损比例",
                    "required": False
                }
            ],
            "validation_rules": [
                {
                    "rule": "long_period > short_period",
                    "message": "长期均线周期必须大于短期均线周期"
                }
            ]
        }
    
    @staticmethod
    def generate_rsi_schema() -> Dict[str, Any]:
        """生成RSI策略参数模式"""
        return {
            "parameters": [
                {
                    "name": "rsi_period",
                    "type": "integer",
                    "default": 14,
                    "min": 2,
                    "max": 50,
                    "description": "RSI计算周期",
                    "required": True
                },
                {
                    "name": "overbought_level",
                    "type": "float",
                    "default": 70.0,
                    "min": 50.0,
                    "max": 90.0,
                    "description": "超买水平",
                    "required": True
                },
                {
                    "name": "oversold_level",
                    "type": "float",
                    "default": 30.0,
                    "min": 10.0,
                    "max": 50.0,
                    "description": "超卖水平",
                    "required": True
                }
            ],
            "validation_rules": [
                {
                    "rule": "overbought_level > oversold_level",
                    "message": "超买水平必须大于超卖水平"
                }
            ]
        }
