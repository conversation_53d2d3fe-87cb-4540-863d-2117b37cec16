#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易API测试脚本
用于测试交易API服务的各个端点
"""

import requests
import json
import time
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8003"
API_PREFIX = "/api/v1"

def test_health():
    """测试健康检查端点"""
    url = f"{BASE_URL}/health"
    response = requests.get(url)
    print(f"健康检查: {response.status_code}")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()

def test_get_orders():
    """测试获取订单列表"""
    url = f"{BASE_URL}{API_PREFIX}/trading/orders"
    response = requests.get(url)
    print(f"获取订单列表: {response.status_code}")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()

def test_get_order(order_id):
    """测试获取订单详情"""
    url = f"{BASE_URL}{API_PREFIX}/trading/orders/{order_id}"
    response = requests.get(url)
    print(f"获取订单详情: {response.status_code}")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()

def test_create_order():
    """测试创建订单"""
    url = f"{BASE_URL}{API_PREFIX}/trading/orders"
    data = {
        "symbol": "BTC/USDT",
        "side": "buy",
        "type": "limit",
        "amount": 0.001,
        "price": 50000
    }
    response = requests.post(url, json=data)
    print(f"创建订单: {response.status_code}")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()
    
    # 返回订单ID，用于后续测试
    if response.status_code == 200 and response.json().get("status") == "success":
        return response.json().get("order", {}).get("id")
    return None

def test_cancel_order(order_id):
    """测试取消订单"""
    url = f"{BASE_URL}{API_PREFIX}/trading/orders/{order_id}/cancel"
    response = requests.post(url)
    print(f"取消订单: {response.status_code}")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()

def test_get_executions():
    """测试获取交易执行记录"""
    url = f"{BASE_URL}{API_PREFIX}/trading/executions"
    response = requests.get(url)
    print(f"获取交易执行记录: {response.status_code}")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()

def test_get_trading_status():
    """测试获取交易系统状态"""
    url = f"{BASE_URL}{API_PREFIX}/trading/status"
    response = requests.get(url)
    print(f"获取交易系统状态: {response.status_code}")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()

def run_all_tests():
    """运行所有测试"""
    print("开始测试交易API服务...")
    print(f"时间: {datetime.now().isoformat()}")
    print(f"基础URL: {BASE_URL}")
    print("=" * 50)
    
    # 测试健康检查
    test_health()
    
    # 测试获取订单列表
    test_get_orders()
    
    # 测试获取交易执行记录
    test_get_executions()
    
    # 测试获取交易系统状态
    test_get_trading_status()
    
    # 测试创建订单
    order_id = test_create_order()
    
    if order_id:
        # 测试获取订单详情
        test_get_order(order_id)
        
        # 等待一秒，确保订单已创建
        time.sleep(1)
        
        # 测试取消订单
        test_cancel_order(order_id)
        
        # 等待一秒，确保订单已取消
        time.sleep(1)
        
        # 再次获取订单详情，确认状态已更新
        test_get_order(order_id)
    
    print("=" * 50)
    print("测试完成!")

if __name__ == "__main__":
    run_all_tests()
