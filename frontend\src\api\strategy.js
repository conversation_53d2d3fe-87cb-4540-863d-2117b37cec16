/**
 * 策略管理API模块
 * 统一的策略管理接口，支持多种策略类型和TradingView代码格式
 */

import request from '@/utils/request'

// 策略管理API
export const strategyApi = {
  // 获取策略列表
  getStrategies(params = {}) {
    return request({
      url: '/api/v1/strategies',
      method: 'get',
      params
    })
  },

  // 获取策略详情
  getStrategy(id) {
    return request({
      url: `/api/v1/strategies/${id}`,
      method: 'get'
    })
  },

  // 创建策略
  createStrategy(data) {
    return request({
      url: '/api/v1/strategies',
      method: 'post',
      data
    })
  },

  // 更新策略
  updateStrategy(id, data) {
    return request({
      url: `/api/v1/strategies/${id}`,
      method: 'put',
      data
    })
  },

  // 删除策略
  deleteStrategy(id) {
    return request({
      url: `/api/v1/strategies/${id}`,
      method: 'delete'
    })
  },

  // 获取策略统计
  getStrategyStats() {
    return request({
      url: '/api/v1/strategies/stats',
      method: 'get'
    })
  },

  // 获取策略参数历史
  getParameterHistory(id) {
    return request({
      url: `/api/v1/strategies/${id}/parameter-history`,
      method: 'get'
    })
  }
}

// 策略模板API
export const templateApi = {
  // 获取策略模板列表
  getTemplates(params = {}) {
    return request({
      url: '/api/v1/strategy-templates',
      method: 'get',
      params
    })
  },

  // 从模板生成策略代码
  generateCode(templateId, parameters) {
    return request({
      url: `/api/v1/strategy-templates/${templateId}/generate`,
      method: 'post',
      data: { parameters }
    })
  }
}

// 代码验证和转换API
export const codeApi = {
  // 验证策略代码
  validateCode(data) {
    return request({
      url: '/api/v1/strategies/validate-code',
      method: 'post',
      data
    })
  },

  // 转换Pine Script代码
  convertPineScript(pineCode) {
    return request({
      url: '/api/v1/strategies/convert-pinescript',
      method: 'post',
      data: { pine_code: pineCode }
    })
  }
}

// 策略类型和分类API
export const typeApi = {
  // 获取策略类型列表
  getStrategyTypes() {
    return request({
      url: '/api/v1/strategy-types',
      method: 'get'
    })
  }
}

// 统一导出
export default {
  // 策略CRUD操作
  ...strategyApi,
  
  // 模板相关
  getTemplates: templateApi.getTemplates,
  generateCode: templateApi.generateCode,
  
  // 代码验证
  validateCode: codeApi.validateCode,
  convertPineScript: codeApi.convertPineScript,
  
  // 类型信息
  getStrategyTypes: typeApi.getStrategyTypes,

  // 扩展方法
  
  /**
   * 获取策略类型的中文标签
   */
  getTypeLabel(type) {
    const typeMap = {
      'trend_following': '趋势跟踪',
      'mean_reversion': '均值回归',
      'momentum': '动量策略',
      'arbitrage': '套利策略',
      'grid': '网格策略',
      'custom': '自定义策略'
    }
    return typeMap[type] || type
  },

  /**
   * 获取策略分类的中文标签
   */
  getCategoryLabel(category) {
    const categoryMap = {
      'dual_ma_cross': '双均线交叉',
      'triple_ma': '三均线',
      'macd_trend': 'MACD趋势',
      'bollinger_breakout': '布林带突破',
      'rsi_oversold': 'RSI超买超卖',
      'bollinger_reversion': '布林带回归',
      'support_resistance': '支撑阻力',
      'price_momentum': '价格动量',
      'volume_momentum': '成交量动量',
      'relative_strength': '相对强弱',
      'statistical_arbitrage': '统计套利',
      'triangular_arbitrage': '三角套利',
      'fixed_grid': '固定网格',
      'dynamic_grid': '动态网格',
      'martingale': '马丁格尔',
      'python_custom': 'Python自定义',
      'pinescript_custom': 'Pine Script自定义',
      'ml_strategy': '机器学习策略'
    }
    return categoryMap[category] || category
  },

  /**
   * 获取代码类型的中文标签
   */
  getCodeTypeLabel(codeType) {
    const codeTypeMap = {
      'python': 'Python',
      'pinescript': 'Pine Script'
    }
    return codeTypeMap[codeType] || codeType
  },

  /**
   * 获取策略状态的中文标签和颜色
   */
  getStatusInfo(status) {
    const statusMap = {
      'created': { label: '已创建', color: 'info' },
      'validated': { label: '已验证', color: 'success' },
      'active': { label: '运行中', color: 'success' },
      'inactive': { label: '已停止', color: 'warning' },
      'error': { label: '错误', color: 'danger' },
      'deleted': { label: '已删除', color: 'info' }
    }
    return statusMap[status] || { label: status, color: 'info' }
  },

  /**
   * 格式化策略参数显示
   */
  formatParameters(parameters) {
    if (!parameters || typeof parameters !== 'object') {
      return '无参数'
    }
    
    const formatted = Object.entries(parameters).map(([key, value]) => {
      return `${key}: ${value}`
    }).join(', ')
    
    return formatted || '无参数'
  },

  /**
   * 验证策略表单数据
   */
  validateStrategyForm(form) {
    const errors = []
    
    if (!form.name || form.name.trim().length === 0) {
      errors.push('策略名称不能为空')
    }
    
    if (form.name && form.name.length > 100) {
      errors.push('策略名称不能超过100个字符')
    }
    
    if (!form.type) {
      errors.push('请选择策略类型')
    }
    
    if (!form.category) {
      errors.push('请选择策略分类')
    }
    
    if (!form.code_content || form.code_content.trim().length === 0) {
      errors.push('策略代码不能为空')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  },

  /**
   * 生成策略代码模板
   */
  generateCodeTemplate(type, category) {
    const templates = {
      'trend_following': {
        'dual_ma_cross': `
def initialize(context):
    context.short_period = 5
    context.long_period = 20
    context.position = 0

def handle_bar(context, data):
    close_prices = data['close']
    short_ma = close_prices.rolling(context.short_period).mean()
    long_ma = close_prices.rolling(context.long_period).mean()
    
    if short_ma.iloc[-1] > long_ma.iloc[-1] and context.position <= 0:
        return {'action': 'BUY', 'price': close_prices.iloc[-1]}
    elif short_ma.iloc[-1] < long_ma.iloc[-1] and context.position >= 0:
        return {'action': 'SELL', 'price': close_prices.iloc[-1]}
    
    return {'action': 'HOLD'}
        `.trim()
      },
      'mean_reversion': {
        'rsi_oversold': `
def initialize(context):
    context.rsi_period = 14
    context.overbought = 70
    context.oversold = 30
    context.position = 0

def calculate_rsi(prices, period=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def handle_bar(context, data):
    close_prices = data['close']
    rsi = calculate_rsi(close_prices, context.rsi_period)
    
    if rsi.iloc[-1] < context.oversold and context.position <= 0:
        return {'action': 'BUY', 'price': close_prices.iloc[-1]}
    elif rsi.iloc[-1] > context.overbought and context.position >= 0:
        return {'action': 'SELL', 'price': close_prices.iloc[-1]}
    
    return {'action': 'HOLD'}
        `.trim()
      }
    }
    
    return templates[type]?.[category] || `
def initialize(context):
    # 初始化策略参数
    pass

def handle_bar(context, data):
    # 处理每个K线数据
    # data包含: open, high, low, close, volume
    return {'action': 'HOLD'}
    `.trim()
  },

  /**
   * 获取Pine Script示例代码
   */
  getPineScriptTemplate() {
    return `
//@version=5
strategy("My Strategy", overlay=true)

// 输入参数
length = input.int(14, title="Length")
source = input(close, title="Source")

// 计算指标
ma = ta.sma(source, length)

// 交易逻辑
if ta.crossover(close, ma)
    strategy.entry("Long", strategy.long)

if ta.crossunder(close, ma)
    strategy.close("Long")

// 绘制指标
plot(ma, color=color.blue, title="Moving Average")
    `.trim()
  }
}
