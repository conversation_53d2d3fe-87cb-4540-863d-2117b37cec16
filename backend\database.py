import sqlite3
from contextlib import contextmanager
from datetime import datetime
import os
import json

class Database:
    def __init__(self):
        # 使用SQLite数据库
        self.db_path = os.path.join(os.path.dirname(__file__), 'app.db')
        # 如果app.db不存在，尝试其他路径
        if not os.path.exists(self.db_path):
            alt_paths = [
                'backend/app.db',
                'app.db',
                os.path.join(os.path.dirname(__file__), '..', 'app.db')
            ]
            for path in alt_paths:
                if os.path.exists(path):
                    self.db_path = path
                    break
            else:
                # 如果都不存在，使用默认路径
                self.db_path = os.path.join(os.path.dirname(__file__), 'app.db')

        self._init_db()

    def _init_db(self):
        """初始化数据库表结构"""
        with self._get_cursor() as cur:
            # 创建通知表
            cur.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    level TEXT NOT NULL,
                    is_read INTEGER DEFAULT 0,
                    created_at TEXT NOT NULL,
                    read_at TEXT
                )
            ''')

            # 创建告警规则表
            cur.execute('''
                CREATE TABLE IF NOT EXISTS alert_rules (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    conditions TEXT NOT NULL,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    notify_channels TEXT,
                    description TEXT DEFAULT '',
                    level TEXT DEFAULT 'info'
                )
            ''')

            # 为现有表添加新字段（如果不存在）
            try:
                cur.execute('ALTER TABLE alert_rules ADD COLUMN description TEXT DEFAULT ""')
            except:
                pass  # 字段已存在

            try:
                cur.execute('ALTER TABLE alert_rules ADD COLUMN level TEXT DEFAULT "info"')
            except:
                pass  # 字段已存在

            # 创建索引
            cur.execute('CREATE INDEX IF NOT EXISTS idx_notifications_created ON notifications(created_at)')
            cur.execute('CREATE INDEX IF NOT EXISTS idx_alert_rules_type ON alert_rules(type)')

    @contextmanager
    def _get_cursor(self):
        """获取数据库游标的上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        try:
            with conn:
                cur = conn.cursor()
                yield cur
        finally:
            conn.close()

    # 通知相关操作
    def get_notifications(self, page=1, page_size=20):
        with self._get_cursor() as cur:
            offset = (page - 1) * page_size
            cur.execute('''
                SELECT * FROM notifications
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            ''', (page_size, offset))
            return cur.fetchall()

    def mark_as_read(self, notification_id):
        with self._get_cursor() as cur:
            cur.execute('''
                UPDATE notifications
                SET is_read = 1, read_at = ?
                WHERE id = ?
            ''', (datetime.now().isoformat(), notification_id))
            # SQLite不支持RETURNING，需要单独查询
            cur.execute('SELECT * FROM notifications WHERE id = ?', (notification_id,))
            return cur.fetchone()

    # 告警规则相关操作
    def get_alert_rules(self, page=1, page_size=20):
        with self._get_cursor() as cur:
            # 获取总数 - 显示所有规则，包括禁用的
            cur.execute('SELECT COUNT(*) FROM alert_rules')
            total = cur.fetchone()[0]

            # 获取分页数据 - 显示所有规则，包括禁用的
            offset = (page - 1) * page_size
            cur.execute('''
                SELECT * FROM alert_rules
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            ''', (page_size, offset))
            rules = cur.fetchall()

            return rules, total

    def create_alert_rule(self, rule_data):
        with self._get_cursor() as cur:
            import uuid
            now = datetime.now().isoformat()
            # 生成唯一的规则ID
            rule_id = f"rule-{str(uuid.uuid4())[:8]}"

            # 处理notify_channels，转换为JSON字符串
            notify_channels = json.dumps(rule_data.get('notify_channels', []))

            cur.execute('''
                INSERT INTO alert_rules (
                    id, name, type, conditions,
                    created_at, updated_at, notify_channels,
                    description, level
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
            ''', (
                rule_id,
                rule_data['name'],
                rule_data['type'],
                rule_data['conditions'],
                now,
                now,
                notify_channels,
                rule_data.get('description', ''),
                rule_data.get('level', 'info')
            ))

            # SQLite不支持RETURNING，需要单独查询
            cur.execute('SELECT * FROM alert_rules WHERE id = ?', (rule_id,))
            return cur.fetchone()

    def update_alert_rule(self, rule_id, update_data):
        with self._get_cursor() as cur:
            # 处理notify_channels，转换为JSON字符串
            notify_channels = json.dumps(update_data.get('notify_channels', []))
            is_active = 1 if update_data.get('is_active', True) else 0

            # 确保所有字段都被明确更新
            cur.execute('''
                UPDATE alert_rules
                SET name = ?,
                    type = ?,
                    conditions = ?,
                    notify_channels = ?,
                    is_active = ?,
                    updated_at = ?,
                    description = ?,
                    level = ?
                WHERE id = ?
            ''', (
                update_data['name'],
                update_data['type'],
                update_data['conditions'],
                notify_channels,
                is_active,
                datetime.now().isoformat(),
                update_data.get('description', ''),
                update_data.get('level', 'info'),
                rule_id
            ))

            # 检查是否有行被更新
            if cur.rowcount == 0:
                raise ValueError(f"规则 {rule_id} 更新失败")

            # SQLite不支持RETURNING，需要单独查询
            cur.execute('SELECT * FROM alert_rules WHERE id = ?', (rule_id,))
            result = cur.fetchone()
            if not result:
                raise ValueError(f"规则 {rule_id} 更新失败")
            return result

    def delete_alert_rule(self, rule_id):
        with self._get_cursor() as cur:
            # 先检查规则是否存在
            cur.execute('SELECT 1 FROM alert_rules WHERE id = ?', (rule_id,))
            if not cur.fetchone():
                raise ValueError(f"规则 {rule_id} 不存在")

            # 执行删除
            cur.execute('DELETE FROM alert_rules WHERE id = ?', (rule_id,))

            # 检查是否有行被删除
            if cur.rowcount == 0:
                raise ValueError(f"规则 {rule_id} 删除失败")

            # 验证删除是否成功
            cur.execute('SELECT 1 FROM alert_rules WHERE id = ?', (rule_id,))
            if cur.fetchone():
                raise ValueError(f"规则 {rule_id} 删除失败")

            return (rule_id,)  # 返回元组以保持兼容性

# 全局数据库实例
db = Database()