# 重启前端服务脚本

Write-Host "=== 重启前端服务 ===" -ForegroundColor Green

# 关闭占用8080端口的进程
Write-Host "关闭占用8080端口的进程..." -ForegroundColor Yellow
$processes = netstat -ano | findstr ":8080" | findstr "LISTENING"
if ($processes) {
    foreach ($line in $processes) {
        $parts = $line -split '\s+' | Where-Object { $_ -ne '' }
        $pid = $parts[-1]
        if ($pid -and $pid -match '^\d+$') {
            try {
                $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                if ($process -and $process.ProcessName -eq "node") {
                    Stop-Process -Id $pid -Force
                    Write-Host "已关闭Node.js进程 PID: $pid" -ForegroundColor Green
                }
            } catch {
                Write-Host "无法关闭进程 PID: $pid" -ForegroundColor Red
            }
        }
    }
    Start-Sleep -Seconds 3
}

# 进入前端目录并启动
Set-Location frontend
Write-Host "启动前端服务在8080端口..." -ForegroundColor Green
$env:PORT = "8080"
npm run serve
