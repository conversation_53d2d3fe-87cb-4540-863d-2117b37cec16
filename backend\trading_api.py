#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask, jsonify, request
from flask_cors import CORS
import logging
import time
from datetime import datetime, timedelta
import os
import json
import sys
import uuid
import ccxt

# 添加项目根目录到Python路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入交易所API工厂
try:
    from core.exchanges.exchange_factory import ExchangeFactory
    from core.exchanges.binance_api import BinanceAPI
    exchange_api_available = True
except ImportError as e:
    print(f"导入交易所API模块失败: {str(e)}")
    exchange_api_available = False

# 设置日志记录
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用CORS

# API版本前缀
API_PREFIX = '/api/v1'

# 交易所API实例
exchange_api = None

# 订单存储 - 实际应用中应使用数据库
orders = {}
executions = {}

def init_exchange_api():
    """初始化交易所API"""
    global exchange_api

    # 从环境变量获取API密钥
    api_key = os.environ.get("BINANCE_API_KEY", "")
    api_secret = os.environ.get("BINANCE_API_SECRET", "")
    use_testnet = os.environ.get("USE_TESTNET", "false").lower() == "true"

    if not api_key or not api_secret:
        logger.error("未设置API密钥，无法连接交易所API")
        return None

    if not exchange_api_available:
        logger.info("交易所API模块不可用，将使用ccxt直接集成")
        # 使用ccxt直接集成
        try:
            # 设置U本位合约API选项
            options = {
                'defaultType': 'future',  # 指定使用U本位合约API
                'adjustForTimeDifference': True
            }
            if use_testnet:
                options['test'] = True
                logger.info("使用币安测试网")

            exchange_api = ccxt.binance({
                'apiKey': api_key,
                'secret': api_secret,
                'enableRateLimit': True,
                'timeout': 30000,
                'options': options
            })
            logger.info("已配置连接到币安U本位合约API")
            logger.info("已使用ccxt初始化币安API")
        except Exception as e:
            logger.error(f"初始化币安API失败: {str(e)}")
            raise RuntimeError(f"初始化币安API失败: {str(e)}")
    else:
        # 使用项目的交易所API工厂
        try:
            # 创建选项字典，指定使用U本位合约API
            options = {
                'market_type': 'future',  # 指定使用U本位合约API
                'contract_type': 'usdm'   # 指定使用U本位合约
            }
            if use_testnet:
                options['testnet'] = True

            # 正确传递参数
            exchange_api = ExchangeFactory.create_exchange(
                exchange_name='binance',
                api_key=api_key,
                api_secret=api_secret,
                options=options
            )
            logger.info("已使用ExchangeFactory初始化币安API")
        except Exception as e:
            logger.error(f"使用ExchangeFactory初始化币安API失败: {str(e)}")

            # 尝试使用ccxt作为备选方案
            try:
                logger.info("尝试使用ccxt作为备选方案")
                # 设置U本位合约API选项
                options = {
                    'defaultType': 'future',  # 指定使用U本位合约API
                    'adjustForTimeDifference': True
                }
                if use_testnet:
                    options['test'] = True

                exchange_api = ccxt.binance({
                    'apiKey': api_key,
                    'secret': api_secret,
                    'enableRateLimit': True,
                    'timeout': 30000,
                    'options': options
                })
                logger.info("已配置连接到币安U本位合约API（备选方案）")
                logger.info("已使用ccxt初始化币安API")
            except Exception as ccxt_error:
                logger.error(f"使用ccxt初始化币安API也失败: {str(ccxt_error)}")
                raise RuntimeError(f"初始化币安API失败: {str(e)}")

# 获取当前连接的市场类型
def get_market_type():
    """获取当前连接的市场类型"""
    if not exchange_api:
        return "未连接"

    try:
        # 尝试从CCXT对象中获取市场类型
        if hasattr(exchange_api, 'options') and 'defaultType' in exchange_api.options:
            return exchange_api.options['defaultType']
        elif hasattr(exchange_api, 'market_type'):
            return exchange_api.market_type
        else:
            # 尝试通过API调用判断
            try:
                # 尝试获取U本位合约市场信息，如果成功则说明是连接到合约市场
                exchange_api.fapiPublicGetExchangeInfo()
                return "future (U本位合约)"
            except Exception:
                try:
                    # 尝试获取现货市场信息
                    exchange_api.publicGetExchangeInfo()
                    return "spot (现货)"
                except Exception:
                    return "unknown (未知)"
    except Exception as e:
        logger.error(f"获取市场类型失败: {str(e)}")
        return "error (获取失败)"

# 初始化交易所API
init_exchange_api()

# 健康检查端点
@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    market_type = get_market_type()
    return jsonify({
        "service": "trading-api",
        "status": "ok",
        "time": datetime.now().isoformat(),
        "version": "1.0.0",
        "exchange_api_status": "available" if exchange_api else "unavailable",
        "market_type": market_type,
        "note": "已配置连接到币安U本位合约API"
    })

# 交易API路由
@app.route(f'{API_PREFIX}/trading/orders', methods=['GET'])
def get_trading_orders():
    """获取交易订单列表"""
    logger.info("获取交易订单列表请求")

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)
    symbol = request.args.get('symbol', None)

    try:
        # 如果交易所API可用，尝试获取真实订单
        if exchange_api and hasattr(exchange_api, 'fetch_orders'):
            try:
                # 根据是否提供了symbol参数决定获取方式
                if symbol and hasattr(exchange_api, 'fetch_orders_by_symbol'):
                    real_orders = exchange_api.fetch_orders_by_symbol(symbol)
                elif hasattr(exchange_api, 'fetch_orders'):
                    real_orders = exchange_api.fetch_orders()
                else:
                    # 如果没有fetch_orders方法，尝试获取开放订单和已关闭订单
                    open_orders = exchange_api.fetch_open_orders() if hasattr(exchange_api, 'fetch_open_orders') else []
                    closed_orders = exchange_api.fetch_closed_orders() if hasattr(exchange_api, 'fetch_closed_orders') else []
                    real_orders = open_orders + closed_orders

                # 格式化订单数据
                formatted_orders = []
                for order in real_orders:
                    formatted_orders.append({
                        "id": order.get('id', ''),
                        "symbol": order.get('symbol', ''),
                        "type": order.get('type', 'limit'),
                        "side": order.get('side', 'buy'),
                        "price": float(order.get('price', 0)),
                        "amount": float(order.get('amount', 0)),
                        "status": order.get('status', 'pending'),
                        "created_at": datetime.fromtimestamp(order.get('timestamp', 0)/1000).isoformat() if order.get('timestamp') else datetime.now().isoformat(),
                        "updated_at": datetime.fromtimestamp(order.get('lastTradeTimestamp', 0)/1000).isoformat() if order.get('lastTradeTimestamp') else datetime.now().isoformat()
                    })

                # 如果提供了symbol参数，进行过滤
                if symbol and not hasattr(exchange_api, 'fetch_orders_by_symbol'):
                    formatted_orders = [order for order in formatted_orders if order['symbol'] == symbol]

                # 分页
                start_idx = (page - 1) * limit
                end_idx = start_idx + limit
                paginated_orders = formatted_orders[start_idx:end_idx] if start_idx < len(formatted_orders) else []

                # 同时更新本地存储，以便其他API使用
                for order in formatted_orders:
                    orders[order['id']] = order

                return jsonify({
                    "status": "success",
                    "orders": paginated_orders,
                    "total": len(formatted_orders),
                    "page": page,
                    "limit": limit
                })
            except Exception as e:
                logger.error(f"从交易所获取订单失败: {str(e)}")
                # 继续使用本地存储

        # 没有API连接，返回错误
        error_msg = "未配置交易所API或API连接失败，无法获取订单数据"
        logger.error(error_msg)
        return jsonify({
            "status": "error",
            "message": error_msg
        }), 401
    except Exception as e:
        logger.error(f"获取订单列表失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"获取订单列表失败: {str(e)}"
        }), 500

@app.route(f'{API_PREFIX}/trading/orders/<order_id>', methods=['GET'])
def get_trading_order(order_id):
    """获取单个交易订单详情"""
    logger.info(f"获取交易订单详情请求: {order_id}")

    try:
        # 尝试从交易所获取订单
        try:
            # 获取真实订单
            if hasattr(exchange_api, 'fetch_order'):
                order = exchange_api.fetch_order(order_id)

                # 格式化订单数据
                formatted_order = {
                    "id": order.get('id', ''),
                    "symbol": order.get('symbol', ''),
                    "type": order.get('type', 'limit'),
                    "side": order.get('side', 'buy'),
                    "price": float(order.get('price', 0)),
                    "amount": float(order.get('amount', 0)),
                    "status": order.get('status', 'pending'),
                    "created_at": datetime.fromtimestamp(order.get('timestamp', 0)/1000).isoformat() if order.get('timestamp') else datetime.now().isoformat(),
                    "updated_at": datetime.fromtimestamp(order.get('lastTradeTimestamp', 0)/1000).isoformat() if order.get('lastTradeTimestamp') else datetime.now().isoformat(),
                    "filled_amount": float(order.get('filled', 0)),
                    "fee": float(order.get('fee', {}).get('cost', 0)) if order.get('fee') else 0,
                    "total": float(order.get('cost', 0))
                }

                # 更新本地存储
                orders[order_id] = formatted_order

                return jsonify({
                    "status": "success",
                    "order": formatted_order
                })
            else:
                raise ValueError("交易所API不支持fetch_order方法")
        except Exception as e:
            logger.error(f"从交易所获取订单详情失败: {str(e)}")

            # API连接失败，返回错误
            error_msg = f"无法从交易所API获取订单详情: {str(e)}"
            logger.error(error_msg)
            return jsonify({
                "status": "error",
                "message": error_msg
            }), 500
    except Exception as e:
        logger.error(f"获取订单详情失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"获取订单详情失败: {str(e)}"
        }), 500

@app.route(f'{API_PREFIX}/trading/executions', methods=['GET'])
def get_trading_executions():
    """获取交易执行记录列表"""
    logger.info("获取交易执行记录列表请求")

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)
    symbol = request.args.get('symbol', None)

    try:
        # 如果交易所API可用，尝试获取真实交易记录
        if exchange_api and hasattr(exchange_api, 'fetch_my_trades'):
            try:
                # 如果提供了symbol参数，按交易对获取
                if symbol:
                    real_trades = exchange_api.fetch_my_trades(symbol)
                else:
                    real_trades = exchange_api.fetch_my_trades()

                # 格式化交易记录数据
                formatted_executions = []
                for trade in real_trades:
                    formatted_execution = {
                        "id": trade.get('id', ''),
                        "order_id": trade.get('order', ''),
                        "symbol": trade.get('symbol', ''),
                        "side": trade.get('side', ''),
                        "price": float(trade.get('price', 0)),
                        "amount": float(trade.get('amount', 0)),
                        "fee": float(trade.get('fee', {}).get('cost', 0)) if trade.get('fee') else 0,
                        "executed_at": datetime.fromtimestamp(trade.get('timestamp', 0)/1000).isoformat() if trade.get('timestamp') else datetime.now().isoformat()
                    }
                    formatted_executions.append(formatted_execution)

                    # 同时更新本地存储
                    executions[formatted_execution['id']] = formatted_execution

                # 分页
                start_idx = (page - 1) * limit
                end_idx = start_idx + limit
                paginated_executions = formatted_executions[start_idx:end_idx] if start_idx < len(formatted_executions) else []

                return jsonify({
                    "status": "success",
                    "executions": paginated_executions,
                    "total": len(formatted_executions),
                    "page": page,
                    "limit": limit
                })
            except Exception as e:
                logger.error(f"从交易所获取交易记录失败: {str(e)}")
                # 继续使用本地存储

        # 没有API连接，返回错误
        error_msg = "未配置交易所API或API连接失败，无法获取交易执行记录"
        logger.error(error_msg)
        return jsonify({
            "status": "error",
            "message": error_msg
        }), 401
    except Exception as e:
        logger.error(f"获取交易记录列表失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"获取交易记录列表失败: {str(e)}"
        }), 500

@app.route(f'{API_PREFIX}/trading/status', methods=['GET'])
def get_trading_status():
    """获取交易系统状态"""
    logger.info("获取交易系统状态请求")

    try:
        # 获取真实交易状态
        active_orders_count = 0
        total_orders_count = 0
        exchange_status = "disconnected"

        try:
            # 检查交易所API连接状态
            if exchange_api:
                # 尝试获取活跃订单数量
                if hasattr(exchange_api, 'fetch_open_orders'):
                    open_orders = exchange_api.fetch_open_orders()
                    active_orders_count = len(open_orders)
                    exchange_status = "connected"

                # 尝试获取总订单数量
                if hasattr(exchange_api, 'fetch_orders'):
                    all_orders = exchange_api.fetch_orders()
                    total_orders_count = len(all_orders)
                elif len(orders) > 0:
                    # 如果API不支持fetch_orders但本地有订单数据
                    total_orders_count = len(orders)

                # 尝试获取账户余额，进一步验证API连接
                if hasattr(exchange_api, 'fetch_balance'):
                    exchange_api.fetch_balance()
                    exchange_status = "connected"

            # 获取策略数据（实际应用中应从策略服务获取）
            active_strategies = 3  # 示例值
            total_strategies = 5   # 示例值

            # 返回真实数据
            current_time = datetime.now()
            return jsonify({
                "status": "success",
                "data": {
                    "is_trading_enabled": True,
                    "active_strategies": active_strategies,
                    "total_strategies": total_strategies,
                    "active_orders": active_orders_count,
                    "total_orders": total_orders_count,
                    "last_update": current_time.isoformat(),
                    "exchange_status": exchange_status,
                    "api_key_configured": bool(os.environ.get("BINANCE_API_KEY", "")),
                    "market_type": get_market_type(),
                    "note": "已配置连接到币安U本位合约API"
                }
            })
        except Exception as e:
            logger.error(f"获取交易所状态失败: {str(e)}")

            # 返回基本状态信息
            current_time = datetime.now()
            return jsonify({
                "status": "success",
                "data": {
                    "is_trading_enabled": False,
                    "active_strategies": 0,
                    "total_strategies": 0,
                    "active_orders": 0,
                    "total_orders": len(orders),
                    "last_update": current_time.isoformat(),
                    "exchange_status": "error",
                    "api_key_configured": bool(os.environ.get("BINANCE_API_KEY", "")),
                    "market_type": "未连接 (错误)",
                    "note": "已配置连接到币安U本位合约API，但连接失败",
                    "error": str(e)
                }
            })
    except Exception as e:
        logger.error(f"获取交易系统状态失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"获取交易系统状态失败: {str(e)}"
        }), 500

@app.route(f'{API_PREFIX}/trading/orders', methods=['POST'])
def create_trading_order():
    """创建交易订单"""
    logger.info("创建交易订单请求")

    try:
        # 获取请求数据
        order_data = request.json
        if not order_data:
            return jsonify({
                "status": "error",
                "message": "请求数据为空"
            }), 400

        # 验证必要字段
        required_fields = ['symbol', 'side', 'amount']
        for field in required_fields:
            if field not in order_data:
                return jsonify({
                    "status": "error",
                    "message": f"缺少必要字段: {field}"
                }), 400

        # 准备订单数据
        symbol = order_data.get('symbol')
        side = order_data.get('side')
        amount = float(order_data.get('amount'))
        price = float(order_data.get('price', 0))
        order_type = order_data.get('type', 'limit').lower()

        # 创建订单
        if exchange_api and hasattr(exchange_api, 'create_order'):
            try:
                # 使用交易所API创建订单
                params = {}

                # 如果是市价单，不需要价格
                if order_type == 'market':
                    result = exchange_api.create_order(symbol, order_type, side, amount, None, params)
                else:
                    # 限价单需要价格
                    if price <= 0:
                        return jsonify({
                            "status": "error",
                            "message": "限价单需要有效的价格"
                        }), 400

                    result = exchange_api.create_order(symbol, order_type, side, amount, price, params)

                # 格式化订单数据
                formatted_order = {
                    "id": result.get('id', ''),
                    "symbol": result.get('symbol', ''),
                    "type": result.get('type', 'limit'),
                    "side": result.get('side', ''),
                    "price": float(result.get('price', 0)),
                    "amount": float(result.get('amount', 0)),
                    "status": result.get('status', 'pending'),
                    "created_at": datetime.fromtimestamp(result.get('timestamp', 0)/1000).isoformat() if result.get('timestamp') else datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }

                # 保存到本地存储
                orders[formatted_order['id']] = formatted_order

                return jsonify({
                    "status": "success",
                    "message": "订单已创建",
                    "order": formatted_order
                })
            except Exception as e:
                logger.error(f"创建订单失败: {str(e)}")
                return jsonify({
                    "status": "error",
                    "message": f"创建订单失败: {str(e)}"
                }), 500
        else:
            # 没有API连接，返回错误
            error_msg = "未配置交易所API或API连接失败，无法创建订单"
            logger.error(error_msg)
            return jsonify({
                "status": "error",
                "message": error_msg
            }), 401
    except Exception as e:
        logger.error(f"创建订单请求处理失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"创建订单请求处理失败: {str(e)}"
        }), 500

@app.route(f'{API_PREFIX}/trading/orders/<order_id>/cancel', methods=['POST'])
def cancel_trading_order(order_id):
    """取消交易订单"""
    logger.info(f"取消交易订单请求: {order_id}")

    try:
        # 尝试使用交易所API取消订单
        try:
            if hasattr(exchange_api, 'cancel_order'):
                # 使用交易所API取消订单
                cancel_result = exchange_api.cancel_order(order_id)
                logger.info(f"订单取消结果: {cancel_result}")

                # 更新本地存储
                if order_id in orders:
                    orders[order_id]['status'] = 'cancelled'
                    orders[order_id]['updated_at'] = datetime.now().isoformat()

                return jsonify({
                    "status": "success",
                    "message": "订单已取消",
                    "order_id": order_id,
                    "result": cancel_result
                })
            else:
                raise ValueError("交易所API不支持cancel_order方法")
        except Exception as e:
            logger.error(f"通过API取消订单失败: {str(e)}")

            # API连接失败，返回错误
            error_msg = f"无法通过交易所API取消订单: {str(e)}"
            logger.error(error_msg)
            return jsonify({
                "status": "error",
                "message": error_msg,
                "order_id": order_id
            }), 500
    except Exception as e:
        logger.error(f"取消订单请求处理失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"取消订单请求处理失败: {str(e)}"
        }), 500

# 下面是应用程序的主入口
if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8003))
    logger.info(f"启动交易API服务，端口: {port}")
    logger.info(f"健康检查端点: http://localhost:{port}/health")
    app.run(host='0.0.0.0', port=port, debug=False)
