#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
初始化数据质量报告表
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('init_quality_reports_table.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# 数据库路径
DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market_data.db')
ALT_DB_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'app.db')
THIRD_DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app.db')

def get_db_connection():
    """获取数据库连接"""
    try:
        if os.path.exists(DB_PATH):
            logger.info(f"使用数据库: {DB_PATH}")
            return sqlite3.connect(DB_PATH)
        elif os.path.exists(ALT_DB_PATH):
            logger.info(f"使用替代数据库: {ALT_DB_PATH}")
            return sqlite3.connect(ALT_DB_PATH)
        elif os.path.exists(THIRD_DB_PATH):
            logger.info(f"使用第三替代数据库: {THIRD_DB_PATH}")
            return sqlite3.connect(THIRD_DB_PATH)
        else:
            # 如果找不到现有数据库，创建一个新的
            logger.warning("未找到现有数据库，将创建新数据库")
            os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
            return sqlite3.connect(DB_PATH)
    except Exception as e:
        logger.error(f"连接数据库失败: {str(e)}")
        raise

def init_quality_reports_table():
    """初始化数据质量报告表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查表是否已存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='quality_reports'")
        if cursor.fetchone():
            logger.info("数据质量报告表已存在")
        else:
            # 创建表
            logger.info("创建数据质量报告表")
            cursor.execute("""
            CREATE TABLE quality_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                report_type TEXT NOT NULL,
                source_id INTEGER NOT NULL,
                status TEXT NOT NULL,
                summary TEXT,
                details TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT
            )
            """)

            # 创建索引
            cursor.execute("CREATE INDEX idx_quality_reports_source_id ON quality_reports(source_id)")
            cursor.execute("CREATE INDEX idx_quality_reports_created_at ON quality_reports(created_at)")

            conn.commit()
            logger.info("数据质量报告表创建成功")

        conn.close()
        return True
    except Exception as e:
        logger.error(f"初始化数据质量报告表失败: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("开始初始化数据质量报告表")
    if init_quality_reports_table():
        logger.info("数据质量报告表初始化成功")
    else:
        logger.error("数据质量报告表初始化失败")
