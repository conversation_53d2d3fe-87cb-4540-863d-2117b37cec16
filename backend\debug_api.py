#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试API
"""

import os
import sys
import sqlite3
import json
import logging
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug_api.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# 数据库路径
DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market_data.db')

def check_db():
    """检查数据库"""
    logger.info(f"检查数据库: {DB_PATH}")
    
    if not os.path.exists(DB_PATH):
        logger.error(f"数据库文件不存在: {DB_PATH}")
        return
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # 获取所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    logger.info("数据库中的表:")
    for table in tables:
        logger.info(f"  - {table[0]}")
    
    # 检查quality_reports表
    if ('quality_reports',) in tables:
        logger.info("\n质量报告表:")
        cursor.execute("PRAGMA table_info(quality_reports)")
        columns = cursor.fetchall()
        logger.info("  列:")
        for column in columns:
            logger.info(f"    - {column[1]} ({column[2]})")
        
        # 获取报告数量
        cursor.execute("SELECT COUNT(*) FROM quality_reports")
        count = cursor.fetchone()[0]
        logger.info(f"  报告数量: {count}")
        
        # 获取最新的报告
        if count > 0:
            cursor.execute("SELECT id, title, report_type, source_id, status, created_at, summary, details FROM quality_reports ORDER BY created_at DESC LIMIT 1")
            report = cursor.fetchone()
            logger.info("\n最新报告:")
            logger.info(f"  ID: {report[0]}")
            logger.info(f"  标题: {report[1]}")
            logger.info(f"  类型: {report[2]}")
            logger.info(f"  数据源ID: {report[3]}")
            logger.info(f"  状态: {report[4]}")
            logger.info(f"  创建时间: {report[5]}")
            logger.info(f"  摘要: {report[6]}")
            logger.info(f"  详情: {report[7][:100]}...")
    
    conn.close()

def test_api():
    """测试API"""
    logger.info("测试API")
    
    # 测试获取最近报告
    try:
        logger.info("测试获取最近报告")
        response = requests.get("http://localhost:8005/api/v1/data-quality/reports/recent?limit=5", timeout=10)
        logger.info(f"状态码: {response.status_code}")
        logger.info(f"响应: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"成功: {data.get('success')}")
            logger.info(f"报告数量: {len(data.get('data', {}).get('reports', []))}")
    except Exception as e:
        logger.error(f"测试获取最近报告失败: {str(e)}")
    
    # 测试生成报告
    try:
        logger.info("测试生成报告")
        response = requests.post(
            "http://localhost:8005/api/v1/data-quality/reports/generate",
            json={
                "report_type": "daily",
                "source_ids": [1],
                "include_charts": True,
                "include_insights": True
            },
            timeout=10
        )
        logger.info(f"状态码: {response.status_code}")
        logger.info(f"响应: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"成功: {data.get('success')}")
            logger.info(f"消息: {data.get('message')}")
    except Exception as e:
        logger.error(f"测试生成报告失败: {str(e)}")

if __name__ == "__main__":
    logger.info("开始调试API")
    check_db()
    test_api()
