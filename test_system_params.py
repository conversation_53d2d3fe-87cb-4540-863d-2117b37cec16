#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

def test_system_params():
    """测试系统参数保存功能"""
    
    base_url = "http://localhost:8000/api/v1/unified-config"
    
    print("🔧 测试系统参数功能...")
    print("=" * 50)
    
    # 1. 获取当前系统参数
    print("\n1. 获取当前系统参数...")
    try:
        response = requests.get(f"{base_url}/system-params")
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取系统参数成功")
            print(f"当前参数: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 获取系统参数失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 获取系统参数失败: {e}")
        return
    
    # 2. 清理并重新设置系统参数
    print("\n2. 清理并重新设置系统参数...")
    try:
        clean_params = {
            "performance": {
                "maxThreads": 8,
                "cacheSize": 512,
                "connectionTimeout": 30
            },
            "network": {
                "wsReconnectInterval": 3,
                "apiRateLimit": 150
            },
            "system": {
                "title": "量子交易平台",
                "timezone": "Asia/Shanghai",
                "language": "zh-CN"
            }
        }
        
        response = requests.post(f"{base_url}/system-params", json=clean_params)
        if response.status_code == 200:
            result = response.json()
            print("✅ 清理系统参数成功")
            print(f"响应: {result['message']}")
        else:
            print(f"❌ 清理系统参数失败: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"❌ 清理系统参数失败: {e}")
        return
    
    # 3. 验证参数是否正确保存
    print("\n3. 验证参数是否正确保存...")
    try:
        response = requests.get(f"{base_url}/system-params")
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取更新后的系统参数成功")
            print(f"更新后参数: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
            
            # 检查是否有重复字段
            params = result['data']
            if 'maxThreads' in params and 'performance' in params:
                print("❌ 发现重复字段！")
            else:
                print("✅ 没有重复字段，参数结构正确")
        else:
            print(f"❌ 获取更新后的系统参数失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取更新后的系统参数失败: {e}")
    
    # 4. 测试部分更新
    print("\n4. 测试部分更新...")
    try:
        partial_update = {
            "performance": {
                "maxThreads": 16
            }
        }
        
        response = requests.post(f"{base_url}/system-params", json=partial_update)
        if response.status_code == 200:
            result = response.json()
            print("✅ 部分更新成功")
            
            # 验证部分更新结果
            response = requests.get(f"{base_url}/system-params")
            if response.status_code == 200:
                result = response.json()
                params = result['data']
                if params['performance']['maxThreads'] == 16:
                    print("✅ 部分更新验证成功")
                else:
                    print("❌ 部分更新验证失败")
        else:
            print(f"❌ 部分更新失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 部分更新失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 系统参数功能测试完成！")

if __name__ == "__main__":
    test_system_params()
