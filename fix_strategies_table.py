import sqlite3
import json
from datetime import datetime

def fix_strategies_table():
    """修复strategies表结构"""
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        # 1. 备份现有数据
        print("备份现有策略数据...")
        cursor.execute("SELECT * FROM strategies")
        old_strategies = cursor.fetchall()
        
        # 获取旧表的列名
        cursor.execute("PRAGMA table_info(strategies)")
        old_columns = [row[1] for row in cursor.fetchall()]
        print(f"旧表列: {old_columns}")
        
        # 2. 删除旧表
        cursor.execute("DROP TABLE strategies")
        
        # 3. 创建新表
        print("创建新的strategies表...")
        cursor.execute("""
            CREATE TABLE strategies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                type VARCHAR(50) NOT NULL DEFAULT 'custom',
                category VARCHAR(50) NOT NULL DEFAULT 'python_custom',
                description TEXT,
                code_type VARCHAR(20) DEFAULT 'python',
                code_content TEXT,
                parameters JSON,
                template_id INTEGER,
                symbol VARCHAR(20),
                timeframe VARCHAR(10),
                file_path VARCHAR(255),
                status VARCHAR(20) DEFAULT 'created',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                creator_id INTEGER
            )
        """)
        
        # 4. 迁移数据
        print("迁移现有数据...")
        for strategy in old_strategies:
            # 映射旧数据到新结构
            strategy_dict = dict(zip(old_columns, strategy))
            
            cursor.execute("""
                INSERT INTO strategies (
                    id, name, type, category, description, code_type, code_content,
                    parameters, status, is_active, created_at, updated_at, creator_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                strategy_dict.get('id'),
                strategy_dict.get('name'),
                'custom',  # 默认类型
                'python_custom',  # 默认分类
                strategy_dict.get('description'),
                'python',  # 默认代码类型
                strategy_dict.get('code'),  # 旧的code字段映射到code_content
                strategy_dict.get('parameters', '{}'),
                'created',  # 默认状态
                strategy_dict.get('is_active', 1),
                strategy_dict.get('created_at'),
                strategy_dict.get('updated_at'),
                strategy_dict.get('creator_id')
            ))
        
        # 5. 创建索引
        print("创建索引...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategies_type ON strategies(type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategies_category ON strategies(category)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategies_creator ON strategies(creator_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategies_active ON strategies(is_active)")
        
        conn.commit()
        print("表结构修复完成")
        
        # 6. 验证
        cursor.execute("PRAGMA table_info(strategies)")
        new_columns = [row[1] for row in cursor.fetchall()]
        print(f"新表列: {new_columns}")
        
        cursor.execute("SELECT COUNT(*) FROM strategies")
        count = cursor.fetchone()[0]
        print(f"策略记录数: {count}")
        
    except Exception as e:
        conn.rollback()
        print(f"修复失败: {str(e)}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    fix_strategies_table()
