#!/usr/bin/env python3
"""
生成铃声音频文件的脚本
为通知系统创建基本的音频文件
"""

import os
import numpy as np
import wave
import struct
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_tone(frequency, duration, sample_rate=44100, amplitude=0.3):
    """
    生成指定频率和持续时间的音调
    
    Args:
        frequency: 频率 (Hz)
        duration: 持续时间 (秒)
        sample_rate: 采样率
        amplitude: 振幅 (0-1)
    
    Returns:
        numpy array: 音频数据
    """
    frames = int(duration * sample_rate)
    t = np.linspace(0, duration, frames)
    
    # 生成正弦波
    wave_data = amplitude * np.sin(2 * np.pi * frequency * t)
    
    # 添加淡入淡出效果
    fade_frames = int(0.1 * sample_rate)  # 0.1秒淡入淡出
    if fade_frames < frames:
        # 淡入
        wave_data[:fade_frames] *= np.linspace(0, 1, fade_frames)
        # 淡出
        wave_data[-fade_frames:] *= np.linspace(1, 0, fade_frames)
    
    return wave_data

def generate_multi_tone(frequencies, duration, sample_rate=44100, amplitude=0.3):
    """
    生成多频率混合音调
    
    Args:
        frequencies: 频率列表
        duration: 持续时间
        sample_rate: 采样率
        amplitude: 振幅
    
    Returns:
        numpy array: 音频数据
    """
    frames = int(duration * sample_rate)
    t = np.linspace(0, duration, frames)
    
    wave_data = np.zeros(frames)
    for freq in frequencies:
        wave_data += (amplitude / len(frequencies)) * np.sin(2 * np.pi * freq * t)
    
    # 添加淡入淡出效果
    fade_frames = int(0.05 * sample_rate)  # 0.05秒淡入淡出
    if fade_frames < frames:
        wave_data[:fade_frames] *= np.linspace(0, 1, fade_frames)
        wave_data[-fade_frames:] *= np.linspace(1, 0, fade_frames)
    
    return wave_data

def save_wav_file(filename, audio_data, sample_rate=44100):
    """
    保存音频数据为WAV文件
    
    Args:
        filename: 文件名
        audio_data: 音频数据
        sample_rate: 采样率
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    
    # 转换为16位整数
    audio_data = np.clip(audio_data, -1.0, 1.0)
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # 保存WAV文件
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())
    
    logger.info(f"已生成音频文件: {filename}")

def generate_notification_sounds():
    """生成通知系统所需的音频文件"""
    
    sounds_dir = os.path.join(os.path.dirname(__file__), 'static', 'sounds')
    
    # 定义音频文件配置
    sound_configs = {
        'notification-1.mp3': {
            'type': 'single_tone',
            'frequency': 800,
            'duration': 0.5,
            'description': '默认通知音'
        },
        'notification-2.mp3': {
            'type': 'multi_tone',
            'frequencies': [600, 800],
            'duration': 0.8,
            'description': '轻柔通知音'
        },
        'warning-1.mp3': {
            'type': 'multi_tone',
            'frequencies': [400, 600, 800],
            'duration': 1.0,
            'description': '默认警告音'
        },
        'warning-2.mp3': {
            'type': 'single_tone',
            'frequency': 1000,
            'duration': 1.2,
            'description': '紧急警告音'
        },
        'alert-1.mp3': {
            'type': 'multi_tone',
            'frequencies': [800, 1200, 1600],
            'duration': 1.5,
            'description': '默认警报音'
        },
        'alert-2.mp3': {
            'type': 'single_tone',
            'frequency': 1500,
            'duration': 2.0,
            'description': '紧急警报音'
        },
        'success-1.mp3': {
            'type': 'multi_tone',
            'frequencies': [523, 659, 784],  # C-E-G 和弦
            'duration': 1.0,
            'description': '成功提示音'
        },
        'system-1.mp3': {
            'type': 'single_tone',
            'frequency': 440,
            'duration': 0.8,
            'description': '系统通知音'
        }
    }
    
    logger.info(f"开始生成音频文件到目录: {sounds_dir}")
    
    for filename, config in sound_configs.items():
        # 将.mp3扩展名改为.wav，因为我们生成的是WAV格式
        wav_filename = filename.replace('.mp3', '.wav')
        filepath = os.path.join(sounds_dir, wav_filename)
        
        try:
            if config['type'] == 'single_tone':
                audio_data = generate_tone(
                    frequency=config['frequency'],
                    duration=config['duration']
                )
            elif config['type'] == 'multi_tone':
                audio_data = generate_multi_tone(
                    frequencies=config['frequencies'],
                    duration=config['duration']
                )
            
            save_wav_file(filepath, audio_data)
            logger.info(f"✓ {config['description']}: {wav_filename}")
            
        except Exception as e:
            logger.error(f"生成 {filename} 失败: {str(e)}")
    
    logger.info("音频文件生成完成!")

if __name__ == "__main__":
    try:
        generate_notification_sounds()
    except Exception as e:
        logger.error(f"生成音频文件时发生错误: {str(e)}")
        print("请确保已安装numpy库: pip install numpy")
