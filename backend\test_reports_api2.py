#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask, jsonify, request
import logging
from datetime import datetime, timedelta

# 设置日志记录
logging.basicConfig(level=logging.DEBUG,
                  format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 允许跨域请求
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', 'http://localhost:8081')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    response.headers.add('Access-Control-Allow-Credentials', 'true')
    return response

# 处理OPTIONS请求
@app.route('/', defaults={'path': ''}, methods=['OPTIONS'])
@app.route('/<path:path>', methods=['OPTIONS'])
def options_handler(path):
    logger.debug(f"处理OPTIONS请求: {path}")
    return '', 200

# 健康检查接口
@app.route('/api/v1/health', methods=['GET'])
def health_check():
    logger.info("健康检查请求")
    return jsonify({
        "status": "ok",
        "service": "reports-api",
        "time": datetime.now().isoformat(),
        "version": "1.0.0"
    })

# 调试信息
@app.route('/debug', methods=['GET'])
def debug_info():
    """调试信息"""
    logger.info("调试信息请求")
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append({
            'endpoint': rule.endpoint,
            'methods': list(rule.methods),
            'path': str(rule)
        })
    return jsonify({
        "routes": routes,
        "request_headers": dict(request.headers),
        "request_args": dict(request.args),
        "request_path": request.path
    })

# 报告列表API
@app.route('/api/v1/data-quality/reports/list/<report_type>', methods=['GET'])
def get_reports_list(report_type):
    """获取指定类型的数据质量报告列表"""
    logger.info(f"获取{report_type}报告列表请求")
    
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)
    
    # 生成模拟报告数据
    reports = []
    total = 0
    
    if report_type == 'daily':
        total = 5
        for i in range(total):
            report_date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
            reports.append({
                "id": i + 1,
                "filename": f"report_{i+1}.md",
                "path": f"/reports/daily/report_{i+1}.md",
                "date_info": report_date,
                "size_kb": 10.5 + i,
                "created_time": (datetime.now() - timedelta(days=i)).isoformat()
            })
    elif report_type == 'weekly':
        total = 4
        for i in range(total):
            report_date = (datetime.now() - timedelta(weeks=i)).strftime("%Y-%m-%d")
            reports.append({
                "id": i + 1,
                "filename": f"report_{i+1}.md",
                "path": f"/reports/weekly/report_{i+1}.md",
                "date_info": report_date,
                "size_kb": 15.5 + i,
                "created_time": (datetime.now() - timedelta(weeks=i)).isoformat()
            })
    elif report_type == 'monthly':
        total = 3
        for i in range(total):
            report_date = (datetime.now() - timedelta(days=30*i)).strftime("%Y-%m-%d")
            reports.append({
                "id": i + 1,
                "filename": f"report_{i+1}.md",
                "path": f"/reports/monthly/report_{i+1}.md",
                "date_info": report_date,
                "size_kb": 25.5 + i,
                "created_time": (datetime.now() - timedelta(days=30*i)).isoformat()
            })
    
    # 分页处理
    start_idx = (page - 1) * limit
    end_idx = min(start_idx + limit, len(reports))
    paged_reports = reports[start_idx:end_idx]
    
    return jsonify({
        "success": True,
        "data": {
            "report_type": report_type,
            "reports": paged_reports,
            "total": total
        }
    })

# 报告内容API
@app.route('/api/v1/data-quality/reports/content/<report_type>/<filename>', methods=['GET'])
def get_report_content(report_type, filename):
    """获取报告内容"""
    logger.info(f"获取报告内容请求: {report_type}/{filename}")
    
    # 从文件名中提取报告ID
    report_id = None
    if filename.startswith('report_') and filename.endswith('.md'):
        try:
            report_id = int(filename.replace('report_', '').replace('.md', ''))
        except ValueError:
            pass
    
    if not report_id:
        return jsonify({
            "success": False,
            "error": {"message": "无效的报告文件名"}
        }), 400
    
    # 生成报告内容
    title = f"{report_type.capitalize()} 数据质量报告 #{report_id}"
    created_at = (datetime.now() - timedelta(days=report_id)).isoformat()
    
    # 生成Markdown内容
    markdown_content = f"""# {title}

## 报告概述

- **报告类型**: {report_type}
- **创建时间**: {created_at}
- **状态**: 已完成

## 数据质量摘要

- **总记录数**: 1,245
- **缺失记录数**: 3
- **完整性评分**: 99.8%
- **准确性评分**: 98.5%
- **总体评分**: 99.2%

## 数据质量详情

### 数据缺口

#### 1h 时间级别

| 开始时间 | 结束时间 | 持续时间(分钟) | 严重程度 |
|---------|---------|--------------|--------|
| 2025-04-10T14:00:00 | 2025-04-10T15:00:00 | 60 | 中等 |
| 2025-04-11T02:00:00 | 2025-04-11T03:00:00 | 60 | 中等 |

### 数据异常

| 时间 | 类型 | 描述 | 严重程度 |
|------|------|------|--------|
| 2025-04-10T18:30:00 | 价格异常 | 价格波动超过正常范围 | 低 |
| 2025-04-11T09:15:00 | 交易量异常 | 交易量突然增加 | 中等 |

## 建议

- 定期检查数据质量，确保数据完整性
- 监控数据延迟，确保实时性
- 对异常数据进行分析，找出根本原因
"""
    
    return jsonify({
        "success": True,
        "data": {
            "content": markdown_content,
            "date": created_at
        }
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8004, debug=True)
