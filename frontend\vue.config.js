const path = require('path')
const { execSync } = require('child_process')

// 端口冲突检测和处理函数
function checkAndKillPort(port) {
  try {
    console.log(`检查端口 ${port} 是否被占用...`)

    // 检查端口占用情况
    const result = execSync(`netstat -ano | findstr :${port}`, { encoding: 'utf8' })

    if (result) {
      console.log(`端口 ${port} 被占用，正在关闭占用进程...`)

      // 提取PID并关闭进程
      const lines = result.split('\n').filter(line => line.trim())
      const pids = new Set()

      lines.forEach(line => {
        const parts = line.trim().split(/\s+/)
        const pid = parts[parts.length - 1]
        if (pid && /^\d+$/.test(pid)) {
          pids.add(pid)
        }
      })

      pids.forEach(pid => {
        try {
          execSync(`taskkill /F /PID ${pid}`, { stdio: 'ignore' })
          console.log(`已关闭进程 PID: ${pid}`)
        } catch (e) {
          // 忽略关闭失败的情况
        }
      })

      console.log(`端口 ${port} 清理完成，等待端口释放...`)
      // 等待端口释放
      setTimeout(() => {}, 2000)
    } else {
      console.log(`端口 ${port} 未被占用`)
    }
  } catch (error) {
    console.log(`端口 ${port} 检查完成`)
  }
}

// 在启动前检查并清理8080端口
checkAndKillPort(8080)

module.exports = {
  // 部署应用包时的基本URL
  publicPath: '/',

  // 关闭eslint检查
  lintOnSave: false,

  // 生产环境不需要 source map
  productionSourceMap: false,

  // 配置开发服务器
  devServer: {
    port: 8080,
    open: true,
    hot: true,
    host: '0.0.0.0',
    overlay: {
      warnings: false,
      errors: true
    },
    // 增加服务器最大监听器数量，修复MaxListenersExceededWarning警告
    before(app, server) {
      if (server && server._server) {
        server._server.setMaxListeners(30);
      }
    },
    // 配置API代理
    proxy: {
      // 代理数据质量报告API请求 - 必须放在最前面，优先匹配
      '/api/v1/data-quality/reports': {
        target: 'http://localhost:8006',
        changeOrigin: true,
        ws: true,
        logLevel: 'debug'
      },
      // 代理数据质量监控API请求
      '/api/v1/data-quality': {
        target: 'http://localhost:8005',
        changeOrigin: true,
        ws: true,
        logLevel: 'debug'
      },
      // 代理API请求
      '/api/v1': {
        target: 'http://localhost:8000', // 主API服务
        changeOrigin: true,
        ws: true,
        secure: false,
        pathRewrite: {
          '^/api/v1': '/api/v1'
        },
        headers: {
          'Access-Control-Allow-Origin': '*'
        }
      },
      // 健康检查API端点
      '/health': {
        target: 'http://localhost:8000', // 主API服务
        changeOrigin: true,
        pathRewrite: {
          '^/health': '/health'
        }
      },
      // 代理主API服务健康检查
      '/health/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        pathRewrite: {
          '^/health/api': '/health'
        }
      },
      // 代理市场数据API健康检查
      '/health/market': {
        target: 'http://localhost:8005',
        changeOrigin: true,
        pathRewrite: {
          '^/health/market': '/health'
        }
      },
      // 代理回测API健康检查
      '/health/backtest': {
        target: 'http://localhost:8001',
        changeOrigin: true,
        pathRewrite: {
          '^/health/backtest': '/health'
        }
      },
      // 代理交易信号API健康检查
      '/health/signal': {
        target: 'http://localhost:8004',
        changeOrigin: true,
        pathRewrite: {
          '^/health/signal': '/health'
        }
      },
      // 代理风险管理API健康检查
      '/health/risk': {
        target: 'http://localhost:8002',
        changeOrigin: true,
        pathRewrite: {
          '^/health/risk': '/health'
        }
      },
      // 代理市场数据API请求
      '/api/v1/market': {
        target: 'http://localhost:8005',
        changeOrigin: true,
        pathRewrite: {
          '^/api/v1/market': '/api/v1'
        }
      },

      // 代理回测API请求
      '/api/v1/backtest': {
        target: 'http://localhost:8001',
        changeOrigin: true,
        pathRewrite: {
          '^/api/v1/backtest': '/api/v1'
        }
      },
      // 代理交易信号API请求
      '/api/v1/signal': {
        target: 'http://localhost:8004',
        changeOrigin: true,
        pathRewrite: {
          '^/api/v1/signal': '/api/v1'
        }
      },
      // 代理风险管理API请求
      '/api/v1/risk': {
        target: 'http://localhost:8002',
        changeOrigin: true,
        pathRewrite: {
          '^/api/v1/risk': '/api/v1'
        }
      },
      // 代理统一配置API请求
      '/unified-config': {
        target: 'http://localhost:8000/api/v1',
        changeOrigin: true,
        pathRewrite: {
          '^/unified-config': '/unified-config'
        }
      },
      // 代理交易API请求 - 转发到交易API服务
      '/api/v1/trading': {
        target: 'http://localhost:8003',
        changeOrigin: true,
        ws: true,
        secure: false,
        headers: {
          'Access-Control-Allow-Origin': '*'
        }
      },
      // 代理所有API请求
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        ws: true,
        secure: false,
        headers: {
          'Access-Control-Allow-Origin': '*'
        }
      }
    },
    // 历史记录模式
    historyApiFallback: {
      rewrites: [
        {
          from: /.*/,
          to: path.posix.join('/', 'index.html')
        }
      ]
    }
  },

  // 配置webpack
  configureWebpack: {
    // 路径别名配置
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    // 优化配置
    optimization: {
      splitChunks: {
        chunks: 'all'
      }
    },
    devtool: 'source-map'
  },

  css: {
    loaderOptions: {
      sass: {
        prependData: `@import "@/styles/variables.scss";`
      }
    }
  },

  chainWebpack: config => {
    // 设置页面标题
    config.plugin('html').tap(args => {
      args[0].title = '量化交易系统'
      return args
    })
  }
}