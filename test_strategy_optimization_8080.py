#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化功能测试脚本 - 使用8080端口的前端服务
测试通过start-services.ps1启动的前端服务中的策略优化功能
"""

import requests
import json
import time
import sys

def test_frontend_service():
    """测试前端服务是否正常运行"""
    print("检查前端服务状态...")
    try:
        response = requests.get("http://localhost:8080", timeout=10)
        if response.status_code == 200 and "量化交易系统" in response.text:
            print("✓ 前端服务正常运行在8080端口")
            return True
        else:
            print("✗ 前端服务响应异常")
            return False
    except Exception as e:
        print(f"✗ 无法连接到前端服务: {str(e)}")
        print("请确保已运行 .\\start-services.ps1 启动服务")
        return False

def test_strategy_optimization():
    """测试策略优化功能"""
    print("\n测试策略优化功能...")
    
    try:
        # 1. 测试策略列表
        print("  1. 测试策略列表API...")
        response = requests.get("http://localhost:8000/api/v1/strategy", timeout=10)
        if response.status_code == 200:
            strategies = response.json()
            if strategies and len(strategies) > 0:
                print(f"    ✓ 策略列表正常，返回 {len(strategies)} 个策略")
                strategy_id = strategies[0]['id']
                strategy_name = strategies[0]['name']
                print(f"    使用策略: ID={strategy_id}, 名称={strategy_name}")
            else:
                print("    ✗ 策略列表为空")
                return False
        else:
            print(f"    ✗ 策略列表API失败: {response.status_code}")
            return False
        
        # 2. 测试策略优化建议
        print("  2. 测试策略优化建议API...")
        response = requests.get(f"http://localhost:8000/api/v1/strategies/{strategy_id}/optimization", timeout=10)
        if response.status_code == 200:
            optimization = response.json()
            if optimization and 'optimization_suggestions' in optimization:
                suggestions = optimization['optimization_suggestions']
                print(f"    ✓ 优化建议正常，返回 {len(suggestions)} 个建议")
                
                # 显示当前绩效
                if 'current_performance' in optimization:
                    current_perf = optimization['current_performance']
                    print("    当前绩效:")
                    print(f"      年化收益率: {current_perf.get('annualized_return', 'N/A')}%")
                    print(f"      夏普比率: {current_perf.get('sharpe_ratio', 'N/A')}")
                    print(f"      最大回撤: {current_perf.get('max_drawdown', 'N/A')}%")
                
                # 显示优化建议
                for i, suggestion in enumerate(suggestions, 1):
                    print(f"    建议{i}: {suggestion.get('parameter', 'N/A')}")
                    print(f"      当前值: {suggestion.get('current_value', 'N/A')}")
                    print(f"      建议值: {suggestion.get('suggested_value', 'N/A')}")
                    print(f"      理由: {suggestion.get('reason', 'N/A')}")
                    
                    # 显示预期改进
                    if 'expected_improvement' in suggestion:
                        expected = suggestion['expected_improvement']
                        print(f"      预期改进:")
                        print(f"        年化收益率: {expected.get('annualized_return', 'N/A')}%")
                        print(f"        夏普比率: {expected.get('sharpe_ratio', 'N/A')}")
                        print(f"        最大回撤: {expected.get('max_drawdown', 'N/A')}%")
            else:
                print("    ✗ 优化建议为空")
                return False
        else:
            print(f"    ✗ 优化建议API失败: {response.status_code}")
            return False
        
        # 3. 测试应用优化（如果有建议的话）
        if suggestions and len(suggestions) > 0:
            print("  3. 测试应用优化API...")
            suggestion = suggestions[0]
            optimization_params = {
                "parameter": suggestion.get('parameter'),
                "value": suggestion.get('suggested_value')
            }
            
            print(f"    应用优化参数: {optimization_params}")
            
            response = requests.post(
                f"http://localhost:8000/api/v1/strategies/{strategy_id}/optimize",
                json=optimization_params,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                apply_result = response.json()
                if apply_result.get('success'):
                    print(f"    ✓ 应用优化成功: {apply_result.get('message', 'N/A')}")
                    
                    # 显示应用结果
                    print(f"    应用的参数: {apply_result.get('applied_parameter', 'N/A')}")
                    print(f"    应用的值: {apply_result.get('applied_value', 'N/A')}")
                    print(f"    时间戳: {apply_result.get('timestamp', 'N/A')}")
                else:
                    print(f"    ✗ 应用优化失败: {apply_result.get('error', 'N/A')}")
                    return False
            else:
                print(f"    ✗ 应用优化API失败: {response.status_code}")
                print(f"    响应内容: {response.text}")
                return False
        
        print("策略优化功能测试完成")
        return True
        
    except Exception as e:
        print(f"  ✗ 策略优化功能测试失败: {str(e)}")
        return False

def test_frontend_pages():
    """测试前端页面访问"""
    print("\n测试前端页面访问...")
    
    pages = [
        {"name": "首页", "url": "http://localhost:8080/#/"},
        {"name": "策略优化", "url": "http://localhost:8080/#/strategy-optimization"},
        {"name": "策略管理", "url": "http://localhost:8080/#/strategy"},
        {"name": "回测分析", "url": "http://localhost:8080/#/backtest"},
        {"name": "风险管理", "url": "http://localhost:8080/#/risk"}
    ]
    
    success_count = 0
    for page in pages:
        try:
            response = requests.get(page["url"], timeout=10)
            if response.status_code == 200:
                print(f"  ✓ {page['name']}")
                success_count += 1
            else:
                print(f"  ✗ {page['name']} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"  ✗ {page['name']} - {str(e)}")
    
    print(f"前端页面测试完成: {success_count}/{len(pages)} 个页面正常")
    return success_count == len(pages)

def main():
    """主测试流程"""
    print("=" * 80)
    print("           前端功能测试 - 使用8080端口服务")
    print("=" * 80)
    print()
    
    # 检查前端服务
    if not test_frontend_service():
        print("前端服务检查失败，无法继续测试")
        return False
    
    # 执行策略优化功能测试
    optimization_result = test_strategy_optimization()
    
    # 执行前端页面测试
    frontend_result = test_frontend_pages()
    
    # 总结结果
    print()
    print("=" * 80)
    if optimization_result and frontend_result:
        print("                   测试全部通过")
        print("前端服务在8080端口正常工作，所有功能测试通过")
        print("✅ 策略优化功能正常")
        print("✅ 前端页面访问正常")
    else:
        print("                   测试发现问题")
        print("请检查相关服务状态和日志文件")
        if not optimization_result:
            print("❌ 策略优化功能异常")
        if not frontend_result:
            print("❌ 前端页面访问异常")
    print("=" * 80)
    print()
    
    return optimization_result and frontend_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
