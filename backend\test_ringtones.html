<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>铃声测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .ringtone-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .ringtone-info {
            flex: 1;
        }
        .ringtone-name {
            font-weight: bold;
            color: #333;
        }
        .ringtone-details {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .category-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            color: white;
            margin-right: 5px;
        }
        .category-info { background-color: #409eff; }
        .category-warning { background-color: #e6a23c; }
        .category-error { background-color: #f56c6c; }
        .category-success { background-color: #67c23a; }
        .category-system { background-color: #909399; }
        .play-btn {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        .play-btn:hover {
            background-color: #337ecc;
        }
        .play-btn:disabled {
            background-color: #c0c4cc;
            cursor: not-allowed;
        }
        .volume-control {
            margin: 20px 0;
            text-align: center;
        }
        .volume-slider {
            width: 200px;
            margin: 0 10px;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .status.success {
            background-color: #f0f9ff;
            color: #67c23a;
            border: 1px solid #b3d8ff;
        }
        .status.error {
            background-color: #fef0f0;
            color: #f56c6c;
            border: 1px solid #fbc4c4;
        }
        .test-all-btn {
            background-color: #67c23a;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px auto;
            display: block;
        }
        .test-all-btn:hover {
            background-color: #5daf34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 铃声测试页面</h1>
        
        <div class="volume-control">
            <label>音量控制: </label>
            <input type="range" id="volumeSlider" class="volume-slider" min="0" max="100" value="80">
            <span id="volumeValue">80%</span>
        </div>

        <button class="test-all-btn" onclick="testAllRingtones()">🎵 测试所有铃声</button>
        
        <div id="ringtonesList"></div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        const ringtones = [
            {id: "notification-1", name: "默认通知", category: "info", file: "notification-1.wav", duration: 0.5},
            {id: "notification-2", name: "轻柔通知", category: "info", file: "notification-2.wav", duration: 0.8},
            {id: "warning-1", name: "默认警告", category: "warning", file: "warning-1.wav", duration: 1.0},
            {id: "warning-2", name: "紧急警告", category: "warning", file: "warning-2.wav", duration: 1.2},
            {id: "alert-1", name: "默认警报", category: "error", file: "alert-1.wav", duration: 1.5},
            {id: "alert-2", name: "紧急警报", category: "error", file: "alert-2.wav", duration: 2.0},
            {id: "success-1", name: "默认成功", category: "success", file: "success-1.wav", duration: 1.0},
            {id: "system-1", name: "系统通知", category: "system", file: "system-1.wav", duration: 0.8}
        ];

        let currentVolume = 80;

        // 初始化页面
        function init() {
            renderRingtones();
            setupVolumeControl();
        }

        // 渲染铃声列表
        function renderRingtones() {
            const container = document.getElementById('ringtonesList');
            container.innerHTML = ringtones.map(ringtone => `
                <div class="ringtone-item">
                    <div class="ringtone-info">
                        <div class="ringtone-name">${ringtone.name}</div>
                        <div class="ringtone-details">
                            <span class="category-tag category-${ringtone.category}">${getCategoryText(ringtone.category)}</span>
                            文件: ${ringtone.file} | 时长: ${ringtone.duration}秒
                        </div>
                    </div>
                    <button class="play-btn" onclick="playRingtone('${ringtone.id}')">
                        🔊 播放
                    </button>
                </div>
            `).join('');
        }

        // 设置音量控制
        function setupVolumeControl() {
            const slider = document.getElementById('volumeSlider');
            const valueDisplay = document.getElementById('volumeValue');
            
            slider.addEventListener('input', function() {
                currentVolume = this.value;
                valueDisplay.textContent = this.value + '%';
            });
        }

        // 获取分类文本
        function getCategoryText(category) {
            const categoryMap = {
                'info': '信息',
                'warning': '警告',
                'error': '错误',
                'success': '成功',
                'system': '系统'
            };
            return categoryMap[category] || category;
        }

        // 播放铃声
        async function playRingtone(ringtoneId) {
            const button = event.target;
            button.disabled = true;
            button.textContent = '播放中...';
            
            try {
                showStatus('正在播放铃声...', 'success');
                
                // 方法1: 直接播放音频文件
                const audio = new Audio(`http://localhost:8000/static/sounds/${getRingtoneFile(ringtoneId)}`);
                audio.volume = currentVolume / 100;
                await audio.play();
                
                showStatus(`铃声 "${getRingtoneName(ringtoneId)}" 播放成功！`, 'success');
                
            } catch (error) {
                console.error('播放失败:', error);
                
                // 方法2: 尝试使用API
                try {
                    const response = await fetch('http://localhost:8000/api/v1/notifications/ringtones/test-play', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'text/plain'
                        },
                        body: ringtoneId
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                        showStatus(`API播放成功: ${result.message}`, 'success');
                    } else {
                        showStatus(`API播放失败: ${result.message}`, 'error');
                    }
                } catch (apiError) {
                    showStatus(`播放失败: ${error.message}`, 'error');
                }
            } finally {
                setTimeout(() => {
                    button.disabled = false;
                    button.textContent = '🔊 播放';
                }, 1000);
            }
        }

        // 测试所有铃声
        async function testAllRingtones() {
            const button = event.target;
            button.disabled = true;
            button.textContent = '测试中...';
            
            showStatus('开始测试所有铃声...', 'success');
            
            for (let i = 0; i < ringtones.length; i++) {
                const ringtone = ringtones[i];
                showStatus(`正在测试: ${ringtone.name} (${i + 1}/${ringtones.length})`, 'success');
                
                try {
                    const audio = new Audio(`http://localhost:8000/static/sounds/${ringtone.file}`);
                    audio.volume = currentVolume / 100;
                    await audio.play();
                    
                    // 等待音频播放完成
                    await new Promise(resolve => setTimeout(resolve, ringtone.duration * 1000 + 500));
                    
                } catch (error) {
                    console.error(`播放 ${ringtone.name} 失败:`, error);
                }
            }
            
            showStatus('所有铃声测试完成！', 'success');
            button.disabled = false;
            button.textContent = '🎵 测试所有铃声';
        }

        // 显示状态信息
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        // 获取铃声文件名
        function getRingtoneFile(id) {
            const ringtone = ringtones.find(r => r.id === id);
            return ringtone ? ringtone.file : 'notification-1.wav';
        }

        // 获取铃声名称
        function getRingtoneName(id) {
            const ringtone = ringtones.find(r => r.id === id);
            return ringtone ? ringtone.name : '未知铃声';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
