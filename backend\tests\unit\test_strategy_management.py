"""
策略管理系统单元测试
测试策略模板、参数验证、代码生成等核心功能
"""

import unittest
import sys
import os
import json
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from strategy_management.services.strategy_service import StrategyService
from strategy_management.services.template_service import TemplateService
from strategy_management.services.code_generator import CodeGenerator, PineScriptConverter
from strategy_management.services.parameter_validator import ParameterValidator

class TestStrategyTemplates(unittest.TestCase):
    """测试策略模板功能"""
    
    def setUp(self):
        """测试前准备"""
        self.template_service = TemplateService()
        self.code_generator = CodeGenerator()
    
    def test_dual_ma_cross_template(self):
        """测试双均线交叉策略模板"""
        template_id = "dual_ma_cross"
        parameters = {
            "short_period": 5,
            "long_period": 20,
            "price_type": "close",
            "use_stop_loss": True,
            "stop_loss_rate": 0.02
        }
        
        # 测试模板代码生成
        result = self.code_generator.generate_from_template(template_id, parameters)
        
        self.assertTrue(result["success"])
        self.assertIn("short_period = 5", result["code"])
        self.assertIn("long_period = 20", result["code"])
        self.assertIn("def handle_bar", result["code"])
    
    def test_rsi_oversold_template(self):
        """测试RSI超买超卖策略模板"""
        template_id = "rsi_oversold"
        parameters = {
            "rsi_period": 14,
            "overbought_level": 70.0,
            "oversold_level": 30.0
        }
        
        # 测试模板代码生成
        result = self.code_generator.generate_from_template(template_id, parameters)
        
        self.assertTrue(result["success"])
        self.assertIn("rsi_period = 14", result["code"])
        self.assertIn("overbought_level = 70.0", result["code"])
        self.assertIn("oversold_level = 30.0", result["code"])
    
    def test_invalid_template(self):
        """测试无效模板处理"""
        template_id = "invalid_template"
        parameters = {}
        
        result = self.code_generator.generate_from_template(template_id, parameters)
        
        self.assertFalse(result["success"])
        self.assertIn("模板不存在", result["message"])

class TestParameterValidation(unittest.TestCase):
    """测试参数验证功能"""
    
    def setUp(self):
        """测试前准备"""
        self.validator = ParameterValidator()
    
    def test_valid_parameters(self):
        """测试有效参数验证"""
        parameters = {
            "short_period": 5,
            "long_period": 20,
            "price_type": "close"
        }
        
        schema = {
            "short_period": {"type": "integer", "min": 1, "max": 50},
            "long_period": {"type": "integer", "min": 2, "max": 200},
            "price_type": {"type": "enum", "options": ["open", "high", "low", "close"]}
        }
        
        result = self.validator.validate_parameters(parameters, schema)
        
        self.assertTrue(result["valid"])
        self.assertEqual(len(result["errors"]), 0)
    
    def test_invalid_parameters(self):
        """测试无效参数验证"""
        parameters = {
            "short_period": 0,  # 无效：小于最小值
            "long_period": 300,  # 无效：大于最大值
            "price_type": "invalid"  # 无效：不在选项中
        }
        
        schema = {
            "short_period": {"type": "integer", "min": 1, "max": 50},
            "long_period": {"type": "integer", "min": 2, "max": 200},
            "price_type": {"type": "enum", "options": ["open", "high", "low", "close"]}
        }
        
        result = self.validator.validate_parameters(parameters, schema)
        
        self.assertFalse(result["valid"])
        self.assertGreater(len(result["errors"]), 0)
    
    def test_missing_required_parameters(self):
        """测试缺少必需参数"""
        parameters = {
            "short_period": 5
            # 缺少 long_period
        }
        
        schema = {
            "short_period": {"type": "integer", "required": True},
            "long_period": {"type": "integer", "required": True}
        }
        
        result = self.validator.validate_parameters(parameters, schema)
        
        self.assertFalse(result["valid"])
        self.assertIn("long_period", str(result["errors"]))

class TestPineScriptConverter(unittest.TestCase):
    """测试Pine Script转换功能"""
    
    def setUp(self):
        """测试前准备"""
        self.converter = CodeGenerator()
    
    def test_simple_pine_script_conversion(self):
        """测试简单Pine Script转换"""
        pine_code = '''
//@version=5
strategy("Simple MA Strategy", overlay=true)

length = input.int(20, title="Length")
ma = ta.sma(close, length)

if ta.crossover(close, ma)
    strategy.entry("Long", strategy.long)

if ta.crossunder(close, ma)
    strategy.close("Long")
'''
        
        result = self.converter.convert_to_python(pine_code)
        
        self.assertTrue(result["success"])
        self.assertIn("import pandas as pd", result["converted_code"])
        self.assertIn("def strategy_logic", result["converted_code"])
        self.assertIn("length = ", result["converted_code"])
    
    def test_pine_script_compatibility_check(self):
        """测试Pine Script兼容性检查"""
        pine_code = '''
//@version=5
strategy("Test Strategy", overlay=true)

length = input.int(20)
ma = ta.sma(close, length)

if ta.crossover(close, ma)
    strategy.entry("Long", strategy.long)
'''
        
        result = self.converter.check_compatibility(pine_code)
        
        self.assertIn("score", result)
        self.assertIn("warnings", result)
        self.assertIn("recommendations", result)
        self.assertGreaterEqual(result["score"], 0.0)
        self.assertLessEqual(result["score"], 1.0)
    
    def test_unsupported_features_detection(self):
        """测试不支持功能检测"""
        pine_code = '''
//@version=5
strategy("Advanced Strategy", overlay=true)

import mylib as lib
matrix_data = matrix.new<float>(10, 10)
table_data = table.new(position.top_right, 2, 2)
'''
        
        result = self.converter.check_compatibility(pine_code)
        
        self.assertLess(result["score"], 0.8)  # 应该有较低的兼容性分数
        self.assertGreater(len(result["unsupported_features"]), 0)

class TestCodeGeneration(unittest.TestCase):
    """测试代码生成功能"""
    
    def setUp(self):
        """测试前准备"""
        self.code_generator = CodeGenerator()
    
    def test_template_parameter_injection(self):
        """测试模板参数注入"""
        template_code = '''
def strategy_logic(data, params):
    short_period = {{ short_period | default(5) }}
    long_period = {{ long_period | default(20) }}
    
    short_ma = data['close'].rolling(short_period).mean()
    long_ma = data['close'].rolling(long_period).mean()
    
    return signals
'''
        
        parameters = {
            "short_period": 10,
            "long_period": 30
        }
        
        result = self.code_generator.inject_parameters(template_code, parameters)
        
        self.assertIn("short_period = 10", result)
        self.assertIn("long_period = 30", result)
        self.assertNotIn("{{", result)  # 确保所有模板变量都被替换
    
    def test_code_validation(self):
        """测试代码验证"""
        valid_python_code = '''
def strategy_logic(data, params):
    return []
'''
        
        invalid_python_code = '''
def strategy_logic(data, params)  # 缺少冒号
    return []
'''
        
        # 测试有效代码
        result_valid = self.code_generator.validate_code(valid_python_code, "python")
        self.assertTrue(result_valid["valid"])
        
        # 测试无效代码
        result_invalid = self.code_generator.validate_code(invalid_python_code, "python")
        self.assertFalse(result_invalid["valid"])

class TestStrategyService(unittest.TestCase):
    """测试策略服务功能"""
    
    def setUp(self):
        """测试前准备"""
        # 模拟数据库会话
        self.mock_db = Mock()
        self.strategy_service = StrategyService(self.mock_db)
    
    @patch('strategy_management.services.strategy_service.Strategy')
    def test_create_strategy(self, mock_strategy_model):
        """测试策略创建"""
        # 模拟策略数据
        strategy_data = {
            "name": "测试策略",
            "type": "trend_following",
            "category": "dual_ma_cross",
            "description": "测试用双均线策略",
            "code_type": "python",
            "parameters": {"short_period": 5, "long_period": 20}
        }
        
        # 模拟数据库操作
        mock_strategy_instance = Mock()
        mock_strategy_instance.id = 1
        mock_strategy_model.return_value = mock_strategy_instance
        
        # 执行测试
        result = self.strategy_service.create_strategy(strategy_data, user_id=1)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()

if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
