#!/usr/bin/env python3
"""
测试性能优化API端点
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

# 测试用户认证信息
AUTH_DATA = {
    "username": "admin",
    "password": "admin123"
}

def get_auth_token():
    """获取认证token"""
    try:
        # 尝试JSON格式
        response = requests.post(f"{BASE_URL}/auth/token", json=AUTH_DATA)
        if response.status_code == 200:
            data = response.json()
            return data.get("access_token")

        # 如果JSON失败，尝试表单数据格式
        print(f"JSON认证失败: {response.status_code}, 尝试表单数据格式")
        response = requests.post(f"{BASE_URL}/auth/token", data=AUTH_DATA)
        if response.status_code == 200:
            data = response.json()
            return data.get("access_token")
        else:
            print(f"认证失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"认证请求失败: {e}")
        return None

def test_api_endpoint(endpoint, method="GET", data=None, headers=None):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"

    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, json=data, headers=headers)
        else:
            print(f"不支持的HTTP方法: {method}")
            return False

        print(f"{method} {endpoint}: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get("success"):
                    print(f"  ✓ 成功: {result.get('message', 'OK')}")
                    return True
                else:
                    print(f"  ✗ 失败: {result.get('message', 'Unknown error')}")
                    return False
            except:
                print(f"  ✓ 响应成功但非JSON格式")
                return True
        else:
            print(f"  ✗ HTTP错误: {response.text[:100]}")
            return False

    except Exception as e:
        print(f"  ✗ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试性能优化API端点...")

    # 获取认证token
    token = get_auth_token()
    if not token:
        print("无法获取认证token，退出测试")
        return

    headers = {"Authorization": f"Bearer {token}"}

    # 测试的API端点列表
    test_endpoints = [
        # 性能摘要和分析
        ("/performance/summary", "GET"),
        ("/performance/analysis", "GET"),

        # 缓存相关
        ("/performance/cache/stats", "GET"),
        ("/performance/cache/config", "POST", {"maxSize": 1000, "defaultTtl": 300}),
        ("/performance/cache/clear", "POST"),

        # API性能
        ("/performance/api/stats", "GET"),
        ("/performance/api/endpoints", "GET"),

        # 大数据处理
        ("/performance/process-large-dataframe", "GET"),

        # 内存监控
        ("/performance/memory/usage", "GET"),
        ("/performance/memory/analysis", "GET"),
    ]

    success_count = 0
    total_count = len(test_endpoints)

    print(f"\n测试 {total_count} 个API端点:")
    print("-" * 50)

    for endpoint_info in test_endpoints:
        if len(endpoint_info) == 2:
            endpoint, method = endpoint_info
            data = None
        else:
            endpoint, method, data = endpoint_info

        if test_api_endpoint(endpoint, method, data, headers):
            success_count += 1
        print()

    print("-" * 50)
    print(f"测试完成: {success_count}/{total_count} 个端点成功")

    if success_count == total_count:
        print("✓ 所有API端点测试通过！")
    else:
        print(f"✗ {total_count - success_count} 个端点测试失败")

if __name__ == "__main__":
    main()
