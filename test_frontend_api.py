#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端API调用测试脚本
测试前端通过代理访问后端API的情况
"""

import requests
import json
import time

def test_frontend_api():
    """测试前端API调用"""
    print("=" * 60)
    print("前端API调用测试")
    print("=" * 60)
    
    # 前端代理地址
    frontend_url = "http://localhost:8080"
    
    # 等待服务启动
    print("等待前端服务启动...")
    time.sleep(2)
    
    # 测试1: 通过前端代理访问策略API
    print("\n1. 测试前端代理策略API...")
    try:
        response = requests.get(f"{frontend_url}/api/v1/strategy", timeout=10)
        if response.status_code == 200:
            strategies = response.json()
            print(f"✓ 前端代理策略API正常，返回 {len(strategies)} 个策略")
            
            for strategy in strategies:
                print(f"  - ID: {strategy['id']}, 名称: {strategy['name']}, 状态: {strategy['status']}")
        else:
            print(f"✗ 前端代理策略API失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"✗ 前端代理策略API异常: {e}")
        return False
    
    # 测试2: 测试CORS设置
    print("\n2. 测试CORS设置...")
    try:
        headers = {
            'Origin': 'http://localhost:8080',
            'Content-Type': 'application/json'
        }
        response = requests.get(f"{frontend_url}/api/v1/strategy", headers=headers, timeout=10)
        if response.status_code == 200:
            print("✓ CORS设置正常")
        else:
            print(f"✗ CORS设置可能有问题: {response.status_code}")
    except Exception as e:
        print(f"✗ CORS测试异常: {e}")
    
    # 测试3: 测试策略统计API
    print("\n3. 测试策略统计API...")
    try:
        response = requests.get(f"{frontend_url}/api/v1/strategy/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print(f"✓ 策略统计API正常")
            print(f"  总策略数: {stats.get('total', 'N/A')}")
            print(f"  活跃策略: {stats.get('active', 'N/A')}")
            print(f"  非活跃策略: {stats.get('inactive', 'N/A')}")
        else:
            print(f"✗ 策略统计API失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 策略统计API异常: {e}")
    
    # 测试4: 测试健康检查
    print("\n4. 测试健康检查...")
    try:
        response = requests.get(f"{frontend_url}/health", timeout=10)
        if response.status_code == 200:
            print("✓ 健康检查正常")
        else:
            print(f"✗ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 健康检查异常: {e}")
    
    print("\n" + "=" * 60)
    print("✓ 前端API调用测试完成！")
    print("✓ 前端代理功能正常工作")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_frontend_api()
