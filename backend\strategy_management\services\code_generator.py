"""
策略代码生成器
负责从模板和参数生成策略代码
"""

from typing import Dict, Any, Optional
from jinja2 import Template, Environment, BaseLoader, TemplateError
import logging
import re

logger = logging.getLogger(__name__)

class CodeGenerator:
    """策略代码生成器"""

    def __init__(self):
        self.env = Environment(loader=BaseLoader())
        # 添加自定义过滤器
        self.env.filters['default'] = self._default_filter
        self.env.filters['format_float'] = self._format_float_filter
        self.env.filters['format_int'] = self._format_int_filter

    def generate_code(self, template_code: str, parameters: Dict[str, Any]) -> str:
        """从模板和参数生成策略代码"""
        try:
            # 创建Jinja2模板
            template = self.env.from_string(template_code)

            # 渲染模板
            generated_code = template.render(**parameters)

            # 格式化代码
            formatted_code = self._format_code(generated_code)

            logger.info("成功生成策略代码")
            return formatted_code

        except TemplateError as e:
            logger.error(f"模板渲染失败: {str(e)}")
            raise ValueError(f"模板渲染失败: {str(e)}")
        except Exception as e:
            logger.error(f"代码生成失败: {str(e)}")
            raise ValueError(f"代码生成失败: {str(e)}")

    def validate_template(self, template_code: str) -> Dict[str, Any]:
        """验证模板语法"""
        try:
            # 尝试解析模板
            template = self.env.from_string(template_code)

            # 提取模板变量
            variables = self._extract_template_variables(template_code)

            return {
                "valid": True,
                "variables": variables,
                "message": "模板语法正确"
            }

        except TemplateError as e:
            return {
                "valid": False,
                "variables": [],
                "message": f"模板语法错误: {str(e)}"
            }

    def _extract_template_variables(self, template_code: str) -> list:
        """提取模板中的变量"""
        # 使用正则表达式提取 {{ variable }} 格式的变量
        pattern = r'\{\{\s*([^}|]+?)(?:\s*\|\s*[^}]+)?\s*\}\}'
        matches = re.findall(pattern, template_code)

        # 清理变量名
        variables = []
        for match in matches:
            var_name = match.strip()
            if var_name and var_name not in variables:
                variables.append(var_name)

        return variables

    def _format_code(self, code: str) -> str:
        """格式化生成的代码"""
        # 移除多余的空行
        lines = code.split('\n')
        formatted_lines = []
        prev_empty = False

        for line in lines:
            stripped = line.strip()
            if not stripped:
                if not prev_empty:
                    formatted_lines.append('')
                prev_empty = True
            else:
                formatted_lines.append(line)
                prev_empty = False

        # 移除开头和结尾的空行
        while formatted_lines and not formatted_lines[0].strip():
            formatted_lines.pop(0)
        while formatted_lines and not formatted_lines[-1].strip():
            formatted_lines.pop()

        return '\n'.join(formatted_lines)

    def _default_filter(self, value, default_value):
        """默认值过滤器"""
        return value if value is not None else default_value

    def _format_float_filter(self, value, precision=2):
        """浮点数格式化过滤器"""
        try:
            return f"{float(value):.{precision}f}"
        except (ValueError, TypeError):
            return str(value)

    def _format_int_filter(self, value):
        """整数格式化过滤器"""
        try:
            return str(int(value))
        except (ValueError, TypeError):
            return str(value)

class PineScriptConverter:
    """Pine Script转换器"""

    def __init__(self):
        # Pine Script到Python的函数映射
        self.function_mapping = {
            'ta.sma': 'calculate_sma',
            'ta.ema': 'calculate_ema',
            'ta.rsi': 'calculate_rsi',
            'ta.macd': 'calculate_macd',
            'ta.stoch': 'calculate_stoch',
            'ta.atr': 'calculate_atr',
            'ta.crossover': 'crossover',
            'ta.crossunder': 'crossunder',
            'strategy.entry': 'generate_entry_signal',
            'strategy.close': 'generate_close_signal',
            'strategy.exit': 'generate_exit_signal'
        }

        # Pine Script变量到Python的映射
        self.variable_mapping = {
            'close': 'data["close"]',
            'open': 'data["open"]',
            'high': 'data["high"]',
            'low': 'data["low"]',
            'volume': 'data["volume"]',
            'strategy.long': 'LONG',
            'strategy.short': 'SHORT'
        }

    def convert_to_python(self, pine_code: str) -> Dict[str, Any]:
        """将Pine Script代码转换为Python代码"""
        try:
            # 首先进行兼容性检查
            compatibility_result = self.check_compatibility(pine_code)

            # 解析Pine Script代码
            parsed_code = self._parse_pine_script(pine_code)

            # 转换为Python代码
            python_code = self._generate_python_code(parsed_code)

            return {
                "success": True,
                "converted_code": python_code,
                "message": "转换成功",
                "warnings": compatibility_result.get("warnings", []),
                "compatibility_score": compatibility_result.get("score", 1.0)
            }

        except Exception as e:
            logger.error(f"Pine Script转换失败: {str(e)}")
            return {
                "success": False,
                "converted_code": None,
                "message": f"转换失败: {str(e)}",
                "errors": [str(e)]
            }

    def check_compatibility(self, pine_code: str) -> Dict[str, Any]:
        """检查Pine Script代码的兼容性"""
        warnings = []
        unsupported_features = []
        score = 1.0

        lines = pine_code.strip().split('\n')

        # 检查版本兼容性
        version_found = False
        for line in lines:
            if line.strip().startswith('//@version='):
                version = line.split('=')[1].strip()
                version_found = True
                if int(version) < 4:
                    warnings.append(f"Pine Script版本{version}可能存在兼容性问题，建议使用v4或v5")
                    score -= 0.1
                break

        if not version_found:
            warnings.append("未找到版本声明，建议添加 //@version=5")
            score -= 0.1

        # 检查不支持的功能
        unsupported_patterns = [
            (r'request\.security_lower_tf', "request.security_lower_tf函数暂不支持"),
            (r'matrix\.\w+', "矩阵操作暂不支持"),
            (r'map\.\w+', "映射操作暂不支持"),
            (r'polyline\.\w+', "多边形绘制暂不支持"),
            (r'table\.\w+', "表格功能暂不支持"),
            (r'import\s+\w+', "导入外部库暂不支持"),
            (r'export\s+\w+', "导出功能暂不支持"),
            (r'method\s+\w+', "自定义方法暂不支持"),
            (r'type\s+\w+', "自定义类型暂不支持")
        ]

        for pattern, message in unsupported_patterns:
            if re.search(pattern, pine_code):
                unsupported_features.append(message)
                score -= 0.2

        # 检查复杂的条件语句
        complex_conditions = re.findall(r'if\s+.*?(?:\n|$)', pine_code, re.MULTILINE)
        if len(complex_conditions) > 10:
            warnings.append("检测到大量条件语句，转换后的Python代码可能较复杂")
            score -= 0.1

        # 检查策略函数使用
        strategy_functions = re.findall(r'strategy\.\w+', pine_code)
        if len(strategy_functions) > 20:
            warnings.append("检测到大量策略函数调用，请确保转换后的逻辑正确")
            score -= 0.1

        # 确保分数不低于0
        score = max(0.0, score)

        return {
            "score": score,
            "warnings": warnings,
            "unsupported_features": unsupported_features,
            "recommendations": self._get_compatibility_recommendations(score, warnings, unsupported_features)
        }

    def _get_compatibility_recommendations(self, score: float, warnings: list, unsupported_features: list) -> list:
        """获取兼容性建议"""
        recommendations = []

        if score < 0.5:
            recommendations.append("代码兼容性较低，建议简化Pine Script代码或手动编写Python版本")
        elif score < 0.8:
            recommendations.append("代码存在一些兼容性问题，建议检查转换后的代码并进行测试")
        else:
            recommendations.append("代码兼容性良好，转换后应该能正常工作")

        if unsupported_features:
            recommendations.append("请移除或替换不支持的功能，或考虑使用纯Python实现")

        if len(warnings) > 5:
            recommendations.append("建议优化Pine Script代码结构，减少复杂性")

        return recommendations

    def _parse_pine_script(self, pine_code: str) -> Dict[str, Any]:
        """解析Pine Script代码"""
        # 简化的解析器，实际项目中需要更完整的实现
        lines = pine_code.strip().split('\n')

        parsed = {
            "version": None,
            "strategy_name": None,
            "variables": [],
            "indicators": [],
            "conditions": [],
            "actions": []
        }

        for line in lines:
            line = line.strip()
            if not line or line.startswith('//'):
                continue

            # 解析版本
            if line.startswith('//@version='):
                parsed["version"] = line.split('=')[1]

            # 解析策略声明
            elif line.startswith('strategy('):
                match = re.search(r'strategy\("([^"]+)"', line)
                if match:
                    parsed["strategy_name"] = match.group(1)

            # 解析变量定义
            elif '=' in line and not line.startswith('if'):
                parts = line.split('=', 1)
                var_name = parts[0].strip()
                var_value = parts[1].strip()
                parsed["variables"].append({
                    "name": var_name,
                    "value": var_value
                })

            # 解析条件和动作
            elif line.startswith('if '):
                parsed["conditions"].append(line)
            elif any(action in line for action in ['strategy.entry', 'strategy.close', 'strategy.exit']):
                parsed["actions"].append(line)

        return parsed

    def _generate_python_code(self, parsed_code: Dict[str, Any]) -> str:
        """生成Python代码"""
        python_lines = []

        # 添加文档字符串
        if parsed_code["strategy_name"]:
            python_lines.append(f'"""')
            python_lines.append(f'{parsed_code["strategy_name"]}')
            python_lines.append(f'从Pine Script转换而来')
            python_lines.append(f'"""')
            python_lines.append('')

        # 添加导入
        python_lines.append('import pandas as pd')
        python_lines.append('import numpy as np')
        python_lines.append('from typing import Dict, Any, List')
        python_lines.append('')

        # 添加策略函数
        python_lines.append('def strategy_logic(data: pd.DataFrame, params: Dict[str, Any]) -> List[Dict[str, Any]]:')
        python_lines.append('    """策略逻辑函数"""')
        python_lines.append('    signals = []')
        python_lines.append('')

        # 转换变量定义
        for var in parsed_code["variables"]:
            python_var = self._convert_variable(var["name"], var["value"])
            python_lines.append(f'    {python_var}')

        python_lines.append('')

        # 转换条件和动作
        python_lines.append('    # 遍历数据生成信号')
        python_lines.append('    for i in range(1, len(data)):')

        for condition in parsed_code["conditions"]:
            python_condition = self._convert_condition(condition)
            python_lines.append(f'        {python_condition}')

        for action in parsed_code["actions"]:
            python_action = self._convert_action(action)
            python_lines.append(f'            {python_action}')

        python_lines.append('')
        python_lines.append('    return signals')

        return '\n'.join(python_lines)

    def _convert_variable(self, name: str, value: str) -> str:
        """转换变量定义"""
        # 转换Pine Script变量到Python
        for pine_var, python_var in self.variable_mapping.items():
            value = value.replace(pine_var, python_var)

        return f'{name} = {value}'

    def _convert_condition(self, condition: str) -> str:
        """转换条件语句"""
        # 简化的条件转换
        python_condition = condition.replace('if ', 'if ')

        # 转换函数调用
        for pine_func, python_func in self.function_mapping.items():
            python_condition = python_condition.replace(pine_func, python_func)

        # 转换变量
        for pine_var, python_var in self.variable_mapping.items():
            python_condition = python_condition.replace(pine_var, python_var)

        return python_condition + ':'

    def _convert_action(self, action: str) -> str:
        """转换动作语句"""
        # 简化的动作转换
        if 'strategy.entry' in action:
            return 'signals.append({"action": "BUY", "price": data["close"].iloc[i]})'
        elif 'strategy.close' in action:
            return 'signals.append({"action": "SELL", "price": data["close"].iloc[i]})'
        elif 'strategy.exit' in action:
            return 'signals.append({"action": "EXIT", "price": data["close"].iloc[i]})'

        return f'# {action}'
