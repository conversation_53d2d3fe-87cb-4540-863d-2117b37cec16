<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vuex修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>通知管理系统 Vuex 修复验证</h1>
        
        <div class="test-section info">
            <h3>测试目的</h3>
            <p>验证以下Vuex错误是否已修复：</p>
            <ul>
                <li><code>[vuex] unknown action type: notification/getImportantEventTypes</code></li>
                <li><code>[vuex] module namespace not found in mapGetters(): user/</code></li>
            </ul>
        </div>

        <div class="test-section">
            <h3>API端点测试</h3>
            <button onclick="testImportantEventTypes()">测试重要事件类型API</button>
            <button onclick="testImportantEvents()">测试重要事件列表API</button>
            <div id="api-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>前端页面测试</h3>
            <button onclick="openImportantEventsPage()">打开重要事件管理页面</button>
            <button onclick="checkConsoleErrors()">检查控制台错误</button>
            <div id="frontend-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>修复状态总结</h3>
            <div id="summary" class="result info">
                <strong>修复内容：</strong><br>
                1. ✅ 在notification模块中添加了getImportantEventTypes action<br>
                2. ✅ 在主store中注册了user模块<br>
                3. ✅ 在主store中添加了userInfo getter<br>
                4. ✅ 在notification API中添加了getImportantEventTypes方法<br>
                5. ✅ 后端API端点正常工作，返回真实数据
            </div>
        </div>
    </div>

    <script>
        async function testImportantEventTypes() {
            const resultDiv = document.getElementById('api-result');
            try {
                const response = await fetch('/api/v1/notifications/important-event-types');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 重要事件类型API测试成功\n返回数据: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ API返回错误: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        async function testImportantEvents() {
            const resultDiv = document.getElementById('api-result');
            try {
                const response = await fetch('/api/v1/notifications/important-events');
                const data = await response.json();
                
                if (response.ok && data.items) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 重要事件列表API测试成功\n返回${data.items.length}条真实数据`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ API返回错误: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        function openImportantEventsPage() {
            const resultDiv = document.getElementById('frontend-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在打开重要事件管理页面，请检查浏览器控制台是否还有Vuex错误...';
            
            // 在新标签页中打开
            window.open('/notification/important-events', '_blank');
            
            setTimeout(() => {
                resultDiv.textContent += '\n\n请在新打开的页面中按F12查看控制台，确认是否还有以下错误：\n- [vuex] unknown action type: notification/getImportantEventTypes\n- [vuex] module namespace not found in mapGetters(): user/';
            }, 1000);
        }

        function checkConsoleErrors() {
            const resultDiv = document.getElementById('frontend-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '请手动检查浏览器控制台：\n\n1. 按F12打开开发者工具\n2. 切换到Console标签\n3. 刷新重要事件管理页面\n4. 查看是否还有Vuex相关错误\n\n如果没有错误，说明修复成功！';
        }

        // 页面加载时自动运行基础测试
        window.onload = function() {
            console.log('Vuex修复验证页面已加载');
            console.log('开始自动测试API端点...');
            testImportantEventTypes();
        };
    </script>
</body>
</html>
