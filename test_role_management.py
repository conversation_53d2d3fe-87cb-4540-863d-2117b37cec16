#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
角色管理功能验证测试脚本
"""

import requests
import json

def test_role_management():
    """测试角色管理功能"""
    print("=" * 60)
    print("🎯 角色管理功能验证测试")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    try:
        print("1. 测试获取权限列表...")
        response = requests.get(f"{base_url}/account/permissions")
        if response.status_code == 200:
            data = response.json()
            print("✅ 权限列表获取成功")
            print(f"   - 总权限数: {data['data']['total']}")
            print(f"   - 权限类别数: {len(data['data']['categories'])}")
            
            # 显示权限类别
            for category in data['data']['categories']:
                print(f"   - {category['display_name']}: {category['permission_count']} 个权限")
        else:
            print(f"❌ 权限列表获取失败: {response.status_code}")
            return False
        
        print("\n2. 测试获取角色列表...")
        response = requests.get(f"{base_url}/account/roles")
        if response.status_code == 200:
            data = response.json()
            print("✅ 角色列表获取成功")
            print(f"   - 总角色数: {data['data']['total']}")
            
            roles = data['data']['roles']
            for role in roles:
                print(f"   - 角色: {role['name']} - {role['description']}")
                print(f"     权限数: {len(role['permissions'])}")
        else:
            print(f"❌ 角色列表获取失败: {response.status_code}")
            return False
        
        print("\n3. 测试获取角色权限...")
        # 测试获取admin角色的权限
        admin_role_id = 1
        response = requests.get(f"{base_url}/account/roles/{admin_role_id}/permissions")
        if response.status_code == 200:
            data = response.json()
            print("✅ 角色权限获取成功")
            print(f"   - 角色: {data['data']['role_name']}")
            print(f"   - 权限数: {len(data['data']['permissions'])}")
            
            for permission in data['data']['permissions']:
                print(f"     - {permission['name']}: {permission['description']}")
        else:
            print(f"❌ 角色权限获取失败: {response.status_code}")
            return False
        
        print("\n4. 测试创建新角色...")
        new_role_data = {
            "name": "test_role",
            "description": "测试角色",
            "permissions": ["data_view", "strategy_view"],
            "is_active": True
        }
        
        response = requests.post(f"{base_url}/account/roles", json=new_role_data)
        if response.status_code == 201:
            data = response.json()
            print("✅ 新角色创建成功")
            print(f"   - 角色ID: {data['data']['id']}")
            print(f"   - 角色名: {data['data']['name']}")
            print(f"   - 权限: {data['data']['permissions']}")
            test_role_id = data['data']['id']
        else:
            print(f"❌ 新角色创建失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
        
        print("\n5. 测试更新角色权限...")
        update_permissions_data = {
            "permissions": ["data_view", "strategy_view", "backtest_management"]
        }
        
        response = requests.put(f"{base_url}/account/roles/{test_role_id}/permissions", 
                              json=update_permissions_data)
        if response.status_code == 200:
            data = response.json()
            print("✅ 角色权限更新成功")
            print(f"   - 更新后权限: {data['data']['permissions']}")
        else:
            print(f"❌ 角色权限更新失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
        
        print("\n6. 测试删除角色...")
        response = requests.delete(f"{base_url}/account/roles/{test_role_id}")
        if response.status_code == 200:
            data = response.json()
            print("✅ 角色删除成功")
            print(f"   - 删除消息: {data['message']}")
        else:
            print(f"❌ 角色删除失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
        
        print("\n7. 验证角色已被删除...")
        response = requests.get(f"{base_url}/account/roles/{test_role_id}")
        if response.status_code == 404:
            print("✅ 角色删除验证成功 - 角色不存在")
        else:
            print(f"❌ 角色删除验证失败: {response.status_code}")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 角色管理功能验证测试通过！")
        print("=" * 60)
        print("✅ 权限列表获取正常")
        print("✅ 角色列表获取正常")
        print("✅ 角色权限获取正常")
        print("✅ 角色创建功能正常")
        print("✅ 角色权限更新正常")
        print("✅ 角色删除功能正常")
        print("\n🚀 角色管理系统完全正常工作！")
        print("🎊 前端角色管理页面应该能正常使用所有功能！")
        print("=" * 60)
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 无法连接到后端服务")
        print("请确保后端服务正在运行在端口8000")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_frontend_compatibility():
    """测试前端兼容性"""
    print("\n" + "=" * 60)
    print("🔧 前端兼容性测试")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    frontend_url = "http://localhost:8080"
    
    try:
        # 检查前端服务
        print("1. 检查前端服务...")
        response = requests.get(frontend_url, timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常运行")
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
        
        # 检查API数据格式
        print("2. 检查API数据格式...")
        response = requests.get(f"{base_url}/account/permissions")
        if response.status_code == 200:
            data = response.json()
            # 检查前端期望的数据结构
            if 'data' in data and 'permissions' in data['data'] and 'categories' in data['data']:
                print("✅ 权限API数据格式正确")
                print("   前端应该使用: response.data.permissions")
                print("   前端应该使用: response.data.categories")
            else:
                print("❌ 权限API数据格式异常")
                return False
        
        response = requests.get(f"{base_url}/account/roles")
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and 'roles' in data['data']:
                print("✅ 角色API数据格式正确")
                print("   前端应该使用: response.data.roles")
            else:
                print("❌ 角色API数据格式异常")
                return False
        
        print("\n🎉 前端兼容性测试通过！")
        print("✅ 所有API数据格式符合前端期望")
        print("✅ 前端角色管理页面应该能正常显示数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端兼容性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始角色管理功能验证...")
    
    # 进行角色管理功能测试
    if test_role_management():
        # 进行前端兼容性测试
        test_frontend_compatibility()
    else:
        print("角色管理功能测试失败，请检查后端服务！")
