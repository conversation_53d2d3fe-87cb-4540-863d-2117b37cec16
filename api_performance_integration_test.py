#!/usr/bin/env python3
"""
API性能监控集成测试
验证前端和后端的完整集成
"""

import requests
import json
import time
from datetime import datetime

def test_api_performance_integration():
    """测试API性能监控的完整集成"""
    print("🎯 API性能监控集成测试...")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 1. 获取认证令牌
    print("\n1. 🔐 获取认证令牌...")
    auth_response = requests.post(
        f"{base_url}/api/v1/auth/token",
        json={"username": "admin", "password": "admin123"}
    )
    
    if auth_response.status_code != 200:
        print(f"❌ 认证失败: {auth_response.status_code}")
        return False
    
    token = auth_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 认证成功")
    
    # 2. 测试API统计端点
    print("\n2. 📊 测试API统计端点...")
    stats_response = requests.get(f"{base_url}/api/v1/performance/api/stats", headers=headers)
    
    if stats_response.status_code != 200:
        print(f"❌ API统计获取失败: {stats_response.status_code}")
        return False
    
    stats_data = stats_response.json()
    print("✅ API统计获取成功")
    print(f"   数据结构: {json.dumps(stats_data, indent=2, ensure_ascii=False)}")
    
    # 验证数据结构
    required_fields = ["avg", "count", "max", "min", "recent", "endpoints"]
    data = stats_data.get("data", {})
    
    for field in required_fields:
        if field not in data:
            print(f"❌ 缺少必需字段: {field}")
            return False
        print(f"   ✅ {field}: {data[field]}")
    
    # 3. 测试API趋势数据
    print("\n3. 📈 测试API趋势数据...")
    trend_response = requests.get(f"{base_url}/api/v1/performance/metrics/api?limit=10", headers=headers)
    
    if trend_response.status_code != 200:
        print(f"❌ API趋势数据获取失败: {trend_response.status_code}")
        return False
    
    trend_data = trend_response.json()
    print("✅ API趋势数据获取成功")
    print(f"   数据条数: {len(trend_data.get('data', []))}")
    
    # 4. 测试API端点列表
    print("\n4. 🔗 测试API端点列表...")
    endpoints_response = requests.get(f"{base_url}/api/v1/performance/api/endpoints", headers=headers)
    
    if endpoints_response.status_code != 200:
        print(f"❌ API端点列表获取失败: {endpoints_response.status_code}")
        return False
    
    endpoints_data = endpoints_response.json()
    print("✅ API端点列表获取成功")
    print(f"   端点数量: {len(endpoints_data.get('data', []))}")
    
    # 5. 执行性能测试
    print("\n5. ⚡ 执行API性能测试...")
    test_payload = {
        "endpoint": "/api/v1/performance/summary",
        "users": 5,
        "duration": "10s"
    }
    
    test_response = requests.post(
        f"{base_url}/api/v1/performance/api/test",
        headers=headers,
        json=test_payload
    )
    
    if test_response.status_code != 200:
        print(f"❌ API性能测试失败: {test_response.status_code}")
        return False
    
    test_result = test_response.json()
    print("✅ API性能测试完成")
    print(f"   平均响应时间: {test_result['data']['avg_response_time']:.3f}s")
    print(f"   请求总数: {test_result['data']['total_requests']}")
    print(f"   成功率: {(1 - test_result['data']['error_rate']) * 100:.1f}%")
    
    # 6. 验证数据更新
    print("\n6. 🔄 验证数据更新...")
    time.sleep(2)  # 等待数据更新
    
    updated_stats_response = requests.get(f"{base_url}/api/v1/performance/api/stats", headers=headers)
    updated_stats_data = updated_stats_response.json()
    
    old_count = data.get("count", 0)
    new_count = updated_stats_data.get("data", {}).get("count", 0)
    
    if new_count > old_count:
        print(f"✅ 数据已更新: 请求数从 {old_count} 增加到 {new_count}")
    else:
        print(f"⚠️ 数据可能未更新: 请求数仍为 {new_count}")
    
    print("\n" + "=" * 60)
    print("✅ API性能监控集成测试完成！")
    
    # 7. 前端验证指南
    print("\n🎯 前端验证指南:")
    print("1. 访问 http://localhost:8080/#/performance")
    print("2. 点击'API性能'标签页")
    print("3. 应该看到以下数据:")
    print(f"   - 平均响应时间: {data.get('avg', 0):.3f}s")
    print(f"   - 请求数量: {data.get('count', 0)}")
    print(f"   - 慢端点数量: {len(data.get('endpoints', {}))}")
    print("4. 图表应该显示API响应时间趋势")
    print("5. 慢端点表格应该显示具体端点信息")
    
    return True

if __name__ == "__main__":
    success = test_api_performance_integration()
    if success:
        print("\n🎉 所有测试通过！前端应该能正确显示API性能数据。")
    else:
        print("\n❌ 测试失败，请检查后端服务状态。")
