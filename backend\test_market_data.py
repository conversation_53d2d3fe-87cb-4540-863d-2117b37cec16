import asyncio
from monitoring_routes_module import get_sync_service

async def test():
    sync_service = get_sync_service()
    df = await sync_service.get_market_data(source_id=1)
    print(f'DataFrame shape: {df.shape}')
    print(f'DataFrame columns: {df.columns}')
    print(f'First 5 rows timestamp: {df["timestamp"].head(5).tolist() if not df.empty else "Empty"}')

if __name__ == "__main__":
    asyncio.run(test())
