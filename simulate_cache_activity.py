#!/usr/bin/env python3
"""
模拟缓存活动，生成真实的缓存统计数据
"""

import requests
import json
import time
import random

def simulate_cache_activity():
    """模拟缓存活动"""
    
    print("🔄 开始模拟缓存活动...")
    print("=" * 60)
    
    # 获取认证token
    print("\n1. 🔐 获取认证令牌...")
    try:
        auth_response = requests.post('http://localhost:8000/api/v1/auth/token', 
                                     json={'username': 'admin', 'password': 'admin123'})
        
        if auth_response.status_code != 200:
            print(f"❌ 认证失败: {auth_response.status_code}")
            return
        
        token = auth_response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print(f"✅ 认证成功")
        
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return
    
    # 模拟多次API调用来生成缓存活动
    print("\n2. 🔄 模拟API调用生成缓存活动...")
    
    api_endpoints = [
        '/api/v1/performance/summary',
        '/api/v1/performance/analysis', 
        '/api/v1/performance/metrics/cpu?limit=10',
        '/api/v1/performance/metrics/memory?limit=10',
        '/api/v1/performance/metrics/api?limit=10',
        '/api/v1/performance/metrics/cache?limit=10',
        '/api/v1/performance/api/stats',
        '/api/v1/performance/api/endpoints',
        '/api/v1/performance/memory/usage',
        '/api/v1/performance/memory/analysis'
    ]
    
    # 进行多轮API调用
    for round_num in range(1, 4):
        print(f"\n   第 {round_num} 轮调用:")
        
        for i, endpoint in enumerate(api_endpoints, 1):
            try:
                response = requests.get(f'http://localhost:8000{endpoint}', headers=headers)
                if response.status_code == 200:
                    print(f"      {i:2d}. ✅ {endpoint}")
                else:
                    print(f"      {i:2d}. ❌ {endpoint} - {response.status_code}")
                
                # 随机延迟
                time.sleep(random.uniform(0.1, 0.3))
                
            except Exception as e:
                print(f"      {i:2d}. ❌ {endpoint} - 异常: {e}")
        
        # 轮次间延迟
        if round_num < 3:
            print(f"      等待 2 秒后进行下一轮...")
            time.sleep(2)
    
    # 检查缓存统计
    print("\n3. 📊 检查缓存统计...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data.get('data', {})
                print(f"   ✅ 缓存统计:")
                print(f"      缓存大小: {stats.get('size', 0)}")
                print(f"      最大大小: {stats.get('max_size', 0)}")
                print(f"      命中次数: {stats.get('hits', 0)}")
                print(f"      未命中次数: {stats.get('misses', 0)}")
                print(f"      命中率: {stats.get('hit_rate', 0)*100:.1f}%")
                print(f"      项目数量: {stats.get('items_count', 0)}")
            else:
                print(f"   ❌ 获取缓存统计失败: {data.get('message', '未知错误')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试缓存清理功能
    print("\n4. 🧹 测试缓存清理功能...")
    try:
        response = requests.post('http://localhost:8000/api/v1/performance/cache/clear', headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ 缓存清理成功")
            else:
                print(f"   ❌ 缓存清理失败: {data.get('message', '未知错误')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 再次检查缓存统计
    print("\n5. 📊 清理后的缓存统计...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data.get('data', {})
                print(f"   ✅ 清理后缓存统计:")
                print(f"      缓存大小: {stats.get('size', 0)}")
                print(f"      最大大小: {stats.get('max_size', 0)}")
                print(f"      命中次数: {stats.get('hits', 0)}")
                print(f"      未命中次数: {stats.get('misses', 0)}")
                print(f"      命中率: {stats.get('hit_rate', 0)*100:.1f}%")
                print(f"      项目数量: {stats.get('items_count', 0)}")
            else:
                print(f"   ❌ 获取缓存统计失败: {data.get('message', '未知错误')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 再次进行API调用生成新的缓存数据
    print("\n6. 🔄 重新生成缓存数据...")
    for i, endpoint in enumerate(api_endpoints[:5], 1):
        try:
            response = requests.get(f'http://localhost:8000{endpoint}', headers=headers)
            if response.status_code == 200:
                print(f"   {i}. ✅ {endpoint}")
            else:
                print(f"   {i}. ❌ {endpoint} - {response.status_code}")
            
            time.sleep(0.2)
            
        except Exception as e:
            print(f"   {i}. ❌ {endpoint} - 异常: {e}")
    
    # 最终缓存统计
    print("\n7. 📊 最终缓存统计...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data.get('data', {})
                print(f"   ✅ 最终缓存统计:")
                print(f"      缓存大小: {stats.get('size', 0)}")
                print(f"      最大大小: {stats.get('max_size', 0)}")
                print(f"      命中次数: {stats.get('hits', 0)}")
                print(f"      未命中次数: {stats.get('misses', 0)}")
                print(f"      命中率: {stats.get('hit_rate', 0)*100:.1f}%")
                print(f"      项目数量: {stats.get('items_count', 0)}")
                
                # 判断缓存是否正常工作
                if stats.get('size', 0) > 0 or stats.get('hits', 0) > 0:
                    print(f"\n🎉 缓存系统正常工作！")
                else:
                    print(f"\n⚠️  缓存系统可能未正常工作，所有值都为0")
                    
            else:
                print(f"   ❌ 获取缓存统计失败: {data.get('message', '未知错误')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print("\n" + "=" * 60)
    print("🔄 缓存活动模拟完成")
    print(f"完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    simulate_cache_activity()
