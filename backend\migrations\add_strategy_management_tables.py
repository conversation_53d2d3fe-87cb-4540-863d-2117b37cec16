"""
策略管理系统数据库迁移脚本
添加新的策略管理表结构
"""

import sqlite3
import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_db_path():
    """获取数据库路径"""
    # 查找数据库文件
    possible_paths = [
        'app.db',
        'backend/app.db',
        'app/database.db',
        'database.db',
        '../database.db',
        'app/btc_trading.db',
        'btc_trading.db'
    ]

    for path in possible_paths:
        if os.path.exists(path):
            return path

    # 如果没找到，创建新的数据库
    return 'app/database.db'

def migrate_strategy_tables():
    """迁移策略相关表"""
    db_path = get_db_path()
    logger.info(f"使用数据库: {db_path}")

    # 确保目录存在
    db_dir = os.path.dirname(db_path)
    if db_dir:  # 只有当目录不为空时才创建
        os.makedirs(db_dir, exist_ok=True)

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 1. 备份现有策略表（如果存在）
        cursor.execute("""
            SELECT name FROM sqlite_master
            WHERE type='table' AND name='strategies'
        """)

        if cursor.fetchone():
            logger.info("备份现有策略表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS strategies_backup AS
                SELECT * FROM strategies
            """)

        # 2. 创建新的策略表结构
        logger.info("创建新的策略表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategies_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                type VARCHAR(50) NOT NULL,
                category VARCHAR(50) NOT NULL,
                description TEXT,
                code_type VARCHAR(20) DEFAULT 'python',
                code_content TEXT,
                parameters JSON,
                template_id INTEGER,
                symbol VARCHAR(20),
                timeframe VARCHAR(10),
                file_path VARCHAR(255),
                status VARCHAR(20) DEFAULT 'created',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                creator_id INTEGER,
                FOREIGN KEY (creator_id) REFERENCES users(id)
            )
        """)

        # 3. 创建策略模板表
        logger.info("创建策略模板表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                type VARCHAR(50) NOT NULL,
                category VARCHAR(50) NOT NULL,
                description TEXT,
                code_template TEXT NOT NULL,
                parameter_schema JSON,
                default_parameters JSON,
                is_builtin BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 4. 创建策略参数历史表
        logger.info("创建策略参数历史表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_parameter_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_id INTEGER NOT NULL,
                parameters JSON NOT NULL,
                changed_by INTEGER,
                change_reason VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (strategy_id) REFERENCES strategies_new(id),
                FOREIGN KEY (changed_by) REFERENCES users(id)
            )
        """)

        # 5. 迁移现有数据（如果有的话）
        cursor.execute("""
            SELECT name FROM sqlite_master
            WHERE type='table' AND name='strategies'
        """)

        if cursor.fetchone():
            logger.info("迁移现有策略数据...")

            # 获取旧表结构
            cursor.execute("PRAGMA table_info(strategies)")
            old_columns = [row[1] for row in cursor.fetchall()]

            # 构建迁移SQL
            common_columns = []
            if 'id' in old_columns:
                common_columns.append('id')
            if 'name' in old_columns:
                common_columns.append('name')
            if 'description' in old_columns:
                common_columns.append('description')
            if 'code' in old_columns:
                common_columns.append('code AS code_content')
            elif 'code_content' in old_columns:
                common_columns.append('code_content')
            if 'is_active' in old_columns:
                common_columns.append('is_active')
            if 'created_at' in old_columns:
                common_columns.append('created_at')
            if 'updated_at' in old_columns:
                common_columns.append('updated_at')
            if 'creator_id' in old_columns:
                common_columns.append('creator_id')
            elif 'created_by' in old_columns:
                common_columns.append('created_by AS creator_id')

            if common_columns:
                # 添加默认值
                select_sql = f"""
                    INSERT INTO strategies_new (
                        {', '.join([col.split(' AS ')[0] if ' AS ' in col else col for col in common_columns])},
                        type, category, code_type, status
                    )
                    SELECT
                        {', '.join(common_columns)},
                        'custom' as type,
                        'python_custom' as category,
                        'python' as code_type,
                        'created' as status
                    FROM strategies
                """
                cursor.execute(select_sql)
                logger.info(f"迁移了 {cursor.rowcount} 条策略记录")

        # 6. 替换旧表
        cursor.execute("DROP TABLE IF EXISTS strategies")
        cursor.execute("ALTER TABLE strategies_new RENAME TO strategies")

        # 7. 创建索引
        logger.info("创建索引...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategies_type ON strategies(type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategies_category ON strategies(category)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategies_creator ON strategies(creator_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategies_active ON strategies(is_active)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_templates_type ON strategy_templates(type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_templates_builtin ON strategy_templates(is_builtin)")

        # 8. 创建用户表（如果不存在）
        logger.info("确保用户表存在...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE,
                password_hash VARCHAR(255),
                role VARCHAR(20) DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 插入默认管理员用户（如果不存在）
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        if not cursor.fetchone():
            cursor.execute("""
                INSERT INTO users (username, email, role, password_hash)
                VALUES ('admin', '<EMAIL>', 'admin', 'admin_hash')
            """)
            logger.info("创建默认管理员用户")

        conn.commit()
        logger.info("数据库迁移完成")

        # 9. 验证表结构
        cursor.execute("PRAGMA table_info(strategies)")
        columns = cursor.fetchall()
        logger.info(f"策略表字段: {[col[1] for col in columns]}")

        cursor.execute("SELECT COUNT(*) FROM strategies")
        count = cursor.fetchone()[0]
        logger.info(f"策略表记录数: {count}")

        cursor.execute("SELECT COUNT(*) FROM strategy_templates")
        template_count = cursor.fetchone()[0]
        logger.info(f"模板表记录数: {template_count}")

    except Exception as e:
        conn.rollback()
        logger.error(f"数据库迁移失败: {str(e)}")
        raise
    finally:
        conn.close()

def main():
    """主函数"""
    logger.info("开始策略管理系统数据库迁移...")
    migrate_strategy_tables()
    logger.info("数据库迁移完成")

if __name__ == "__main__":
    main()
