#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终策略模板初始化脚本
创建剩余的策略模板：动态网格、马丁格尔、自定义策略
"""

import os
import sys
import sqlite3
import json
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_final_templates(conn):
    """创建最终策略模板"""
    cursor = conn.cursor()

    # 动态网格策略模板 (网格策略类型)
    dynamic_grid_code = """
def initialize(context):
    context.volatility_period = {{ volatility_period | default(20) }}
    context.grid_multiplier = {{ grid_multiplier | default(2.0) }}
    context.max_grid_levels = {{ max_grid_levels | default(10) }}
    context.position = 0
    context.grid_positions = {}

def calculate_volatility(prices, period=20):
    returns = prices.pct_change().dropna()
    return returns.rolling(period).std().iloc[-1]

def handle_bar(context, data):
    close_prices = data['close']

    if len(close_prices) < context.volatility_period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}

    current_price = close_prices.iloc[-1]
    volatility = calculate_volatility(close_prices, context.volatility_period)

    # 动态计算网格间距
    dynamic_grid_size = volatility * context.grid_multiplier

    # 计算网格级别
    if not hasattr(context, 'base_price'):
        context.base_price = current_price

    price_diff = (current_price - context.base_price) / context.base_price
    grid_level = int(price_diff / dynamic_grid_size)

    # 价格下跌到下一个网格买入
    if grid_level < 0 and grid_level not in context.grid_positions:
        context.grid_positions[grid_level] = current_price
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'动态网格买入 (级别:{grid_level} 波动率:{volatility:.4f})'
        }

    # 价格上涨到上一个网格卖出
    elif grid_level > 0 and (grid_level - 1) in context.grid_positions:
        del context.grid_positions[grid_level - 1]
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'动态网格卖出 (级别:{grid_level} 波动率:{volatility:.4f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("动态网格策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "动态网格策略",
            "grid",
            "dynamic_grid",
            "基于动态网格的交易策略，根据市场波动率调整网格间距",
            dynamic_grid_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "volatility_period",
                        "type": "integer",
                        "default": 20,
                        "min": 10,
                        "max": 50,
                        "description": "波动率计算周期",
                        "required": True
                    },
                    {
                        "name": "grid_multiplier",
                        "type": "float",
                        "default": 2.0,
                        "min": 1.0,
                        "max": 5.0,
                        "description": "网格倍数",
                        "required": True
                    },
                    {
                        "name": "max_grid_levels",
                        "type": "integer",
                        "default": 10,
                        "min": 5,
                        "max": 20,
                        "description": "最大网格级别",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "volatility_period": 20,
                "grid_multiplier": 2.0,
                "max_grid_levels": 10
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建动态网格策略模板")
    else:
        logger.info("动态网格策略模板已存在")

    # 马丁格尔策略模板 (网格策略类型)
    martingale_code = """
def initialize(context):
    context.base_amount = {{ base_amount | default(100) }}
    context.multiplier = {{ multiplier | default(2.0) }}
    context.max_levels = {{ max_levels | default(5) }}
    context.profit_target = {{ profit_target | default(0.02) }}
    context.position = 0
    context.current_level = 0
    context.entry_prices = []

def handle_bar(context, data):
    current_price = data['close'].iloc[-1]

    # 首次买入
    if context.position == 0:
        context.position = 1
        context.current_level = 1
        context.entry_prices = [current_price]
        return {
            'action': 'BUY',
            'price': current_price,
            'amount': context.base_amount,
            'reason': f'马丁格尔首次买入 (级别:{context.current_level})'
        }

    # 价格下跌，加仓
    elif (context.position > 0 and
          current_price < context.entry_prices[-1] * 0.95 and
          context.current_level < context.max_levels):
        context.current_level += 1
        context.entry_prices.append(current_price)
        amount = context.base_amount * (context.multiplier ** (context.current_level - 1))
        return {
            'action': 'BUY',
            'price': current_price,
            'amount': amount,
            'reason': f'马丁格尔加仓 (级别:{context.current_level})'
        }

    # 价格上涨，达到盈利目标
    elif (context.position > 0 and
          current_price > context.entry_prices[0] * (1 + context.profit_target)):
        context.position = 0
        context.current_level = 0
        context.entry_prices = []
        return {
            'action': 'SELL_ALL',
            'price': current_price,
            'reason': f'马丁格尔止盈 (目标:{context.profit_target:.2%})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("马丁格尔策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "马丁格尔策略",
            "grid",
            "martingale",
            "基于马丁格尔的交易策略，价格下跌时加倍买入，等待反弹获利",
            martingale_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "base_amount",
                        "type": "float",
                        "default": 100,
                        "min": 10,
                        "max": 10000,
                        "description": "基础交易金额",
                        "required": True
                    },
                    {
                        "name": "multiplier",
                        "type": "float",
                        "default": 2.0,
                        "min": 1.5,
                        "max": 3.0,
                        "description": "加仓倍数",
                        "required": True
                    },
                    {
                        "name": "max_levels",
                        "type": "integer",
                        "default": 5,
                        "min": 3,
                        "max": 10,
                        "description": "最大加仓级别",
                        "required": True
                    },
                    {
                        "name": "profit_target",
                        "type": "float",
                        "default": 0.02,
                        "min": 0.01,
                        "max": 0.1,
                        "description": "盈利目标",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "base_amount": 100,
                "multiplier": 2.0,
                "max_levels": 5,
                "profit_target": 0.02
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建马丁格尔策略模板")
    else:
        logger.info("马丁格尔策略模板已存在")

    # Python自定义策略模板 (自定义策略类型)
    python_custom_code = """
def initialize(context):
    # 在这里初始化您的策略参数
    context.custom_param1 = {{ custom_param1 | default(10) }}
    context.custom_param2 = {{ custom_param2 | default(0.02) }}
    context.position = 0

    # 添加您的自定义初始化逻辑
    pass

def handle_bar(context, data):
    # 获取当前价格数据
    current_price = data['close'].iloc[-1]

    # 在这里编写您的自定义交易逻辑
    # 示例：简单的价格突破策略
    if len(data['close']) >= 20:
        sma_20 = data['close'].rolling(20).mean().iloc[-1]

        if current_price > sma_20 * 1.02 and context.position <= 0:
            context.position = 1
            return {
                'action': 'BUY',
                'price': current_price,
                'reason': '自定义买入信号'
            }
        elif current_price < sma_20 * 0.98 and context.position >= 0:
            context.position = -1
            return {
                'action': 'SELL',
                'price': current_price,
                'reason': '自定义卖出信号'
            }

    return {'action': 'HOLD'}

# 您可以在这里添加自定义函数
def custom_indicator(prices, period):
    # 自定义指标计算
    return prices.rolling(period).mean()
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("Python自定义策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "Python自定义策略",
            "custom",
            "python_custom",
            "Python自定义策略模板，用户可以在此基础上编写自己的交易逻辑",
            python_custom_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "custom_param1",
                        "type": "integer",
                        "default": 10,
                        "min": 1,
                        "max": 100,
                        "description": "自定义参数1",
                        "required": True
                    },
                    {
                        "name": "custom_param2",
                        "type": "float",
                        "default": 0.02,
                        "min": 0.01,
                        "max": 0.1,
                        "description": "自定义参数2",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "custom_param1": 10,
                "custom_param2": 0.02
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建Python自定义策略模板")
    else:
        logger.info("Python自定义策略模板已存在")

    # Pine Script自定义策略模板 (自定义策略类型)
    pinescript_custom_code = """
//@version=5
strategy("自定义Pine Script策略", overlay=true)

// 输入参数
length = input.int({{ length | default(14) }}, title="周期长度", minval=1, maxval=100)
threshold = input.float({{ threshold | default(0.02) }}, title="阈值", minval=0.01, maxval=0.1)

// 计算指标
sma = ta.sma(close, length)
price_change = (close - close[1]) / close[1]

// 交易条件
long_condition = close > sma and price_change > threshold
short_condition = close < sma and price_change < -threshold

// 执行交易
if long_condition
    strategy.entry("Long", strategy.long)

if short_condition
    strategy.entry("Short", strategy.short)

// 绘制指标
plot(sma, color=color.blue, title="SMA")
plotshape(long_condition, style=shape.triangleup, location=location.belowbar, color=color.green, size=size.small)
plotshape(short_condition, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.small)
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("Pine Script自定义策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "Pine Script自定义策略",
            "custom",
            "pinescript_custom",
            "Pine Script自定义策略模板，兼容TradingView格式",
            pinescript_custom_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "length",
                        "type": "integer",
                        "default": 14,
                        "min": 1,
                        "max": 100,
                        "description": "周期长度",
                        "required": True
                    },
                    {
                        "name": "threshold",
                        "type": "float",
                        "default": 0.02,
                        "min": 0.01,
                        "max": 0.1,
                        "description": "阈值",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "length": 14,
                "threshold": 0.02
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建Pine Script自定义策略模板")
    else:
        logger.info("Pine Script自定义策略模板已存在")

    # 机器学习策略模板 (自定义策略类型)
    ml_strategy_code = """
def initialize(context):
    context.feature_period = {{ feature_period | default(20) }}
    context.prediction_threshold = {{ prediction_threshold | default(0.6) }}
    context.retrain_frequency = {{ retrain_frequency | default(100) }}
    context.position = 0
    context.model = None
    context.bar_count = 0

def extract_features(data, period=20):
    # 提取技术指标作为特征
    import pandas as pd

    features = pd.DataFrame()
    prices = data['close']

    # 移动平均线
    features['sma_5'] = prices.rolling(5).mean()
    features['sma_20'] = prices.rolling(20).mean()

    # RSI
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    features['rsi'] = 100 - (100 / (1 + rs))

    # 价格变化率
    features['price_change'] = prices.pct_change()

    # 波动率
    features['volatility'] = prices.rolling(period).std()

    return features.dropna()

def handle_bar(context, data):
    current_price = data['close'].iloc[-1]
    context.bar_count += 1

    if len(data['close']) < context.feature_period + 20:
        return {'action': 'HOLD', 'reason': '数据不足'}

    # 提取特征
    features = extract_features(data, context.feature_period)

    if len(features) < 50:  # 需要足够的历史数据
        return {'action': 'HOLD', 'reason': '特征数据不足'}

    # 简化的预测逻辑（实际应用中应使用真实的ML模型）
    latest_features = features.iloc[-1]

    # 简单的规则基预测
    if latest_features['rsi'] < 30 and latest_features['price_change'] > 0:
        prediction = 0.8  # 买入信号
    elif latest_features['rsi'] > 70 and latest_features['price_change'] < 0:
        prediction = 0.2  # 卖出信号
    else:
        prediction = 0.5  # 持有

    # 根据预测执行交易
    if prediction > context.prediction_threshold and context.position <= 0:
        context.position = 1
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'ML预测买入 (置信度:{prediction:.2f})'
        }
    elif prediction < (1 - context.prediction_threshold) and context.position >= 0:
        context.position = -1
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'ML预测卖出 (置信度:{1-prediction:.2f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("机器学习策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "机器学习策略",
            "custom",
            "ml_strategy",
            "基于机器学习的策略模板，使用技术指标作为特征进行预测",
            ml_strategy_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "feature_period",
                        "type": "integer",
                        "default": 20,
                        "min": 10,
                        "max": 50,
                        "description": "特征计算周期",
                        "required": True
                    },
                    {
                        "name": "prediction_threshold",
                        "type": "float",
                        "default": 0.6,
                        "min": 0.5,
                        "max": 0.9,
                        "description": "预测阈值",
                        "required": True
                    },
                    {
                        "name": "retrain_frequency",
                        "type": "integer",
                        "default": 100,
                        "min": 50,
                        "max": 500,
                        "description": "重训练频率",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "feature_period": 20,
                "prediction_threshold": 0.6,
                "retrain_frequency": 100
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建机器学习策略模板")
    else:
        logger.info("机器学习策略模板已存在")

    logger.info("最终策略模板创建完成")

def main():
    """主函数"""
    logger.info("开始初始化最终策略模板...")

    # 获取数据库路径
    db_paths = [
        'app.db',
        'backend/app.db',
        'app/database.db',
        'app/db/database.db'
    ]

    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        db_path = 'app.db'  # 使用默认路径

    logger.info(f"使用数据库: {db_path}")

    # 连接数据库
    conn = sqlite3.connect(db_path)

    try:
        # 创建最终模板
        create_final_templates(conn)

        # 提交更改
        conn.commit()
        logger.info("最终策略模板初始化完成")

    except Exception as e:
        conn.rollback()
        logger.error(f"初始化最终策略模板失败: {str(e)}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    main()
