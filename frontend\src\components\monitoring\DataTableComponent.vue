<template>
  <div class="data-table" :style="{ height: height }">
    <div v-if="loading" class="loading-overlay">
      <i class="el-icon-loading"></i>
      <p>加载数据中...</p>
    </div>

    <div v-else-if="error" class="error-message">
      <i class="el-icon-warning"></i>
      <p>{{ error }}</p>
    </div>

    <div v-else-if="!tableData || tableData.length === 0" class="empty-data">
      <i class="el-icon-document"></i>
      <p>暂无数据</p>
    </div>

    <el-table
      v-else
      :data="tableData"
      border
      stripe
      size="small"
      style="width: 100%"
      :max-height="maxHeight"
      :row-class-name="getRowClass"
    >
      <!-- 动态生成列 -->
      <el-table-column
        v-for="column in tableColumns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :sortable="column.sortable"
        :formatter="column.formatter"
      >
        <template slot-scope="scope" v-if="column.type === 'datetime'">
          {{ formatDateTime(scope.row[column.prop]) }}
        </template>

        <template slot-scope="scope" v-else-if="column.type === 'price'">
          <span :class="getPriceClass(scope.row[column.prop], column.prop)">
            {{ formatPrice(scope.row[column.prop]) }}
          </span>
        </template>

        <template slot-scope="scope" v-else-if="column.type === 'volume'">
          {{ formatVolume(scope.row[column.prop]) }}
        </template>
      </el-table-column>
    </el-table>

    <div class="table-pagination" v-if="tableData && tableData.length > 0">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[5, 10, 20, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalItems"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'DataTableComponent',
  props: {
    dataSource: {
      type: Object,
      required: true
    },
    columns: {
      type: Array,
      default: () => []
    },
    pageSize: {
      type: Number,
      default: 10
    },
    height: {
      type: String,
      default: '350px'
    }
  },
  data() {
    return {
      loading: false,
      error: null,
      tableData: [],
      currentPage: 1,
      totalItems: 0,
      maxHeight: '300px',
      lastPrice: null
    };
  },
  computed: {
    tableColumns() {
      // 如果提供了自定义列，使用自定义列
      if (this.columns && this.columns.length > 0) {
        return this.columns;
      }

      // 否则，根据数据类型自动生成列
      if (this.tableData && this.tableData.length > 0) {
        const firstRow = this.tableData[0];

        // 检测是否为OHLCV数据
        if ('open' in firstRow && 'high' in firstRow && 'low' in firstRow && 'close' in firstRow) {
          return [
            { prop: 'timestamp', label: '时间', width: '180', type: 'datetime' },
            { prop: 'open', label: '开盘价', width: '120', type: 'price', sortable: true },
            { prop: 'high', label: '最高价', width: '120', type: 'price', sortable: true },
            { prop: 'low', label: '最低价', width: '120', type: 'price', sortable: true },
            { prop: 'close', label: '收盘价', width: '120', type: 'price', sortable: true },
            { prop: 'volume', label: '成交量', width: '120', type: 'volume', sortable: true }
          ];
        }

        // 默认列配置
        return Object.keys(firstRow).map(key => {
          let type = 'text';
          let width = '120';

          // 根据字段名和值类型推断列类型
          if (key.includes('time') || key.includes('date')) {
            type = 'datetime';
            width = '180';
          } else if (key.includes('price') || key === 'open' || key === 'high' || key === 'low' || key === 'close') {
            type = 'price';
          } else if (key.includes('volume') || key.includes('amount')) {
            type = 'volume';
          }

          return {
            prop: key,
            label: this.formatColumnLabel(key),
            width,
            type,
            sortable: true
          };
        });
      }

      return [];
    }
  },
  mounted() {
    this.fetchData();
    this.calculateMaxHeight();

    // 监听窗口大小变化，重新计算表格高度
    window.addEventListener('resize', this.calculateMaxHeight);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateMaxHeight);
  },
  methods: {
    async fetchData() {
      this.loading = true;
      this.error = null;

      try {
        // 克隆dataSource以避免修改原始对象
        const dataSourceConfig = JSON.parse(JSON.stringify(this.dataSource));

        console.log('DataTableComponent - 原始dataSource:', this.dataSource);

        // 获取市场数据服务URL
        const marketDataUrl = process.env.VUE_APP_MARKET_DATA_URL || 'http://localhost:8005';

        // 从URL中提取API端点
        let endpoint = '/api/v1/data/ohlcv';
        if (dataSourceConfig.url) {
          const urlParts = dataSourceConfig.url.split('/');
          endpoint = '/' + urlParts.slice(3).join('/');
        }

        // 从URL中提取交易对
        let symbol = 'BTCUSDT';
        if (dataSourceConfig.params && dataSourceConfig.params.symbol) {
          symbol = dataSourceConfig.params.symbol;
          // 确保使用不带斜杠的格式
          if (symbol.includes('/')) {
            symbol = symbol.replace('/', '');
          }
        }

        // 从URL中提取时间周期
        let timeframe = '1h';
        if (dataSourceConfig.params && dataSourceConfig.params.timeframe) {
          timeframe = dataSourceConfig.params.timeframe;
        }

        // 从URL中提取限制数量
        let limit = 100;
        if (dataSourceConfig.params && dataSourceConfig.params.limit) {
          limit = dataSourceConfig.params.limit;
        }

        // 构建请求参数
        const params = {
          symbol: symbol,
          timeframe: timeframe,
          limit: limit
        };

        // 如果有开始时间和结束时间，也添加到参数中
        if (dataSourceConfig.params && dataSourceConfig.params.start_time) {
          params.start_time = dataSourceConfig.params.start_time;
        }
        if (dataSourceConfig.params && dataSourceConfig.params.end_time) {
          params.end_time = dataSourceConfig.params.end_time;
        }

        console.log('DataTableComponent - 修正后的请求参数:', params);

        // 发送请求
        const response = await axios.get(`${marketDataUrl}${endpoint}`, { params });

        if (response.data) {
          // 处理不同的数据格式
          if (Array.isArray(response.data)) {
            this.tableData = response.data;
          } else if (response.data.data && Array.isArray(response.data.data)) {
            this.tableData = response.data.data;
          } else if (response.data.data && response.data.data.candles && Array.isArray(response.data.data.candles)) {
            // OHLCV数据格式
            this.tableData = response.data.data.candles;
          } else {
            console.warn('未知的数据格式:', response.data);
            this.tableData = [];
          }

          this.totalItems = this.tableData.length;

          // 记录最后价格用于比较
          if (this.tableData.length > 0 && 'close' in this.tableData[0]) {
            this.lastPrice = this.tableData[0].close;
          }
        } else {
          this.tableData = [];
          this.totalItems = 0;
        }
      } catch (error) {
        console.error('获取表格数据失败:', error);
        this.error = '获取数据失败: ' + (error.response?.data?.message || error.message);
        this.tableData = [];
      } finally {
        this.loading = false;
      }
    },

    calculateMaxHeight() {
      // 计算表格最大高度，留出分页器的空间
      if (this.$el) {
        const containerHeight = this.$el.clientHeight;
        this.maxHeight = `${containerHeight - 50}px`; // 减去分页器高度和边距
      }
    },

    formatColumnLabel(key) {
      // 将字段名转换为更友好的显示名称
      const labelMap = {
        timestamp: '时间',
        open: '开盘价',
        high: '最高价',
        low: '最低价',
        close: '收盘价',
        volume: '成交量',
        symbol: '交易对',
        exchange: '交易所',
        source_id: '数据源ID'
      };

      if (labelMap[key]) {
        return labelMap[key];
      }

      // 将下划线分隔的字段名转换为空格分隔的标题格式
      return key.replace(/_/g, ' ')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    },

    formatDateTime(timestamp) {
      if (!timestamp) return '暂无数据';

      try {
        const date = new Date(timestamp);
        return date.toLocaleString();
      } catch (e) {
        return timestamp;
      }
    },

    formatPrice(price) {
      if (price === undefined || price === null) return '暂无数据';

      // 根据价格大小选择合适的小数位数
      let decimals = 2;
      if (price < 1) decimals = 6;
      else if (price < 10) decimals = 4;
      else if (price < 1000) decimals = 2;
      else decimals = 2;

      return Number(price).toFixed(decimals);
    },

    formatVolume(volume) {
      if (volume === undefined || volume === null) return '暂无数据';

      // 格式化大数字
      if (volume >= 1000000) {
        return (volume / 1000000).toFixed(2) + 'M';
      } else if (volume >= 1000) {
        return (volume / 1000).toFixed(2) + 'K';
      } else {
        return volume.toFixed(2);
      }
    },

    getPriceClass(price, field) {
      // 为价格添加颜色，上涨为绿色，下跌为红色
      if (field === 'close' && this.lastPrice !== null && this.lastPrice !== price) {
        return price > this.lastPrice ? 'price-up' : 'price-down';
      }
      return '';
    },

    getRowClass({ row, rowIndex }) {
      // 可以根据行数据添加自定义类名
      return '';
    },

    handleSizeChange(size) {
      this.pageSize = size;
    },

    handleCurrentChange(page) {
      this.currentPage = page;
    }
  }
};
</script>

<style scoped>
.data-table {
  position: relative;
  overflow: hidden;
}

.loading-overlay, .error-message, .empty-data {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(20, 30, 60, 0.7);
  color: #EAEAEA;
  z-index: 1;
}

.loading-overlay i, .error-message i, .empty-data i {
  font-size: 32px;
  margin-bottom: 10px;
}

.error-message i {
  color: #F56C6C;
}

.table-pagination {
  margin-top: 15px;
  text-align: right;
}

.price-up {
  color: #67C23A;
}

.price-down {
  color: #F56C6C;
}

/* 适配暗色主题 */
:deep(.el-table) {
  background-color: transparent;
  color: #EAEAEA;
}

:deep(.el-table th) {
  background-color: rgba(10, 20, 50, 0.7);
  color: #00F7FF;
}

:deep(.el-table tr) {
  background-color: rgba(20, 30, 60, 0.5);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: rgba(30, 40, 70, 0.5);
}

:deep(.el-table td, .el-table th.is-leaf) {
  border-color: rgba(0, 247, 255, 0.1);
}

:deep(.el-table--border) {
  border-color: rgba(0, 247, 255, 0.2);
}

:deep(.el-pagination) {
  color: #EAEAEA;
}

:deep(.el-pagination button) {
  background-color: transparent;
  color: #EAEAEA;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  background-color: rgba(20, 30, 60, 0.7);
  color: #EAEAEA;
  border-color: rgba(0, 247, 255, 0.2);
}
</style>
