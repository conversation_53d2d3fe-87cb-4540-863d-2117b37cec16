# 前端功能测试脚本 - 使用正确的8080端口
# 此脚本直接测试通过start-services.ps1启动的前端服务

param(
    [string]$TestType = "all"  # all, api, frontend, optimization
)

# 颜色配置
$infoColor = "Cyan"
$successColor = "Green"
$errorColor = "Red"
$warningColor = "Yellow"

Write-Host "=========================================================" -ForegroundColor $infoColor
Write-Host "           前端功能测试 - 使用8080端口服务              " -ForegroundColor $infoColor
Write-Host "=========================================================" -ForegroundColor $infoColor
Write-Host ""

# 检查8080端口是否有服务运行
function Test-FrontendService {
    Write-Host "检查前端服务状态..." -ForegroundColor $infoColor
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080" -Method GET -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200 -and $response.Content -like "*量化交易系统*") {
            Write-Host "✓ 前端服务正常运行在8080端口" -ForegroundColor $successColor
            return $true
        } else {
            Write-Host "✗ 前端服务响应异常" -ForegroundColor $errorColor
            return $false
        }
    } catch {
        Write-Host "✗ 无法连接到前端服务: $_" -ForegroundColor $errorColor
        Write-Host "请确保已运行 .\start-services.ps1 启动服务" -ForegroundColor $warningColor
        return $false
    }
}

# 测试API连接
function Test-ApiConnections {
    Write-Host "测试API连接..." -ForegroundColor $infoColor
    
    $apis = @(
        @{Name="主API服务"; Url="http://localhost:8000/api/v1/strategy"; Port=8000},
        @{Name="回测API"; Url="http://localhost:8001/health"; Port=8001},
        @{Name="风险管理API"; Url="http://localhost:8002/health"; Port=8002},
        @{Name="交易API"; Url="http://localhost:8003/health"; Port=8003},
        @{Name="信号API"; Url="http://localhost:8004/health"; Port=8004},
        @{Name="市场数据API"; Url="http://localhost:8005/health"; Port=8005}
    )
    
    $successCount = 0
    foreach ($api in $apis) {
        try {
            $response = Invoke-RestMethod -Uri $api.Url -Method GET -TimeoutSec 5
            Write-Host "  ✓ $($api.Name) (端口 $($api.Port))" -ForegroundColor $successColor
            $successCount++
        } catch {
            Write-Host "  ✗ $($api.Name) (端口 $($api.Port)) - $_" -ForegroundColor $errorColor
        }
    }
    
    Write-Host "API连接测试完成: $successCount/$($apis.Count) 个服务正常" -ForegroundColor $infoColor
    return $successCount -eq $apis.Count
}

# 测试策略优化功能
function Test-StrategyOptimization {
    Write-Host "测试策略优化功能..." -ForegroundColor $infoColor
    
    try {
        # 1. 测试策略列表
        Write-Host "  1. 测试策略列表API..." -ForegroundColor $infoColor
        $strategies = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/strategy" -Method GET -TimeoutSec 10
        if ($strategies -and $strategies.Count -gt 0) {
            Write-Host "    ✓ 策略列表正常，返回 $($strategies.Count) 个策略" -ForegroundColor $successColor
            $strategyId = $strategies[0].id
        } else {
            Write-Host "    ✗ 策略列表为空" -ForegroundColor $errorColor
            return $false
        }
        
        # 2. 测试策略优化建议
        Write-Host "  2. 测试策略优化建议API..." -ForegroundColor $infoColor
        $optimization = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/strategies/$strategyId/optimization" -Method GET -TimeoutSec 10
        if ($optimization -and $optimization.optimization_suggestions) {
            Write-Host "    ✓ 优化建议正常，返回 $($optimization.optimization_suggestions.Count) 个建议" -ForegroundColor $successColor
        } else {
            Write-Host "    ✗ 优化建议为空" -ForegroundColor $errorColor
            return $false
        }
        
        # 3. 测试应用优化（如果有建议的话）
        if ($optimization.optimization_suggestions.Count -gt 0) {
            Write-Host "  3. 测试应用优化API..." -ForegroundColor $infoColor
            $suggestion = $optimization.optimization_suggestions[0]
            $optimizationParams = @{
                parameter = $suggestion.parameter
                value = $suggestion.suggested_value
            }
            
            $applyResult = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/strategies/$strategyId/optimize" -Method POST -Body ($optimizationParams | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 10
            if ($applyResult -and $applyResult.success) {
                Write-Host "    ✓ 应用优化成功: $($applyResult.message)" -ForegroundColor $successColor
            } else {
                Write-Host "    ✗ 应用优化失败" -ForegroundColor $errorColor
                return $false
            }
        }
        
        Write-Host "策略优化功能测试完成" -ForegroundColor $successColor
        return $true
        
    } catch {
        Write-Host "  ✗ 策略优化功能测试失败: $_" -ForegroundColor $errorColor
        return $false
    }
}

# 测试前端页面访问
function Test-FrontendPages {
    Write-Host "测试前端页面访问..." -ForegroundColor $infoColor
    
    $pages = @(
        @{Name="首页"; Url="http://localhost:8080/#/"},
        @{Name="策略优化"; Url="http://localhost:8080/#/strategy-optimization"},
        @{Name="策略管理"; Url="http://localhost:8080/#/strategy"},
        @{Name="回测分析"; Url="http://localhost:8080/#/backtest"},
        @{Name="风险管理"; Url="http://localhost:8080/#/risk"}
    )
    
    $successCount = 0
    foreach ($page in $pages) {
        try {
            $response = Invoke-WebRequest -Uri $page.Url -Method GET -TimeoutSec 10 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "  ✓ $($page.Name)" -ForegroundColor $successColor
                $successCount++
            } else {
                Write-Host "  ✗ $($page.Name) - 状态码: $($response.StatusCode)" -ForegroundColor $errorColor
            }
        } catch {
            Write-Host "  ✗ $($page.Name) - $_" -ForegroundColor $errorColor
        }
    }
    
    Write-Host "前端页面测试完成: $successCount/$($pages.Count) 个页面正常" -ForegroundColor $infoColor
    return $successCount -eq $pages.Count
}

# 主测试流程
function Start-MainTest {
    $allPassed = $true
    
    # 检查前端服务
    if (-not (Test-FrontendService)) {
        Write-Host "前端服务检查失败，无法继续测试" -ForegroundColor $errorColor
        return $false
    }
    
    # 根据测试类型执行相应测试
    switch ($TestType.ToLower()) {
        "api" {
            $allPassed = Test-ApiConnections
        }
        "frontend" {
            $allPassed = Test-FrontendPages
        }
        "optimization" {
            $allPassed = Test-StrategyOptimization
        }
        "all" {
            $apiResult = Test-ApiConnections
            $frontendResult = Test-FrontendPages
            $optimizationResult = Test-StrategyOptimization
            $allPassed = $apiResult -and $frontendResult -and $optimizationResult
        }
        default {
            Write-Host "未知的测试类型: $TestType" -ForegroundColor $errorColor
            Write-Host "支持的类型: all, api, frontend, optimization" -ForegroundColor $warningColor
            return $false
        }
    }
    
    return $allPassed
}

# 执行测试
$testResult = Start-MainTest

Write-Host ""
Write-Host "=========================================================" -ForegroundColor $infoColor
if ($testResult) {
    Write-Host "                   测试全部通过                          " -ForegroundColor $successColor
    Write-Host "前端服务在8080端口正常工作，所有功能测试通过" -ForegroundColor $successColor
} else {
    Write-Host "                   测试发现问题                          " -ForegroundColor $errorColor
    Write-Host "请检查相关服务状态和日志文件" -ForegroundColor $warningColor
}
Write-Host "=========================================================" -ForegroundColor $infoColor
Write-Host ""

# 提供使用说明
Write-Host "使用说明:" -ForegroundColor $infoColor
Write-Host "  .\test_frontend_on_correct_port.ps1           # 运行所有测试" -ForegroundColor $infoColor
Write-Host "  .\test_frontend_on_correct_port.ps1 -TestType api        # 只测试API连接" -ForegroundColor $infoColor
Write-Host "  .\test_frontend_on_correct_port.ps1 -TestType frontend   # 只测试前端页面" -ForegroundColor $infoColor
Write-Host "  .\test_frontend_on_correct_port.ps1 -TestType optimization # 只测试策略优化功能" -ForegroundColor $infoColor

exit $(if ($testResult) { 0 } else { 1 })
