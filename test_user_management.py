#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理功能验证测试脚本
"""

import requests
import json
import time

def test_user_management():
    """测试用户管理功能"""
    print("=" * 60)
    print("🎯 用户管理功能验证测试")
    print("=" * 60)
    print("🎯 开始用户管理功能测试...")
    
    base_url = "http://localhost:8000"
    
    try:
        # 1. 测试获取用户列表
        print("\n1. 测试获取用户列表...")
        response = requests.get(f"{base_url}/account/users?page=1&limit=10")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 用户列表获取成功")
            print(f"   - 总用户数: {data['data']['total']}")
            print(f"   - 当前页用户数: {len(data['data']['users'])}")
            
            # 显示用户信息
            for user in data['data']['users']:
                print(f"   - 用户: {user['username']} ({user['fullName']}) - {user['role']} - {user['status']}")
        else:
            print(f"❌ 用户列表获取失败: {response.status_code}")
            return False
        
        # 2. 测试获取角色列表
        print("\n2. 测试获取角色列表...")
        response = requests.get(f"{base_url}/account/roles")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 角色列表获取成功")
            print(f"   - 总角色数: {data['data']['total']}")
            
            # 显示角色信息
            for role in data['data']['roles']:
                print(f"   - 角色: {role['name']} - {role['description']}")
                print(f"     权限: {', '.join(role['permissions'])}")
        else:
            print(f"❌ 角色列表获取失败: {response.status_code}")
            return False
        
        # 3. 测试创建新用户
        print("\n3. 测试创建新用户...")
        new_user_data = {
            "username": "test_new_user",
            "email": "<EMAIL>",
            "fullName": "测试新用户",
            "password": "password123",
            "role": "user",
            "status": "active"
        }
        
        response = requests.post(f"{base_url}/account/users", json=new_user_data)
        
        if response.status_code == 201:
            data = response.json()
            print("✅ 新用户创建成功")
            print(f"   - 用户ID: {data['data']['id']}")
            print(f"   - 用户名: {data['data']['username']}")
            print(f"   - 姓名: {data['data']['fullName']}")
            print(f"   - 角色: {data['data']['role']}")
            print(f"   - 状态: {data['data']['status']}")
            new_user_id = data['data']['id']
        else:
            print(f"❌ 新用户创建失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
        
        # 4. 测试获取单个用户信息
        print("\n4. 测试获取单个用户信息...")
        response = requests.get(f"{base_url}/account/users/{new_user_id}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 用户信息获取成功")
            print(f"   - 用户名: {data['data']['username']}")
            print(f"   - 邮箱: {data['data']['email']}")
            print(f"   - 姓名: {data['data']['fullName']}")
            print(f"   - 角色: {data['data']['role']}")
            print(f"   - 状态: {data['data']['status']}")
        else:
            print(f"❌ 用户信息获取失败: {response.status_code}")
            return False
        
        # 5. 测试更新用户信息
        print("\n5. 测试更新用户信息...")
        update_data = {
            "fullName": "更新后的测试用户",
            "role": "analyst",
            "status": "active"
        }
        
        response = requests.put(f"{base_url}/account/users/{new_user_id}", json=update_data)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 用户信息更新成功")
            print(f"   - 更新后姓名: {data['data']['fullName']}")
            print(f"   - 更新后角色: {data['data']['role']}")
        else:
            print(f"❌ 用户信息更新失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
        
        # 6. 测试删除用户
        print("\n6. 测试删除用户...")
        response = requests.delete(f"{base_url}/account/users/{new_user_id}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 用户删除成功")
            print(f"   - 删除消息: {data['message']}")
        else:
            print(f"❌ 用户删除失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
        
        # 7. 验证用户已被删除
        print("\n7. 验证用户已被删除...")
        response = requests.get(f"{base_url}/account/users/{new_user_id}")
        
        if response.status_code == 404:
            print("✅ 用户删除验证成功 - 用户不存在")
        else:
            print(f"❌ 用户删除验证失败: {response.status_code}")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 用户管理功能验证测试通过！")
        print("✅ 用户列表获取正常")
        print("✅ 角色列表获取正常")
        print("✅ 用户创建功能正常")
        print("✅ 用户信息获取正常")
        print("✅ 用户信息更新正常")
        print("✅ 用户删除功能正常")
        print("\n🚀 用户管理系统完全正常工作！")
        print("🎊 前端用户管理页面应该能正常使用所有功能！")
        print("=" * 60)
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 无法连接到后端服务")
        print("请确保后端服务正在运行在端口8000")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    test_user_management()
