#!/usr/bin/env python3
"""
测试策略模板API，验证MACD趋势和布林带回归策略模板是否能正确返回
"""

import requests
import json

def test_strategy_templates():
    """测试策略模板API"""
    base_url = "http://localhost:8000"

    print("🧪 测试策略模板API...")

    # 测试1: 获取所有模板
    print("\n1. 测试获取所有模板:")
    try:
        response = requests.get(f"{base_url}/api/v1/strategy-templates")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功获取响应")
            print(f"   📋 响应数据类型: {type(data)}")

            # 检查响应格式
            if isinstance(data, dict):
                if 'templates' in data:
                    templates = data['templates']
                elif 'data' in data:
                    templates = data['data']
                else:
                    templates = data
                    print(f"   📋 响应字段: {list(data.keys())}")
            else:
                templates = data

            print(f"   📊 模板数量: {len(templates)}")

            # 查找MACD和布林带模板
            macd_templates = []
            bollinger_templates = []

            for template in templates:
                if isinstance(template, dict):
                    name = template.get('name', '')
                    if 'MACD' in name:
                        macd_templates.append(template)
                    if '布林带回归' in name:
                        bollinger_templates.append(template)
                else:
                    print(f"   ⚠️ 模板格式异常: {type(template)} - {template}")

            print(f"   📊 MACD相关模板: {len(macd_templates)} 个")
            for template in macd_templates:
                print(f"      - {template.get('name')} (分类: {template.get('category')})")

            print(f"   📊 布林带回归模板: {len(bollinger_templates)} 个")
            for template in bollinger_templates:
                print(f"      - {template.get('name')} (分类: {template.get('category')})")
        else:
            print(f"   ❌ 获取模板失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {str(e)}")

    # 测试2: 按分类获取MACD趋势模板
    print("\n2. 测试获取MACD趋势分类模板:")
    try:
        response = requests.get(f"{base_url}/api/v1/strategy-templates?category=macd_trend")
        if response.status_code == 200:
            data = response.json()
            templates = data if isinstance(data, list) else data.get('templates', data.get('data', []))
            print(f"   ✅ MACD趋势分类返回 {len(templates)} 个模板")
            for template in templates:
                if isinstance(template, dict):
                    print(f"      - {template.get('name')} (类型: {template.get('type')}, 分类: {template.get('category')})")
                else:
                    print(f"      ⚠️ 异常模板: {template}")
        else:
            print(f"   ❌ 获取MACD趋势模板失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {str(e)}")

    # 测试3: 按分类获取布林带回归模板
    print("\n3. 测试获取布林带回归分类模板:")
    try:
        response = requests.get(f"{base_url}/api/v1/strategy-templates?category=bollinger_reversion")
        if response.status_code == 200:
            data = response.json()
            templates = data if isinstance(data, list) else data.get('templates', data.get('data', []))
            print(f"   ✅ 布林带回归分类返回 {len(templates)} 个模板")
            for template in templates:
                if isinstance(template, dict):
                    print(f"      - {template.get('name')} (类型: {template.get('type')}, 分类: {template.get('category')})")
                else:
                    print(f"      ⚠️ 异常模板: {template}")
        else:
            print(f"   ❌ 获取布林带回归模板失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {str(e)}")

    # 测试4: 按类型获取趋势跟踪模板
    print("\n4. 测试获取趋势跟踪类型模板:")
    try:
        response = requests.get(f"{base_url}/api/v1/strategy-templates?type=trend_following")
        if response.status_code == 200:
            templates = response.json()
            print(f"   ✅ 趋势跟踪类型返回 {len(templates)} 个模板")
            trend_categories = {}
            for template in templates:
                category = template.get('category')
                if category not in trend_categories:
                    trend_categories[category] = []
                trend_categories[category].append(template.get('name'))

            for category, names in trend_categories.items():
                print(f"      分类 {category}: {', '.join(names)}")
        else:
            print(f"   ❌ 获取趋势跟踪模板失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {str(e)}")

    # 测试5: 按类型获取均值回归模板
    print("\n5. 测试获取均值回归类型模板:")
    try:
        response = requests.get(f"{base_url}/api/v1/strategy-templates?type=mean_reversion")
        if response.status_code == 200:
            templates = response.json()
            print(f"   ✅ 均值回归类型返回 {len(templates)} 个模板")
            reversion_categories = {}
            for template in templates:
                category = template.get('category')
                if category not in reversion_categories:
                    reversion_categories[category] = []
                reversion_categories[category].append(template.get('name'))

            for category, names in reversion_categories.items():
                print(f"      分类 {category}: {', '.join(names)}")
        else:
            print(f"   ❌ 获取均值回归模板失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {str(e)}")

if __name__ == "__main__":
    print("🎯 策略模板API测试")
    print("=" * 50)
    test_strategy_templates()
    print("\n" + "=" * 50)
    print("✨ 测试完成！")
