#!/usr/bin/env python3
"""
通知管理功能修复验证脚本
测试告警规则的创建、更新、删除和持久化功能
"""

import requests
import json
import time
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API基础URL
API_BASE_URL = "http://localhost:8000"

# 测试用户凭据
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

class NotificationTestSuite:
    def __init__(self):
        self.token = None
        self.test_rule_id = None
        self.issues_found = []

    def authenticate(self):
        """用户认证获取token"""
        logger.info("开始用户认证...")

        try:
            # 使用表单数据进行认证
            response = requests.post(
                f"{API_BASE_URL}/api/v1/auth/token",
                data=TEST_USER,  # 使用data而不是json
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                logger.info("用户认证成功")
                return True
            else:
                logger.error(f"用户认证失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"用户认证异常: {str(e)}")
            return False

    def get_headers(self):
        """获取请求头"""
        return {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }

    def test_create_rule(self):
        """测试创建告警规则"""
        logger.info("测试创建告警规则...")

        test_rule = {
            "name": "测试规则-持久化验证",
            "type": "price_change",
            "level": "warning",
            "description": "用于验证数据持久化的测试规则",
            "notify_channels": ["app", "email"],
            "conditions": {
                "direction": "up",
                "percentage": 5,
                "time_frame": 30
            },
            "enabled": True
        }

        try:
            response = requests.post(
                f"{API_BASE_URL}/api/v1/notifications/alert-rules",
                headers=self.get_headers(),
                json=test_rule,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.test_rule_id = data.get("rule_id")
                    logger.info(f"创建告警规则成功: {self.test_rule_id}")
                    return True
                else:
                    logger.error(f"创建告警规则失败: {data}")
                    self.issues_found.append("创建告警规则返回失败状态")
                    return False
            else:
                logger.error(f"创建告警规则失败: {response.status_code} - {response.text}")
                self.issues_found.append(f"创建告警规则HTTP错误: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"创建告警规则异常: {str(e)}")
            self.issues_found.append(f"创建告警规则异常: {str(e)}")
            return False

    def test_get_rules(self):
        """测试获取告警规则列表"""
        logger.info("测试获取告警规则列表...")

        try:
            response = requests.get(
                f"{API_BASE_URL}/api/v1/notifications/alert-rules",
                headers=self.get_headers(),
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                rules = data.get("items", [])
                logger.info(f"获取到 {len(rules)} 条告警规则")

                # 检查是否包含刚创建的规则
                if self.test_rule_id:
                    found_rule = any(rule.get("id") == self.test_rule_id for rule in rules)
                    if found_rule:
                        logger.info("成功找到刚创建的测试规则")
                        return True
                    else:
                        logger.error("未找到刚创建的测试规则")
                        self.issues_found.append("创建的规则未在列表中显示")
                        return False
                else:
                    return len(rules) >= 0  # 至少能获取到列表
            else:
                logger.error(f"获取告警规则失败: {response.status_code} - {response.text}")
                self.issues_found.append(f"获取告警规则HTTP错误: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"获取告警规则异常: {str(e)}")
            self.issues_found.append(f"获取告警规则异常: {str(e)}")
            return False

    def test_update_rule(self):
        """测试更新告警规则"""
        if not self.test_rule_id:
            logger.warning("没有测试规则ID，跳过更新测试")
            return False

        logger.info("测试更新告警规则...")

        updated_rule = {
            "id": self.test_rule_id,
            "name": "测试规则-已更新",
            "type": "price_change",
            "level": "error",
            "description": "已更新的测试规则",
            "notify_channels": ["app"],
            "conditions": {
                "direction": "down",
                "percentage": 10,
                "time_frame": 60
            },
            "enabled": True
        }

        try:
            response = requests.post(
                f"{API_BASE_URL}/api/v1/notifications/alert-rules/update",
                headers=self.get_headers(),
                json=updated_rule,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    logger.info("更新告警规则成功")
                    return True
                else:
                    logger.error(f"更新告警规则失败: {data}")
                    self.issues_found.append("更新告警规则返回失败状态")
                    return False
            else:
                logger.error(f"更新告警规则失败: {response.status_code} - {response.text}")
                self.issues_found.append(f"更新告警规则HTTP错误: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"更新告警规则异常: {str(e)}")
            self.issues_found.append(f"更新告警规则异常: {str(e)}")
            return False

    def test_delete_rule(self):
        """测试删除告警规则"""
        if not self.test_rule_id:
            logger.warning("没有测试规则ID，跳过删除测试")
            return False

        logger.info("测试删除告警规则...")

        try:
            response = requests.post(
                f"{API_BASE_URL}/api/v1/notifications/alert-rules/delete",
                headers=self.get_headers(),
                json={"id": self.test_rule_id},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    logger.info("删除告警规则成功")
                    return True
                else:
                    logger.error(f"删除告警规则失败: {data}")
                    self.issues_found.append("删除告警规则返回失败状态")
                    return False
            else:
                logger.error(f"删除告警规则失败: {response.status_code} - {response.text}")
                self.issues_found.append(f"删除告警规则HTTP错误: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"删除告警规则异常: {str(e)}")
            self.issues_found.append(f"删除告警规则异常: {str(e)}")
            return False

    def test_persistence_after_restart(self):
        """测试重启后的数据持久化（模拟）"""
        logger.info("测试数据持久化...")

        # 等待一段时间，模拟系统重启
        time.sleep(2)

        # 重新获取规则列表，检查数据是否持久化
        return self.test_get_rules()

    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行通知管理功能测试套件...")

        test_results = {}

        # 1. 用户认证
        test_results["authentication"] = self.authenticate()
        if not test_results["authentication"]:
            logger.error("用户认证失败，无法继续测试")
            return test_results

        # 2. 创建规则
        test_results["create_rule"] = self.test_create_rule()

        # 3. 获取规则列表
        test_results["get_rules"] = self.test_get_rules()

        # 4. 更新规则
        test_results["update_rule"] = self.test_update_rule()

        # 5. 数据持久化测试
        test_results["persistence"] = self.test_persistence_after_restart()

        # 6. 删除规则
        test_results["delete_rule"] = self.test_delete_rule()

        return test_results

    def print_summary(self, test_results):
        """打印测试结果摘要"""
        logger.info("=" * 60)
        logger.info("测试结果摘要")
        logger.info("=" * 60)

        passed = 0
        total = len(test_results)

        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if result:
                passed += 1

        logger.info("-" * 60)
        logger.info(f"总计: {passed}/{total} 个测试通过")

        if self.issues_found:
            logger.info("\n发现的问题:")
            for issue in self.issues_found:
                logger.info(f"- {issue}")

        if passed == total:
            logger.info("🎉 所有测试通过！通知管理功能修复成功！")
            return True
        else:
            logger.info("⚠️  部分测试失败，需要进一步修复")
            return False

def main():
    """主函数"""
    test_suite = NotificationTestSuite()

    try:
        # 运行测试
        results = test_suite.run_all_tests()

        # 打印结果
        success = test_suite.print_summary(results)

        # 返回适当的退出码
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
