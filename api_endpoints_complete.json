{"total_count": 163, "categories": {"认证相关": ["/api/v1/auth/login", "/api/v1/auth/logout", "/api/v1/auth/refresh", "/api/v1/auth/token"], "用户管理": ["/api/v1/notifications/ringtones/user", "/api/v1/user/profile", "/api/v1/user/settings", "/api/v1/users/list"], "数据管理": ["/api/v1/data/gaps", "/api/v1/data/ohlcv", "/api/v1/data/quality", "/api/v1/data/repair", "/api/v1/data/sources", "/api/v1/data/sources/<int:source_id>", "/api/v1/data/sources/<int:source_id>/gaps", "/api/v1/data/sources/<int:source_id>/quality", "/api/v1/data/symbols", "/api/v1/data/sync", "/api/v1/data/sync-tasks", "/api/v1/data/tasks/statistics", "/api/v1/data/ticker/<symbol>"], "策略管理": ["/api/v1/strategies", "/api/v1/strategies/<int:strategy_id>", "/api/v1/strategies/stats", "/api/v1/strategies/stats-old", "/api/v1/strategy", "/api/v1/strategy/<int:strategy_id>", "/api/v1/strategy/create", "/api/v1/strategy/delete", "/api/v1/strategy/list", "/api/v1/strategy/stats", "/api/v1/strategy/update"], "回测管理": ["/api/v1/backtest", "/api/v1/backtest-stats", "/api/v1/backtest/<int:backtest_id>", "/api/v1/backtest/create", "/api/v1/backtest/list", "/api/v1/backtest/recent", "/api/v1/backtest/results", "/api/v1/backtest/stats", "/api/v1/backtest/status", "/api/v1/backtests"], "交易管理": ["/api/v1/trading/balance", "/api/v1/trading/executions", "/api/v1/trading/history", "/api/v1/trading/orders", "/api/v1/trading/orders/<order_id>", "/api/v1/trading/orders/<order_id>/cancel", "/api/v1/trading/positions", "/api/v1/trading/status"], "风险管理": ["/api/v1/risk/alerts", "/api/v1/risk/analysis", "/api/v1/risk/limits", "/api/v1/risk/metrics"], "市场数据": ["/api/v1/market/combined-kline", "/api/v1/market/depth", "/api/v1/market/kline", "/api/v1/market/klines", "/api/v1/market/symbols", "/api/v1/market/ticker"], "通知系统": ["/api/v1/notifications/<int:notification_id>", "/api/v1/notifications/<int:notification_id>/read", "/api/v1/notifications/add-test-data", "/api/v1/notifications/alert-rules", "/api/v1/notifications/alert-rules/<string:rule_id>", "/api/v1/notifications/alert-rules/delete", "/api/v1/notifications/alert-rules/update", "/api/v1/notifications/alerts", "/api/v1/notifications/alerts/history", "/api/v1/notifications/email-settings", "/api/v1/notifications/important-event-types", "/api/v1/notifications/important-events", "/api/v1/notifications/list", "/api/v1/notifications/read-all", "/api/v1/notifications/recent", "/api/v1/notifications/ringtones", "/api/v1/notifications/ringtones/custom", "/api/v1/notifications/ringtones/default", "/api/v1/notifications/ringtones/user", "/api/v1/notifications/rules", "/api/v1/notifications/rules/<string:rule_id>", "/api/v1/notifications/rules/delete", "/api/v1/notifications/rules/update", "/api/v1/notifications/settings", "/api/v1/notifications/settings/email", "/api/v1/notifications/settings/sms", "/api/v1/notifications/settings/sound", "/api/v1/notifications/settings/system", "/api/v1/notifications/sms-settings", "/api/v1/notifications/sound-settings", "/api/v1/notifications/stats"], "性能监控": ["/api/v1/performance/analysis", "/api/v1/performance/api/endpoints", "/api/v1/performance/api/stats", "/api/v1/performance/api/test", "/api/v1/performance/cache/clear", "/api/v1/performance/cache/config", "/api/v1/performance/cache/stats", "/api/v1/performance/memory/analysis", "/api/v1/performance/memory/usage", "/api/v1/performance/metrics/<category>", "/api/v1/performance/metrics/api", "/api/v1/performance/metrics/cache", "/api/v1/performance/metrics/cpu", "/api/v1/performance/metrics/memory", "/api/v1/performance/monitoring/start", "/api/v1/performance/monitoring/status", "/api/v1/performance/monitoring/stop", "/api/v1/performance/process-large-dataframe", "/api/v1/performance/summary"], "系统配置": ["/api/v1/config/api-keys", "/api/v1/config/backups", "/api/v1/config/cache-size", "/api/v1/config/cleanup-data", "/api/v1/config/clear-cache", "/api/v1/config/data-maintenance", "/api/v1/config/database", "/api/v1/config/default-timeframes", "/api/v1/config/restore-backup", "/api/v1/config/system", "/api/v1/config/system-params", "/api/v1/config/test-api-connection", "/api/v1/config/timeframes/<int:source_id>", "/api/v1/config/trading"], "监控日志": ["/api/v1/monitoring/dashboard", "/api/v1/monitoring/logs", "/api/v1/monitoring/metrics", "/api/v1/monitoring/quality-report/list/<report_type>", "/api/v1/monitoring/status", "/api/v1/monitoring/visualization/3d-quality-data", "/api/v1/performance/monitoring/start", "/api/v1/performance/monitoring/status", "/api/v1/performance/monitoring/stop"], "健康检查": ["/api/v1/backtest/status", "/api/v1/health", "/api/v1/monitoring/status", "/api/v1/performance/monitoring/status", "/api/v1/status", "/api/v1/trading/status"]}, "all_endpoints": ["/api/v1/<path:path>", "/api/v1/alert-rules", "/api/v1/alert-rules-list", "/api/v1/alert-rules/<string:rule_id>", "/api/v1/alert-rules/delete", "/api/v1/alert-rules/update", "/api/v1/auth/login", "/api/v1/auth/logout", "/api/v1/auth/refresh", "/api/v1/auth/token", "/api/v1/backtest", "/api/v1/backtest-stats", "/api/v1/backtest/<int:backtest_id>", "/api/v1/backtest/create", "/api/v1/backtest/list", "/api/v1/backtest/recent", "/api/v1/backtest/results", "/api/v1/backtest/stats", "/api/v1/backtest/status", "/api/v1/backtests", "/api/v1/config/api-keys", "/api/v1/config/backups", "/api/v1/config/cache-size", "/api/v1/config/cleanup-data", "/api/v1/config/clear-cache", "/api/v1/config/data-maintenance", "/api/v1/config/database", "/api/v1/config/default-timeframes", "/api/v1/config/restore-backup", "/api/v1/config/system", "/api/v1/config/system-params", "/api/v1/config/test-api-connection", "/api/v1/config/timeframes/<int:source_id>", "/api/v1/config/trading", "/api/v1/data", "/api/v1/data-quality/reports/anomaly-detection", "/api/v1/data-quality/reports/content/<report_type>/<filename>", "/api/v1/data-quality/reports/cross-period-analysis", "/api/v1/data-quality/reports/download/<report_type>/<filename>", "/api/v1/data-quality/reports/email", "/api/v1/data-quality/reports/generate", "/api/v1/data-quality/reports/list/<report_type>", "/api/v1/data-sources", "/api/v1/data-sources/<int:source_id>", "/api/v1/data/gaps", "/api/v1/data/ohlcv", "/api/v1/data/quality", "/api/v1/data/repair", "/api/v1/data/sources", "/api/v1/data/sources/<int:source_id>", "/api/v1/data/sources/<int:source_id>/gaps", "/api/v1/data/sources/<int:source_id>/quality", "/api/v1/data/symbols", "/api/v1/data/sync", "/api/v1/data/sync-tasks", "/api/v1/data/tasks/statistics", "/api/v1/data/ticker/<symbol>", "/api/v1/debug/routes", "/api/v1/health", "/api/v1/market/combined-kline", "/api/v1/market/depth", "/api/v1/market/kline", "/api/v1/market/klines", "/api/v1/market/symbols", "/api/v1/market/ticker", "/api/v1/monitoring/dashboard", "/api/v1/monitoring/logs", "/api/v1/monitoring/metrics", "/api/v1/monitoring/quality-report/list/<report_type>", "/api/v1/monitoring/status", "/api/v1/monitoring/visualization/3d-quality-data", "/api/v1/notifications", "/api/v1/notifications/<int:notification_id>", "/api/v1/notifications/<int:notification_id>/read", "/api/v1/notifications/add-test-data", "/api/v1/notifications/alert-rules", "/api/v1/notifications/alert-rules/<string:rule_id>", "/api/v1/notifications/alert-rules/delete", "/api/v1/notifications/alert-rules/update", "/api/v1/notifications/alerts", "/api/v1/notifications/alerts/history", "/api/v1/notifications/email-settings", "/api/v1/notifications/important-event-types", "/api/v1/notifications/important-events", "/api/v1/notifications/list", "/api/v1/notifications/read-all", "/api/v1/notifications/recent", "/api/v1/notifications/ringtones", "/api/v1/notifications/ringtones/custom", "/api/v1/notifications/ringtones/default", "/api/v1/notifications/ringtones/user", "/api/v1/notifications/rules", "/api/v1/notifications/rules/<string:rule_id>", "/api/v1/notifications/rules/delete", "/api/v1/notifications/rules/update", "/api/v1/notifications/settings", "/api/v1/notifications/settings/email", "/api/v1/notifications/settings/sms", "/api/v1/notifications/settings/sound", "/api/v1/notifications/settings/system", "/api/v1/notifications/sms-settings", "/api/v1/notifications/sound-settings", "/api/v1/notifications/stats", "/api/v1/performance/analysis", "/api/v1/performance/api/endpoints", "/api/v1/performance/api/stats", "/api/v1/performance/api/test", "/api/v1/performance/cache/clear", "/api/v1/performance/cache/config", "/api/v1/performance/cache/stats", "/api/v1/performance/memory/analysis", "/api/v1/performance/memory/usage", "/api/v1/performance/metrics/<category>", "/api/v1/performance/metrics/api", "/api/v1/performance/metrics/cache", "/api/v1/performance/metrics/cpu", "/api/v1/performance/metrics/memory", "/api/v1/performance/monitoring/start", "/api/v1/performance/monitoring/status", "/api/v1/performance/monitoring/stop", "/api/v1/performance/process-large-dataframe", "/api/v1/performance/summary", "/api/v1/risk/alerts", "/api/v1/risk/analysis", "/api/v1/risk/limits", "/api/v1/risk/metrics", "/api/v1/signal", "/api/v1/signal/<int:signal_id>", "/api/v1/signal/<int:signal_id>/execute", "/api/v1/signal/realtime", "/api/v1/signal/stats", "/api/v1/signals", "/api/v1/signals/<int:signal_id>", "/api/v1/signals/<int:signal_id>/execute", "/api/v1/signals/realtime", "/api/v1/signals/stats", "/api/v1/status", "/api/v1/strategies", "/api/v1/strategies/<int:strategy_id>", "/api/v1/strategies/stats", "/api/v1/strategies/stats-old", "/api/v1/strategy", "/api/v1/strategy/<int:strategy_id>", "/api/v1/strategy/create", "/api/v1/strategy/delete", "/api/v1/strategy/list", "/api/v1/strategy/stats", "/api/v1/strategy/update", "/api/v1/sync-tasks", "/api/v1/sync-tasks/<int:task_id>", "/api/v1/test", "/api/v1/test-reports", "/api/v1/trading/balance", "/api/v1/trading/executions", "/api/v1/trading/history", "/api/v1/trading/orders", "/api/v1/trading/orders/<order_id>", "/api/v1/trading/orders/<order_id>/cancel", "/api/v1/trading/positions", "/api/v1/trading/status", "/api/v1/user/profile", "/api/v1/user/settings", "/api/v1/users/list"]}