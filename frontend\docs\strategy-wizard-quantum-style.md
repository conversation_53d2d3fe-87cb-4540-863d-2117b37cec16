# 策略创建向导 - 量子科技美学风格重构

## 概述

策略创建向导页面已经完全重构，采用了项目统一的"量子科技美学风格"，提供了更加现代化、科技感十足的用户体验。

## 主要特性

### 1. 量子科技美学设计
- **深空背景**：采用深蓝色渐变背景，营造科技感氛围
- **霓虹发光效果**：标题、按钮、边框都具有霓虹发光动画
- **量子步骤指示器**：动态发光的步骤进度条，支持脉冲动画
- **发光面板**：所有内容区域都采用发光边框设计

### 2. 响应式布局
- **网格布局**：表单采用响应式网格布局，适配不同屏幕尺寸
- **移动端优化**：在小屏幕设备上自动调整为单列布局
- **灵活间距**：组件间距根据屏幕大小自动调整

### 3. 交互体验优化
- **悬停效果**：所有可交互元素都有悬停发光效果
- **动画过渡**：平滑的颜色和位置过渡动画
- **视觉反馈**：按钮点击、输入框聚焦都有视觉反馈

### 4. 组件设计

#### 量子输入框
- 深色背景配合霓虹蓝边框
- 聚焦时发光效果增强
- 占位符文本半透明显示

#### 量子开关
- 自定义开关组件，符合量子风格
- 开启状态显示霓虹蓝发光
- 平滑的滑动动画

#### 量子单选按钮
- 重新设计的单选按钮样式
- 选中状态具有发光效果
- 模板选择卡片化设计

#### 量子标签页
- 自定义标签页组件
- 激活标签具有发光边框
- 平滑的切换动画

### 5. 步骤流程

#### 步骤1：策略类型选择
- **左侧表单区域**：策略基本信息输入
  - 策略名称（必填）
  - 策略描述
  - 策略类型选择
  - 策略分类选择
  - 代码类型选择（Python/Pine Script）

- **右侧模板区域**：策略模板选择
  - 可滚动的模板列表
  - 模板卡片化展示
  - 支持自定义策略选项

#### 步骤2：参数配置
- **策略参数标签页**：根据选择的模板动态生成参数表单
- **风险管理标签页**：止损止盈设置
- **交易时间标签页**：交易时间限制设置

#### 步骤3：代码编写
- 代码编辑器区域（待实现）
- 代码生成、格式化、验证功能

#### 步骤4：验证测试
- 代码验证功能（待实现）
- 错误和警告信息显示

#### 步骤5：保存部署
- 策略信息汇总
- 交易对选择
- 时间周期设置
- 激活状态设置

### 6. 技术实现

#### CSS 特性
- **CSS 变量**：使用CSS自定义属性管理颜色主题
- **Flexbox/Grid**：现代布局技术
- **动画关键帧**：自定义动画效果
- **媒体查询**：响应式设计

#### Vue.js 特性
- **组件化设计**：可复用的UI组件
- **响应式数据**：Vue的响应式系统
- **计算属性**：动态计算表单验证状态
- **生命周期钩子**：组件挂载时加载数据

#### 样式类名规范
- `.quantum-*`：量子风格组件类名
- `.neon-*`：霓虹发光效果类名
- `.glow-*`：发光效果类名

### 7. 颜色主题

#### 主要颜色
- **霓虹蓝**：`#00F7FF` - 主要强调色
- **霓虹粉**：`#FF00F7` - 次要强调色
- **深空蓝**：`rgba(6,21,46,0.9)` - 背景色
- **纯白**：`#FFFFFF` - 文本色

#### 透明度变化
- 背景元素使用不同透明度营造层次感
- 悬停状态增加透明度突出交互

### 8. 字体设计
- **主字体**：Orbitron - 科技感无衬线字体
- **辅助字体**：Rajdhani - 现代几何字体
- **字重变化**：不同重要级别使用不同字重

### 9. 动画效果

#### 发光动画
- 标题脉冲发光效果
- 边框呼吸发光效果
- 按钮悬停发光增强

#### 过渡动画
- 颜色过渡：0.3秒缓动
- 位置过渡：悬停上移效果
- 透明度过渡：聚焦状态变化

### 10. 可访问性
- **键盘导航**：支持Tab键导航
- **颜色对比**：确保文本可读性
- **语义化HTML**：正确的HTML结构

## 使用指南

### 访问路径
```
http://localhost:8080/#/strategy/wizard
```

### 操作流程
1. 填写策略基本信息
2. 选择策略类型和分类
3. 选择策略模板或自定义
4. 配置策略参数和风险管理
5. 编写或生成策略代码
6. 验证策略代码
7. 保存并部署策略

### 注意事项
- 必填字段标有红色星号
- 步骤间有依赖关系，需按顺序完成
- 表单验证会阻止无效的步骤跳转
- 所有设置都会实时保存到表单状态

## 后续优化计划

1. **代码编辑器集成**：集成Monaco Editor或CodeMirror
2. **实时预览**：策略代码实时预览功能
3. **模板管理**：更丰富的策略模板库
4. **参数验证**：更严格的参数验证规则
5. **保存草稿**：支持保存未完成的策略草稿
6. **导入导出**：支持策略配置的导入导出

## 技术栈

- **Vue.js 2.x**：前端框架
- **Element UI**：基础UI组件库（部分使用）
- **Sass/SCSS**：CSS预处理器
- **Webpack**：模块打包工具
- **Babel**：JavaScript编译器

## 文件结构

```
frontend/src/views/strategy/
├── StrategyWizard.vue          # 主组件文件
└── ...

frontend/src/styles/
├── variables.scss              # 样式变量
└── ...
```

---

*最后更新：2025年5月28日*
