from backend.app.db.database import SessionLocal
from backend.app.models.market_data import OHLCV
from backend.app.models.data import DataSource

# 创建数据库会话
db = SessionLocal()

try:
    # 获取OHLCV数据总数
    ohlcv_count = db.query(OHLCV).count()
    print(f'OHLCV数据总数: {ohlcv_count}')

    # 获取最新10条数据
    print('最新10条数据:')
    for record in db.query(OHLCV).order_by(OHLCV.timestamp.desc()).limit(10).all():
        print(f'{record.symbol} {record.timeframe} {record.timestamp} {record.close}')

    # 获取数据源信息
    print('\n数据源信息:')
    for source in db.query(DataSource).all():
        print(f'ID: {source.id}, 名称: {source.name}, 交易对: {source.symbol}, 时间周期: {source.timeframe}, 类型: {source.source_type}, 活跃: {source.is_active}')

finally:
    # 关闭数据库会话
    db.close()
