<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端显示验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #f0f9ff;
            border-left: 4px solid #10b981;
            color: #065f46;
        }
        .error {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
            color: #991b1b;
        }
        .warning {
            background-color: #fffbeb;
            border-left: 4px solid #f59e0b;
            color: #92400e;
        }
        .btn {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #2563eb;
        }
        .btn:disabled {
            background-color: #9ca3af;
            cursor: not-allowed;
        }
        .result-box {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
            border: 1px solid #e2e8f0;
        }
        .metric-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .metric-title {
            font-weight: bold;
            color: #374151;
            margin-bottom: 10px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .metric-details {
            font-size: 12px;
            color: #6b7280;
        }
        .normal { color: #10b981; }
        .warning { color: #f59e0b; }
        .critical { color: #ef4444; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 性能优化页面前端显示验证</h1>
        
        <div class="status warning">
            <strong>说明：</strong>此页面用于验证性能优化页面是否正确显示数据，而不是显示"加载中..."
        </div>

        <div>
            <button class="btn" onclick="checkFrontendDisplay()">🔍 检查前端显示</button>
            <button class="btn" onclick="openPerformancePage()">🚀 打开性能优化页面</button>
            <button class="btn" onclick="refreshAndCheck()">🔄 刷新并检查</button>
        </div>

        <div id="results"></div>

        <div id="metrics-display" class="metric-display" style="display: none;">
            <div class="metric-card">
                <div class="metric-title">CPU使用率</div>
                <div id="cpu-value" class="metric-value">-</div>
                <div class="metric-details">
                    <div>平均: <span id="cpu-avg">-</span></div>
                    <div>最大: <span id="cpu-max">-</span></div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">内存使用率</div>
                <div id="memory-value" class="metric-value">-</div>
                <div class="metric-details">
                    <div>平均: <span id="memory-avg">-</span></div>
                    <div>最大: <span id="memory-max">-</span></div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">API响应时间</div>
                <div id="api-value" class="metric-value">-</div>
                <div class="metric-details">
                    <div>平均: <span id="api-avg">-</span></div>
                    <div>最大: <span id="api-max">-</span></div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">缓存命中率</div>
                <div id="cache-value" class="metric-value">-</div>
                <div class="metric-details">
                    <div>平均: <span id="cache-avg">-</span></div>
                    <div>大小: <span id="cache-size">-</span></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let token = null;

        async function getAuthToken() {
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    token = data.access_token;
                    return token;
                } else {
                    throw new Error(`登录失败: ${response.status}`);
                }
            } catch (error) {
                throw new Error(`登录请求失败: ${error.message}`);
            }
        }

        async function fetchPerformanceData() {
            if (!token) {
                await getAuthToken();
            }

            const response = await fetch('http://localhost:8000/api/v1/performance/summary', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                return data.data;
            } else {
                throw new Error(`API请求失败: ${response.status}`);
            }
        }

        function showResult(message, type = 'success') {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function updateMetricsDisplay(data) {
            document.getElementById('metrics-display').style.display = 'grid';
            
            // CPU
            document.getElementById('cpu-value').textContent = data.cpu.current.toFixed(1) + '%';
            document.getElementById('cpu-avg').textContent = data.cpu.avg.toFixed(1) + '%';
            document.getElementById('cpu-max').textContent = data.cpu.max.toFixed(1) + '%';
            
            // Memory
            document.getElementById('memory-value').textContent = data.memory.current.toFixed(1) + '%';
            document.getElementById('memory-avg').textContent = data.memory.avg.toFixed(1) + '%';
            document.getElementById('memory-max').textContent = data.memory.max.toFixed(1) + '%';
            
            // API
            document.getElementById('api-value').textContent = data.api.current_avg.toFixed(3) + 's';
            document.getElementById('api-avg').textContent = data.api.overall_avg.toFixed(3) + 's';
            document.getElementById('api-max').textContent = data.api.max.toFixed(3) + 's';
            
            // Cache
            document.getElementById('cache-value').textContent = (data.cache.current_hit_rate * 100).toFixed(1) + '%';
            document.getElementById('cache-avg').textContent = (data.cache.avg_hit_rate * 100).toFixed(1) + '%';
            document.getElementById('cache-size').textContent = data.cache.size + '/' + data.cache.max_size;
            
            // 设置颜色
            const cpuValue = document.getElementById('cpu-value');
            const memoryValue = document.getElementById('memory-value');
            const apiValue = document.getElementById('api-value');
            const cacheValue = document.getElementById('cache-value');
            
            // CPU颜色
            if (data.cpu.current > 90) cpuValue.className = 'metric-value critical';
            else if (data.cpu.current > 70) cpuValue.className = 'metric-value warning';
            else cpuValue.className = 'metric-value normal';
            
            // Memory颜色
            if (data.memory.current > 90) memoryValue.className = 'metric-value critical';
            else if (data.memory.current > 80) memoryValue.className = 'metric-value warning';
            else memoryValue.className = 'metric-value normal';
            
            // API颜色
            if (data.api.current_avg > 1.0) apiValue.className = 'metric-value critical';
            else if (data.api.current_avg > 0.5) apiValue.className = 'metric-value warning';
            else apiValue.className = 'metric-value normal';
            
            // Cache颜色
            if (data.cache.current_hit_rate < 0.3) cacheValue.className = 'metric-value critical';
            else if (data.cache.current_hit_rate < 0.5) cacheValue.className = 'metric-value warning';
            else cacheValue.className = 'metric-value normal';
        }

        async function checkFrontendDisplay() {
            try {
                showResult('🔄 正在检查前端显示...', 'warning');
                
                // 获取性能数据
                const data = await fetchPerformanceData();
                
                // 更新显示
                updateMetricsDisplay(data);
                
                // 检查数据是否有效
                const hasValidData = 
                    data.cpu && typeof data.cpu.current === 'number' &&
                    data.memory && typeof data.memory.current === 'number' &&
                    data.api && typeof data.api.current_avg === 'number' &&
                    data.cache && typeof data.cache.current_hit_rate === 'number';
                
                if (hasValidData) {
                    showResult(`
                        ✅ 前端显示验证成功！<br>
                        📊 数据格式正确，所有指标都是有效数字<br>
                        🎉 性能优化页面应该能正确显示数据，而不是"加载中..."<br>
                        <br>
                        <strong>当前数据：</strong><br>
                        • CPU: ${data.cpu.current.toFixed(1)}%<br>
                        • 内存: ${data.memory.current.toFixed(1)}%<br>
                        • API响应: ${data.api.current_avg.toFixed(3)}s<br>
                        • 缓存命中率: ${(data.cache.current_hit_rate * 100).toFixed(1)}%
                    `, 'success');
                } else {
                    showResult('❌ 数据格式有问题，某些字段不是有效数字', 'error');
                }
                
            } catch (error) {
                showResult(`❌ 检查失败: ${error.message}`, 'error');
            }
        }

        function openPerformancePage() {
            window.open('http://localhost:8080/#/performance', '_blank');
            showResult('🚀 已打开性能优化页面，请检查是否显示真实数据而不是"加载中..."', 'warning');
        }

        async function refreshAndCheck() {
            openPerformancePage();
            await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
            await checkFrontendDisplay();
        }

        // 页面加载时自动检查
        window.onload = function() {
            setTimeout(checkFrontendDisplay, 1000);
        };
    </script>
</body>
</html>
