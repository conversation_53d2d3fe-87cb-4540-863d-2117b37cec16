#!/usr/bin/env python3
"""
创建测试策略数据
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.simple_api import get_db, Strategy
from datetime import datetime

def create_test_strategies():
    """创建测试策略数据"""
    db = get_db()
    try:
        # 检查是否已有策略数据
        existing_count = db.query(Strategy).count()
        if existing_count > 0:
            print(f"数据库中已有 {existing_count} 个策略")
            return
        
        print("创建测试策略数据...")
        
        # 创建测试策略
        strategies = [
            Strategy(
                name="双均线交叉策略",
                description="基于快慢均线交叉的经典趋势跟踪策略，适用于趋势明显的市场环境",
                code="""def initialize(context):
    context.fast_ma = 10
    context.slow_ma = 30
    context.symbol = 'BTCUSDT'

def handle_bar(context, data):
    fast_ma = data.history(context.symbol, 'close', context.fast_ma).mean()
    slow_ma = data.history(context.symbol, 'close', context.slow_ma).mean()
    
    if fast_ma > slow_ma:
        order_target_percent(context.symbol, 1.0)
    else:
        order_target_percent(context.symbol, 0.0)""",
                is_active=True,
                creator_id=1
            ),
            Strategy(
                name="MACD策略",
                description="基于MACD指标的动量策略，通过MACD线与信号线的交叉判断买卖时机",
                code="""def initialize(context):
    context.symbol = 'BTCUSDT'
    context.fast_period = 12
    context.slow_period = 26
    context.signal_period = 9

def handle_bar(context, data):
    prices = data.history(context.symbol, 'close', 50)
    
    # 计算MACD
    ema_fast = prices.ewm(span=context.fast_period).mean()
    ema_slow = prices.ewm(span=context.slow_period).mean()
    macd = ema_fast - ema_slow
    signal = macd.ewm(span=context.signal_period).mean()
    
    if macd.iloc[-1] > signal.iloc[-1] and macd.iloc[-2] <= signal.iloc[-2]:
        order_target_percent(context.symbol, 1.0)
    elif macd.iloc[-1] < signal.iloc[-1] and macd.iloc[-2] >= signal.iloc[-2]:
        order_target_percent(context.symbol, 0.0)""",
                is_active=True,
                creator_id=1
            ),
            Strategy(
                name="RSI超买超卖策略",
                description="基于RSI指标的反转策略，在超买超卖区域寻找反转机会",
                code="""def initialize(context):
    context.symbol = 'BTCUSDT'
    context.rsi_period = 14
    context.oversold = 30
    context.overbought = 70

def handle_bar(context, data):
    prices = data.history(context.symbol, 'close', context.rsi_period + 1)
    
    # 计算RSI
    delta = prices.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=context.rsi_period).mean()
    avg_loss = loss.rolling(window=context.rsi_period).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    current_rsi = rsi.iloc[-1]
    
    if current_rsi < context.oversold:
        order_target_percent(context.symbol, 1.0)
    elif current_rsi > context.overbought:
        order_target_percent(context.symbol, 0.0)""",
                is_active=True,
                creator_id=1
            ),
            Strategy(
                name="布林带策略",
                description="基于布林带的均值回归策略，在价格触及布林带边界时进行交易",
                code="""def initialize(context):
    context.symbol = 'BTCUSDT'
    context.period = 20
    context.std_dev = 2

def handle_bar(context, data):
    prices = data.history(context.symbol, 'close', context.period)
    
    # 计算布林带
    sma = prices.mean()
    std = prices.std()
    upper_band = sma + (std * context.std_dev)
    lower_band = sma - (std * context.std_dev)
    
    current_price = prices.iloc[-1]
    
    if current_price <= lower_band:
        order_target_percent(context.symbol, 1.0)
    elif current_price >= upper_band:
        order_target_percent(context.symbol, 0.0)""",
                is_active=True,
                creator_id=1
            ),
            Strategy(
                name="网格交易策略",
                description="在震荡市场中通过网格交易获取收益的策略",
                code="""def initialize(context):
    context.symbol = 'BTCUSDT'
    context.grid_size = 0.02  # 2%网格
    context.max_positions = 5

def handle_bar(context, data):
    current_price = data.current(context.symbol, 'close')
    
    # 网格交易逻辑
    # 这里简化实现，实际需要更复杂的网格管理
    pass""",
                is_active=False,
                creator_id=1
            )
        ]
        
        for strategy in strategies:
            db.add(strategy)
        
        db.commit()
        print(f"成功创建 {len(strategies)} 个测试策略")
        
        # 验证创建结果
        total_count = db.query(Strategy).count()
        print(f"数据库中现在有 {total_count} 个策略")
        
    except Exception as e:
        print(f"创建策略失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_strategies()
