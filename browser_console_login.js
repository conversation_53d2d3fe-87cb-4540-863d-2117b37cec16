// 在浏览器控制台中执行此代码来完成登录
// 使用方法：
// 1. 打开浏览器开发者工具（F12）
// 2. 切换到Console（控制台）标签
// 3. 复制粘贴下面的代码并按回车执行

(async function autoLogin() {
    console.log('🚀 开始自动登录流程...');
    
    try {
        // 步骤1: 清除旧的认证信息
        console.log('📝 清除旧的认证信息...');
        localStorage.removeItem('token');
        localStorage.removeItem('userRole');
        localStorage.removeItem('userInfo');
        
        // 步骤2: 发送登录请求
        console.log('🔐 发送登录请求...');
        const loginResponse = await fetch('http://localhost:8000/api/v1/auth/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123'
            })
        });
        
        if (!loginResponse.ok) {
            throw new Error(`登录请求失败: ${loginResponse.status} ${loginResponse.statusText}`);
        }
        
        const loginData = await loginResponse.json();
        console.log('✅ 登录响应:', loginData);
        
        if (!loginData.access_token) {
            throw new Error('登录响应中没有access_token');
        }
        
        // 步骤3: 存储认证信息
        console.log('💾 存储认证信息...');
        localStorage.setItem('token', loginData.access_token);
        localStorage.setItem('userRole', 'admin');
        localStorage.setItem('userInfo', JSON.stringify({
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            role: 'admin'
        }));
        
        // 步骤4: 验证认证是否有效
        console.log('🔍 验证认证状态...');
        const testResponse = await fetch('http://localhost:8000/api/v1/performance/summary', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${loginData.access_token}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });
        
        if (!testResponse.ok) {
            throw new Error(`API测试失败: ${testResponse.status} ${testResponse.statusText}`);
        }
        
        const testData = await testResponse.json();
        console.log('✅ API测试成功:', testData);
        
        // 步骤5: 刷新页面或跳转到性能优化页面
        console.log('🎉 登录成功！正在跳转到性能优化页面...');
        
        // 如果当前在登录页面，跳转到性能优化页面
        if (window.location.hash === '#/login' || window.location.pathname === '/login') {
            window.location.hash = '#/performance';
        } else if (window.location.hash !== '#/performance') {
            // 如果不在性能优化页面，询问是否跳转
            if (confirm('登录成功！是否跳转到性能优化页面？')) {
                window.location.hash = '#/performance';
            } else {
                // 刷新当前页面
                window.location.reload();
            }
        } else {
            // 已经在性能优化页面，刷新页面
            window.location.reload();
        }
        
        console.log('✨ 自动登录流程完成！');
        
    } catch (error) {
        console.error('❌ 自动登录失败:', error);
        alert(`自动登录失败: ${error.message}\n\n请检查：\n1. 后端服务是否运行在 http://localhost:8000\n2. 网络连接是否正常\n3. 用户名密码是否正确`);
    }
})();

// 额外的辅助函数
window.checkAuthStatus = function() {
    console.log('🔍 检查当前认证状态...');
    
    const token = localStorage.getItem('token');
    const userRole = localStorage.getItem('userRole');
    const userInfo = localStorage.getItem('userInfo');
    
    console.log('Token:', token ? '✅ 存在' : '❌ 不存在');
    console.log('User Role:', userRole || '❌ 不存在');
    console.log('User Info:', userInfo ? JSON.parse(userInfo) : '❌ 不存在');
    
    if (token) {
        console.log('🎯 可以尝试访问性能优化页面');
        return true;
    } else {
        console.log('⚠️ 需要先登录');
        return false;
    }
};

window.testPerformanceAPI = async function() {
    console.log('🧪 测试性能API...');
    
    const token = localStorage.getItem('token');
    if (!token) {
        console.error('❌ 没有找到认证token，请先登录');
        return;
    }
    
    try {
        const response = await fetch('http://localhost:8000/api/v1/performance/summary', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('✅ 性能API测试成功:', data);
        return data;
        
    } catch (error) {
        console.error('❌ 性能API测试失败:', error);
        throw error;
    }
};

window.goToPerformancePage = function() {
    console.log('🎯 跳转到性能优化页面...');
    window.location.hash = '#/performance';
};

console.log(`
🎯 自动登录工具已加载！

可用命令：
- checkAuthStatus()     - 检查当前认证状态
- testPerformanceAPI()  - 测试性能API
- goToPerformancePage() - 跳转到性能优化页面

如果自动登录失败，请手动执行上面的代码。
`);
