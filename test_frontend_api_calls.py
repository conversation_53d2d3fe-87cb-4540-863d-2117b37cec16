#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端API调用验证测试脚本
"""

import requests
import json

def test_frontend_api_calls():
    """测试前端API调用是否正常"""
    print("=" * 60)
    print("🎯 前端API调用验证测试")
    print("=" * 60)
    
    # 模拟前端的API调用
    base_url = "http://localhost:8000"
    
    try:
        print("1. 测试用户列表API调用...")
        # 模拟前端发送的请求
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        response = requests.get(f"{base_url}/account/users?page=1&limit=10", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 用户列表API调用成功")
            print(f"   响应数据结构: {list(data.keys())}")
            
            if 'data' in data and 'users' in data['data']:
                print(f"   用户数据路径: data.users (包含 {len(data['data']['users'])} 个用户)")
                print("   ✅ 前端应该使用 response.data.users 访问用户列表")
            else:
                print("   ❌ 响应数据结构异常")
                return False
        else:
            print(f"❌ 用户列表API调用失败: {response.status_code}")
            return False
        
        print("\n2. 测试角色列表API调用...")
        response = requests.get(f"{base_url}/account/roles", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 角色列表API调用成功")
            print(f"   响应数据结构: {list(data.keys())}")
            
            if 'data' in data and 'roles' in data['data']:
                print(f"   角色数据路径: data.roles (包含 {len(data['data']['roles'])} 个角色)")
                print("   ✅ 前端应该使用 response.data.roles 访问角色列表")
            else:
                print("   ❌ 响应数据结构异常")
                return False
        else:
            print(f"❌ 角色列表API调用失败: {response.status_code}")
            return False
        
        print("\n3. 测试创建用户API调用...")
        create_data = {
            "username": "test_frontend_user",
            "email": "<EMAIL>",
            "fullName": "前端测试用户",
            "password": "password123",
            "role": "user",
            "status": "active"
        }
        
        response = requests.post(f"{base_url}/account/users", json=create_data, headers=headers)
        
        if response.status_code == 201:
            data = response.json()
            print("✅ 创建用户API调用成功")
            print(f"   响应数据结构: {list(data.keys())}")
            
            if 'data' in data:
                print("   ✅ 前端应该使用 response.data 访问创建的用户信息")
                user_id = data['data']['id']
            else:
                print("   ❌ 响应数据结构异常")
                return False
        else:
            print(f"❌ 创建用户API调用失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
        
        print("\n4. 测试获取单个用户API调用...")
        response = requests.get(f"{base_url}/account/users/{user_id}", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取单个用户API调用成功")
            print(f"   响应数据结构: {list(data.keys())}")
            
            if 'data' in data:
                print("   ✅ 前端应该使用 response.data 访问用户详细信息")
            else:
                print("   ❌ 响应数据结构异常")
                return False
        else:
            print(f"❌ 获取单个用户API调用失败: {response.status_code}")
            return False
        
        print("\n5. 清理测试数据...")
        response = requests.delete(f"{base_url}/account/users/{user_id}", headers=headers)
        
        if response.status_code == 200:
            print("✅ 测试数据清理成功")
        else:
            print(f"⚠️  测试数据清理失败: {response.status_code}")
        
        print("\n" + "=" * 60)
        print("🎉 前端API调用验证测试通过！")
        print("✅ 所有API端点正常响应")
        print("✅ 响应数据结构符合预期")
        print("✅ 前端数据访问路径正确")
        print("\n📋 前端数据访问总结:")
        print("   - 用户列表: response.data.users")
        print("   - 角色列表: response.data.roles")
        print("   - 用户详情: response.data")
        print("   - 创建用户: response.data")
        print("   - 更新用户: response.data")
        print("\n🚀 前端用户管理页面应该能正常显示和操作数据！")
        print("=" * 60)
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 无法连接到后端服务")
        print("请确保后端服务正在运行在端口8000")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    test_frontend_api_calls()
