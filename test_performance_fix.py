#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能优化修复验证脚本
测试所有性能API端点是否正常工作
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:8000"
USERNAME = "admin"
PASSWORD = "admin123"

def get_auth_token():
    """获取认证令牌"""
    try:
        response = requests.post(f"{BASE_URL}/api/v1/auth/token", json={
            "username": USERNAME,
            "password": PASSWORD
        })

        if response.status_code == 200:
            data = response.json()
            return data.get("access_token")
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_api_endpoint(endpoint, token, method="GET", data=None):
    """测试API端点"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    try:
        if method == "GET":
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
        elif method == "POST":
            response = requests.post(f"{BASE_URL}{endpoint}", headers=headers, json=data)

        print(f"\n{'='*60}")
        print(f"测试端点: {method} {endpoint}")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"响应成功: {result.get('message', '无消息')}")
                if 'data' in result:
                    print(f"数据类型: {type(result['data'])}")
                    if isinstance(result['data'], dict):
                        print(f"数据键: {list(result['data'].keys())}")
                    elif isinstance(result['data'], list):
                        print(f"数据长度: {len(result['data'])}")
                return True
            except json.JSONDecodeError:
                print(f"响应不是有效的JSON: {response.text[:200]}")
                return False
        else:
            print(f"请求失败: {response.text}")
            return False

    except Exception as e:
        print(f"请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始性能优化API修复验证...")

    # 获取认证令牌
    print("\n1. 获取认证令牌...")
    token = get_auth_token()
    if not token:
        print("无法获取认证令牌，测试终止")
        return

    print(f"认证令牌获取成功: {token[:20]}...")

    # 测试端点列表
    test_endpoints = [
        # 基础性能API
        "/api/v1/performance/summary",
        "/api/v1/performance/analysis",
        "/api/v1/performance/cache/stats",
        "/api/v1/performance/api/stats",
        "/api/v1/performance/api/endpoints",
        "/api/v1/performance/process-large-dataframe",
        "/api/v1/performance/memory/usage",
        "/api/v1/performance/memory/analysis",

        # Metrics API (这些是新添加的)
        "/api/v1/performance/metrics/cpu?limit=10",
        "/api/v1/performance/metrics/memory?limit=10",
        "/api/v1/performance/metrics/api?limit=10",
        "/api/v1/performance/metrics/cache?limit=10",
    ]

    # 执行测试
    print(f"\n2. 测试 {len(test_endpoints)} 个API端点...")
    success_count = 0

    for endpoint in test_endpoints:
        if test_api_endpoint(endpoint, token):
            success_count += 1
        time.sleep(0.5)  # 避免请求过快

    # 测试POST端点
    print(f"\n3. 测试POST端点...")
    post_endpoints = [
        ("/api/v1/performance/cache/clear", None),
        ("/api/v1/performance/cache/config", {"max_memory": "100MB", "ttl": 300})
    ]

    for endpoint, data in post_endpoints:
        if test_api_endpoint(endpoint, token, "POST", data):
            success_count += 1
        time.sleep(0.5)

    # 输出测试结果
    total_tests = len(test_endpoints) + len(post_endpoints)
    print(f"\n{'='*60}")
    print(f"测试完成!")
    print(f"总测试数: {total_tests}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_tests - success_count}")
    print(f"成功率: {(success_count/total_tests)*100:.1f}%")

    if success_count == total_tests:
        print("\n✅ 所有API端点测试通过！性能优化修复成功！")
    else:
        print(f"\n❌ 有 {total_tests - success_count} 个端点测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
