#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化功能完整测试脚本
测试所有策略优化相关的API和功能
"""

import requests
import json
import time

def test_strategy_optimization_complete():
    """完整测试策略优化功能"""
    print("开始完整测试策略优化功能...")
    print("=" * 80)
    
    base_url = "http://localhost:8080/api/v1"
    
    # 测试1: 策略列表API
    print("1. 测试策略列表API...")
    try:
        response = requests.get(f"{base_url}/strategy", timeout=10)
        if response.status_code == 200:
            strategies = response.json()
            print(f"✓ 策略列表API正常，返回 {len(strategies)} 个策略")
            if strategies:
                strategy_id = strategies[0]['id']
                strategy_name = strategies[0]['name']
                print(f"  选择策略: ID={strategy_id}, 名称={strategy_name}")
                
                # 显示所有策略
                for i, strategy in enumerate(strategies, 1):
                    print(f"    策略{i}: ID={strategy['id']}, 名称={strategy['name']}")
            else:
                print("✗ 策略列表为空")
                return False
        else:
            print(f"✗ 策略列表API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 策略列表API异常: {str(e)}")
        return False
    
    # 测试2: 策略绩效API
    print(f"\n2. 测试策略绩效API (策略ID: {strategy_id})...")
    try:
        response = requests.get(f"{base_url}/strategies/{strategy_id}/performance", timeout=10)
        if response.status_code == 200:
            performance_data = response.json()
            print("✓ 策略绩效API正常")
            print(f"  策略名称: {performance_data.get('name', 'N/A')}")
            
            # 检查核心指标
            metrics = performance_data.get('metrics', {})
            if metrics:
                print("  核心指标:")
                print(f"    年化收益率: {metrics.get('annualized_return', 'N/A')}%")
                print(f"    夏普比率: {metrics.get('sharpe_ratio', 'N/A')}")
                print(f"    最大回撤: {metrics.get('max_drawdown', 'N/A')}%")
                print(f"    胜率: {metrics.get('win_rate', 'N/A')}%")
            
            # 检查月度收益数据
            monthly_returns = performance_data.get('monthly_returns', [])
            if monthly_returns:
                print(f"  月度收益数据: {len(monthly_returns)} 个月")
                for month_data in monthly_returns[:3]:  # 显示前3个月
                    print(f"    {month_data['month']}: {month_data['return']}%")
            
            # 检查风险指标
            risk_metrics = performance_data.get('risk_metrics', {})
            if risk_metrics:
                print("  风险指标:")
                print(f"    波动率: {risk_metrics.get('volatility', 'N/A')}%")
                print(f"    VaR: {risk_metrics.get('var', 'N/A')}%")
                print(f"    Beta: {risk_metrics.get('beta', 'N/A')}")
        else:
            print(f"✗ 策略绩效API失败: {response.status_code}")
            print(f"  响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"✗ 策略绩效API异常: {str(e)}")
        return False
    
    # 测试3: 策略优化建议API
    print(f"\n3. 测试策略优化建议API (策略ID: {strategy_id})...")
    try:
        response = requests.get(f"{base_url}/strategies/{strategy_id}/optimization", timeout=10)
        if response.status_code == 200:
            optimization_data = response.json()
            print("✓ 策略优化建议API正常")
            print(f"  策略名称: {optimization_data.get('name', 'N/A')}")
            
            # 检查当前绩效
            current_performance = optimization_data.get('current_performance', {})
            if current_performance:
                print("  当前绩效:")
                print(f"    年化收益率: {current_performance.get('annualized_return', 'N/A')}%")
                print(f"    夏普比率: {current_performance.get('sharpe_ratio', 'N/A')}")
                print(f"    最大回撤: {current_performance.get('max_drawdown', 'N/A')}%")
            
            # 检查优化建议
            suggestions = optimization_data.get('optimization_suggestions', [])
            if suggestions:
                print(f"  优化建议数量: {len(suggestions)}")
                for i, suggestion in enumerate(suggestions, 1):
                    print(f"    建议{i}: {suggestion.get('parameter', 'N/A')}")
                    print(f"      当前值: {suggestion.get('current_value', 'N/A')}")
                    print(f"      建议值: {suggestion.get('suggested_value', 'N/A')}")
                    print(f"      理由: {suggestion.get('reason', 'N/A')}")
                    
                    # 检查预期改进
                    expected_improvement = suggestion.get('expected_improvement', {})
                    if expected_improvement:
                        print(f"      预期改进:")
                        print(f"        年化收益率: {expected_improvement.get('annualized_return', 'N/A')}%")
                        print(f"        夏普比率: {expected_improvement.get('sharpe_ratio', 'N/A')}")
                        print(f"        最大回撤: {expected_improvement.get('max_drawdown', 'N/A')}%")
            
            # 检查潜在改进
            potential_improvements = optimization_data.get('potential_improvements', {})
            if potential_improvements:
                print("  潜在改进:")
                print(f"    收益提升: {potential_improvements.get('return_increase', 'N/A')}%")
                print(f"    风险降低: {potential_improvements.get('risk_reduction', 'N/A')}%")
                print(f"    夏普改进: {potential_improvements.get('sharpe_improvement', 'N/A')}")
        else:
            print(f"✗ 策略优化建议API失败: {response.status_code}")
            print(f"  响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"✗ 策略优化建议API异常: {str(e)}")
        return False
    
    # 测试4: 策略优化应用API
    print(f"\n4. 测试策略优化应用API (策略ID: {strategy_id})...")
    try:
        # 使用第一个优化建议进行测试
        if suggestions:
            first_suggestion = suggestions[0]
            optimization_params = {
                "parameter": first_suggestion.get('parameter'),
                "value": first_suggestion.get('suggested_value')
            }
            
            print(f"  应用优化参数: {optimization_params}")
            
            response = requests.post(
                f"{base_url}/strategies/{strategy_id}/optimize",
                json=optimization_params,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                apply_result = response.json()
                print("✓ 策略优化应用API正常")
                print(f"  应用结果: {apply_result}")
                
                if apply_result.get('success'):
                    print("  ✓ 优化参数应用成功")
                    print(f"    消息: {apply_result.get('message', 'N/A')}")
                    
                    # 检查更新后的参数
                    updated_params = apply_result.get('updated_parameters', {})
                    if updated_params:
                        print("    更新后的参数:")
                        for param, value in updated_params.items():
                            print(f"      {param}: {value}")
                else:
                    print("  ⚠ 优化参数应用失败")
                    print(f"    错误: {apply_result.get('error', 'N/A')}")
            else:
                print(f"✗ 策略优化应用API失败: {response.status_code}")
                print(f"  响应内容: {response.text}")
                return False
        else:
            print("  ⚠ 没有优化建议可供测试")
    except Exception as e:
        print(f"✗ 策略优化应用API异常: {str(e)}")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 策略优化功能完整测试完成！")
    print("✅ 策略列表加载正常")
    print("✅ 策略绩效数据完整")
    print("✅ 策略优化建议详细")
    print("✅ 策略优化应用功能正常")
    print("✅ 所有API端点都正常工作")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    success = test_strategy_optimization_complete()
    if not success:
        print("\n❌ 测试失败，请检查相关问题")
        exit(1)
    else:
        print("\n✅ 测试成功，策略优化功能完全正常")
