#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import time
from datetime import datetime, timedelta
import os
import json
import random
from typing import List, Optional, Dict, Any
from threading import Thread
from flask import jsonify, request
from sqlalchemy.orm import Session

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backtest_api_module.log')
    ]
)
logger = logging.getLogger(__name__)

# 模拟回测数据
BACKTEST_RESULTS = [
    {
        "id": 1,
        "strategy_name": "突破策略",
        "start_date": "2024-01-01",
        "end_date": "2024-02-01",
        "initial_capital": 10000,
        "final_capital": 12500,
        "profit_loss": 2500,
        "profit_loss_percent": 25.0,
        "max_drawdown": 5.2,
        "sharpe_ratio": 1.8,
        "created_at": "2025-02-15T10:30:00",
        "status": "completed"
    },
    {
        "id": 2,
        "strategy_name": "趋势跟踪",
        "start_date": "2024-01-15",
        "end_date": "2024-02-15",
        "initial_capital": 20000,
        "final_capital": 24000,
        "profit_loss": 4000,
        "profit_loss_percent": 20.0,
        "max_drawdown": 8.1,
        "sharpe_ratio": 1.5,
        "created_at": "2025-02-20T14:15:00",
        "status": "completed"
    }
]

# 模拟策略数据
STRATEGIES = [
    {
        "id": 1,
        "name": "双均线交叉策略",
        "description": "基于快慢双均线交叉的趋势跟踪策略",
        "created_at": "2025-01-15T10:30:00",
        "updated_at": "2025-01-15T10:30:00",
        "is_active": True,
        "backtest_count": 5,
        "avg_return": 18.5,
        "parameters": {
            "short_period": 5,
            "long_period": 20
        }
    },
    {
        "id": 2,
        "name": "RSI超买超卖策略",
        "description": "基于RSI指标的均值回归策略",
        "created_at": "2025-01-20T14:15:00",
        "updated_at": "2025-01-20T14:15:00",
        "is_active": True,
        "backtest_count": 3,
        "avg_return": 12.3,
        "parameters": {
            "rsi_period": 14,
            "overbought": 70,
            "oversold": 30
        }
    }
]

# 错误处理装饰器
def handle_exception(func):
    """错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"API错误: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500
    wrapper.__name__ = func.__name__
    return wrapper

# 后台运行回测任务
def run_backtest(backtest_id, data, db):
    """后台运行回测任务"""
    try:
        logger.info(f"开始运行回测任务: {backtest_id}")

        # 更新回测状态为运行中
        from backend.app.models.backtest import BacktestResult
        backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
        if not backtest:
            logger.error(f"未找到回测记录: {backtest_id}")
            return

        backtest.status = "running"
        db.commit()

        # 模拟回测运行时间
        import time
        time.sleep(5)  # 模拟回测运行时间

        # 生成模拟回测结果
        import random

        # 生成模拟交易记录
        trades = []
        start_date = datetime.fromisoformat(data["start_date"].replace('Z', '+00:00'))
        end_date = datetime.fromisoformat(data["end_date"].replace('Z', '+00:00'))
        days_range = (end_date - start_date).days

        # 生成随机交易日期
        trade_dates = [start_date + timedelta(days=random.randint(1, days_range-1)) for _ in range(10)]
        trade_dates.sort()

        # 生成交易记录
        for i, trade_date in enumerate(trade_dates):
            trade_type = "buy" if i % 2 == 0 else "sell"
            price = 50000 + random.uniform(-5000, 5000)
            amount = round(random.uniform(0.1, 1.0), 2)

            trades.append({
                "time": trade_date.isoformat(),
                "type": trade_type,
                "price": price,
                "amount": amount
            })

        # 生成资金曲线数据
        equity_curve = []
        days = (end_date - start_date).days
        initial_capital = data.get("initial_capital", 10000)
        final_capital = initial_capital * (1 + random.uniform(0.1, 0.3))  # 模拟10-30%的收益

        # 生成每天的资金数据
        for i in range(days + 1):
            current_date = start_date + timedelta(days=i)
            # 线性增长加随机波动
            progress = i / days if days > 0 else 0
            capital = initial_capital + (final_capital - initial_capital) * progress
            capital += random.uniform(-500, 500)  # 添加随机波动

            equity_curve.append([int(current_date.timestamp() * 1000), round(capital, 2)])

        # 计算收益率和其他指标
        profit = final_capital - initial_capital
        profit_percent = (profit / initial_capital) * 100
        max_drawdown = random.uniform(5, 15)  # 模拟5-15%的最大回撤
        sharpe_ratio = random.uniform(1.0, 2.5)  # 模拟复合收益率

        # 更新回测结果
        backtest.status = "completed"
        backtest.final_capital = final_capital
        backtest.total_return = profit_percent / 100  # 将百分比转换为小数
        backtest.annual_return = (profit_percent / 100) * (365 / days) if days > 0 else 0
        backtest.max_drawdown = max_drawdown / 100  # 将百分比转换为小数
        backtest.sharpe_ratio = sharpe_ratio
        backtest.win_rate = random.uniform(0.5, 0.7)  # 模拟50-70%的胜率
        backtest.profit_factor = random.uniform(1.5, 2.5)  # 模拟盈亏比
        backtest.trades_count = len(trades)

        # 将详细结果保存为JSON
        backtest.result_data = {
            "trades": trades,
            "equity_curve": equity_curve,
            "performance": {
                "total_return": round(profit_percent, 2),
                "annual_return": round((profit_percent / 100) * (365 / days) * 100 if days > 0 else 0, 2),
                "max_drawdown": round(max_drawdown, 2),
                "sharpe_ratio": round(sharpe_ratio, 2),
                "win_rate": round(random.uniform(0.5, 0.7) * 100, 2),
                "profit_factor": round(random.uniform(1.5, 2.5), 2),
                "total_trades": len(trades)
            }
        }

        db.commit()
        logger.info(f"回测任务完成: {backtest_id}")

    except Exception as e:
        logger.error(f"运行回测任务时出错: {str(e)}")
        try:
            # 尝试将状态更新为失败
            if 'backtest' in locals() and backtest:
                backtest.status = "failed"
                backtest.result_data = {"error": str(e)}
                db.commit()
        except Exception as commit_error:
            logger.error(f"更新回测状态失败: {str(commit_error)}")

# 回测API路由处理函数
@handle_exception
def get_backtests_handler(db: Session, strategy_id=None, status=None, start_date=None, end_date=None, page=1, limit=10):
    """获取所有回测列表 - 从数据库获取回测数据"""
    logger.info("获取回测列表 - 从数据库")

    try:
        # 尝试从数据库获取回测数据
        from backend.app.models.backtest import BacktestResult
        query = db.query(BacktestResult)

        # 应用过滤条件
        if strategy_id:
            query = query.filter(BacktestResult.strategy_id == strategy_id)
        if status:
            query = query.filter(BacktestResult.status == status)
        if start_date:
            start_timestamp = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            query = query.filter(BacktestResult.created_at >= start_timestamp)
        if end_date:
            end_timestamp = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            query = query.filter(BacktestResult.created_at <= end_timestamp)

        # 计算总数
        total = query.count()

        # 分页
        offset = (page - 1) * limit
        query = query.order_by(BacktestResult.created_at.desc()).offset(offset).limit(limit)

        # 执行查询
        backtests = query.all()

        # 格式化结果
        results = []
        for backtest in backtests:
            # 获取策略名称
            strategy_name = "未知策略"
            from backend.app.models.strategy import Strategy
            strategy = db.query(Strategy).filter(Strategy.id == backtest.strategy_id).first()
            if strategy:
                strategy_name = strategy.name

            results.append({
                "id": backtest.id,
                "strategy_id": backtest.strategy_id,
                "strategy_name": strategy_name,
                "start_date": backtest.start_date.isoformat() if backtest.start_date else None,
                "end_date": backtest.end_date.isoformat() if backtest.end_date else None,
                "initial_capital": backtest.initial_capital,
                "final_capital": backtest.final_capital,
                "total_return": round(backtest.total_return * 100, 2) if backtest.total_return is not None else None,
                "annual_return": round(backtest.annual_return * 100, 2) if backtest.annual_return is not None else None,
                "max_drawdown": round(backtest.max_drawdown * 100, 2) if backtest.max_drawdown is not None else None,
                "sharpe_ratio": backtest.sharpe_ratio,
                "win_rate": round(backtest.win_rate * 100, 2) if backtest.win_rate is not None else None,
                "profit_factor": backtest.profit_factor,
                "trades_count": backtest.trades_count,
                "created_at": backtest.created_at.isoformat() if backtest.created_at else None,
                "status": backtest.status
            })

        return {
            "success": True,
            "data": {
                "backtests": results,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total,
                    "pages": (total + limit - 1) // limit
                }
            }
        }

    except Exception as e:
        logger.error(f"从数据库获取回测列表失败: {e}")
        # 如果数据库查询失败，返回模拟数据
        return {
            "success": True,
            "data": {
                "backtests": BACKTEST_RESULTS,
                "pagination": {
                    "page": 1,
                    "limit": 10,
                    "total": len(BACKTEST_RESULTS),
                    "pages": 1
                }
            }
        }

@handle_exception
def get_backtest_by_id_handler(backtest_id: int, db: Session):
    """获取单个回测详情 - 从数据库获取"""
    logger.info(f"获取回测详情: {backtest_id}")

    try:
        # 从数据库获取回测详情
        from backend.app.models.backtest import BacktestResult
        backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()

        if not backtest:
            return {"success": False, "error": "回测记录不存在"}, 404

        # 获取策略名称
        strategy_name = "未知策略"
        from backend.app.models.strategy import Strategy
        strategy = db.query(Strategy).filter(Strategy.id == backtest.strategy_id).first()
        if strategy:
            strategy_name = strategy.name

        # 格式化结果
        result = {
            "id": backtest.id,
            "strategy_id": backtest.strategy_id,
            "strategy_name": strategy_name,
            "start_date": backtest.start_date.isoformat() if backtest.start_date else None,
            "end_date": backtest.end_date.isoformat() if backtest.end_date else None,
            "initial_capital": backtest.initial_capital,
            "final_capital": backtest.final_capital,
            "total_return": round(backtest.total_return * 100, 2) if backtest.total_return is not None else None,
            "annual_return": round(backtest.annual_return * 100, 2) if backtest.annual_return is not None else None,
            "max_drawdown": round(backtest.max_drawdown * 100, 2) if backtest.max_drawdown is not None else None,
            "sharpe_ratio": backtest.sharpe_ratio,
            "win_rate": round(backtest.win_rate * 100, 2) if backtest.win_rate is not None else None,
            "profit_factor": backtest.profit_factor,
            "trades_count": backtest.trades_count,
            "created_at": backtest.created_at.isoformat() if backtest.created_at else None,
            "status": backtest.status,
            "result_data": backtest.result_data
        }

        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        logger.error(f"获取回测详情失败: {e}")
        # 尝试从模拟数据中获取
        for backtest in BACKTEST_RESULTS:
            if backtest["id"] == backtest_id:
                return {"success": True, "data": backtest}
        return {"success": False, "error": "回测记录不存在"}, 404

@handle_exception
def create_backtest_handler(data: Dict[str, Any], db: Session):
    """创建新回测任务 - 保存到数据库"""
    logger.info("创建回测任务 - 保存到数据库")

    try:
        # 验证必填字段
        required_fields = ["strategy_id", "start_date", "end_date", "initial_capital"]
        missing_fields = [f for f in required_fields if f not in data]

        if missing_fields:
            return {"success": False, "error": f"缺少必填字段: {', '.join(missing_fields)}"}, 400

        # 创建新回测记录
        from backend.app.models.backtest import BacktestResult

        # 解析日期
        try:
            start_date = datetime.fromisoformat(data["start_date"].replace('Z', '+00:00'))
            end_date = datetime.fromisoformat(data["end_date"].replace('Z', '+00:00'))
        except ValueError as e:
            return {"success": False, "error": f"日期格式无效: {str(e)}"}, 400

        # 创建新回测记录
        new_backtest = BacktestResult(
            strategy_id=data["strategy_id"],
            start_date=start_date,
            end_date=end_date,
            initial_capital=data["initial_capital"],
            final_capital=data["initial_capital"],  # 初始值与初始资金相同
            total_return=0,  # 初始收益率为0
            trades_count=0,  # 初始交易次数为0
            status="pending",  # 初始状态为等待中
            created_at=datetime.now()
        )

        # 添加到数据库
        db.add(new_backtest)
        db.commit()
        db.refresh(new_backtest)

        # 在后台启动回测任务
        thread = Thread(target=run_backtest, args=(new_backtest.id, data, db))
        thread.daemon = True
        thread.start()

        # 构建要返回的新回测记录数据
        strategy_name = "未知策略"
        try:
            from backend.app.models.strategy import Strategy
            strategy = db.query(Strategy).filter(Strategy.id == new_backtest.strategy_id).first()
            if strategy:
                strategy_name = strategy.name
        except Exception as e:
            logger.warning(f"无法获取策略名称: {e}")
            strategy_name = f"策略ID-{new_backtest.strategy_id}"

        response_data = {
            "id": new_backtest.id,
            "strategy_id": new_backtest.strategy_id,
            "strategy_name": strategy_name,
            "start_date": data["start_date"],
            "end_date": data["end_date"],
            "initial_capital": new_backtest.initial_capital,
            "created_at": new_backtest.created_at.isoformat() if new_backtest.created_at else datetime.now().isoformat(),
            "status": "pending",
            "message": "回测任务已创建，正在后台处理"
        }

        return {"success": True, "data": response_data}, 201

    except Exception as e:
        logger.error(f"创建回测任务失败: {e}")
        return {"success": False, "error": f"创建回测任务失败: {str(e)}"}, 500

@handle_exception
def get_backtests_stats_handler(db: Session):
    """获取回测统计数据 - 从数据库获取"""
    logger.info("获取回测统计数据 - 从数据库")

    try:
        # 从数据库获取回测统计数据
        from backend.app.models.backtest import BacktestResult
        from sqlalchemy import func

        # 获取总数
        total = db.query(func.count(BacktestResult.id)).scalar() or 0

        # 获取各状态数量
        running = db.query(func.count(BacktestResult.id)).filter(BacktestResult.status == "running").scalar() or 0
        completed = db.query(func.count(BacktestResult.id)).filter(BacktestResult.status == "completed").scalar() or 0
        pending = db.query(func.count(BacktestResult.id)).filter(BacktestResult.status == "pending").scalar() or 0
        failed = db.query(func.count(BacktestResult.id)).filter(BacktestResult.status == "failed").scalar() or 0

        # 获取平均收益率
        avg_return = db.query(func.avg(BacktestResult.total_return)).filter(BacktestResult.status == "completed").scalar() or 0
        avg_return = round(avg_return * 100, 2)  # 转换为百分比

        # 获取平均夏普比率
        avg_sharpe = db.query(func.avg(BacktestResult.sharpe_ratio)).filter(BacktestResult.status == "completed").scalar() or 0
        avg_sharpe = round(avg_sharpe, 2)

        # 获取平均最大回撤
        avg_drawdown = db.query(func.avg(BacktestResult.max_drawdown)).filter(BacktestResult.status == "completed").scalar() or 0
        avg_drawdown = round(avg_drawdown * 100, 2)  # 转换为百分比

        return {
            "success": True,
            "data": {
                "total": total,
                "running": running,
                "completed": completed,
                "pending": pending,
                "failed": failed,
                "avg_return": avg_return,
                "avg_sharpe_ratio": avg_sharpe,
                "avg_max_drawdown": avg_drawdown
            }
        }

    except Exception as e:
        logger.error(f"获取回测统计数据失败: {e}")
        # 返回模拟数据
        return {
            "success": True,
            "data": {
                "total": len(BACKTEST_RESULTS),
                "running": 0,
                "completed": len(BACKTEST_RESULTS),
                "pending": 0,
                "failed": 0,
                "avg_return": 22.5,
                "avg_sharpe_ratio": 1.65,
                "avg_max_drawdown": 6.65
            }
        }

@handle_exception
def get_recent_backtests_handler(db: Session, limit=5):
    """获取最近回测记录 - 从数据库获取"""
    logger.info("获取最近回测记录 - 从数据库")

    try:
        # 从数据库获取最近回测记录
        from backend.app.models.backtest import BacktestResult

        # 查询最近的回测记录
        recent_backtests = db.query(BacktestResult).order_by(BacktestResult.created_at.desc()).limit(limit).all()

        # 格式化结果
        results = []
        for backtest in recent_backtests:
            # 获取策略名称
            strategy_name = "未知策略"
            try:
                from backend.app.models.strategy import Strategy
                strategy = db.query(Strategy).filter(Strategy.id == backtest.strategy_id).first()
                if strategy:
                    strategy_name = strategy.name
            except Exception as e:
                logger.warning(f"无法获取策略名称: {e}")
                strategy_name = f"策略ID-{backtest.strategy_id}"

            results.append({
                "id": backtest.id,
                "strategy_name": strategy_name,
                "symbol": "BTC/USDT",  # 假设使用默认交易对
                "start_time": backtest.start_date.isoformat() if backtest.start_date else None,
                "end_time": backtest.end_date.isoformat() if backtest.end_date else None,
                "status": backtest.status,
                "profit": round(backtest.total_return * 100, 2) if backtest.total_return is not None else None
            })

        return results

    except Exception as e:
        logger.error(f"获取最近回测记录失败: {e}")
        # 返回模拟数据
        formatted_tests = []
        for test in sorted(BACKTEST_RESULTS, key=lambda x: x.get('created_at', ''), reverse=True)[:limit]:
            formatted_tests.append({
                "id": test.get('id'),
                "strategy_name": test.get('strategy_name'),
                "symbol": "BTC/USDT",  # 假设使用默认交易对
                "start_time": test.get('start_date'),
                "end_time": test.get('end_date'),
                "status": test.get('status'),
                "profit": test.get('profit_loss_percent')
            })

        return formatted_tests

@handle_exception
def get_strategies_handler():
    """获取策略列表 - 从数据库获取真实数据"""
    logger.info("获取策略列表 - 从数据库")

    try:
        # 尝试从数据库获取策略数据
        from backend.simple_api import get_db
        from backend.app.models.strategy import Strategy
        db = get_db()

        # 查询所有策略
        strategies = db.query(Strategy).all()

        # 格式化结果
        result = []
        for strategy in strategies:
            result.append({
                "id": strategy.id,
                "name": strategy.name,
                "description": strategy.description,
                "code": strategy.code,
                "status": "active" if strategy.is_active else "inactive",
                "is_active": strategy.is_active,
                "created_at": strategy.created_at.isoformat() if strategy.created_at else None,
                "updated_at": strategy.updated_at.isoformat() if strategy.updated_at else None,
                "creator_id": strategy.creator_id
            })

        db.close()
        logger.info(f"从数据库获取到 {len(result)} 个策略")
        return result

    except Exception as e:
        logger.error(f"从数据库获取策略列表失败: {e}")
        # 如果数据库查询失败，返回模拟数据作为备用
        logger.info("使用模拟数据作为备用")
        return STRATEGIES

@handle_exception
def get_strategy_by_id_handler(strategy_id: int):
    """获取单个策略 - 从数据库获取真实数据"""
    logger.info(f"获取策略详情: {strategy_id} - 从数据库")

    try:
        # 尝试从数据库获取策略详情
        from backend.simple_api import get_db
        from backend.app.models.strategy import Strategy
        db = get_db()

        strategy = db.query(Strategy).filter(Strategy.id == strategy_id).first()

        if not strategy:
            db.close()
            return {"success": False, "error": "策略不存在"}, 404

        result = {
            "id": strategy.id,
            "name": strategy.name,
            "description": strategy.description,
            "code": strategy.code,
            "status": "active" if strategy.is_active else "inactive",
            "is_active": strategy.is_active,
            "created_at": strategy.created_at.isoformat() if strategy.created_at else None,
            "updated_at": strategy.updated_at.isoformat() if strategy.updated_at else None,
            "creator_id": strategy.creator_id
        }

        db.close()
        return result

    except Exception as e:
        logger.error(f"从数据库获取策略详情失败: {e}")
        # 如果数据库查询失败，尝试从模拟数据中获取
        for strategy in STRATEGIES:
            if strategy["id"] == strategy_id:
                return strategy
        return {"success": False, "error": "策略不存在"}, 404

@handle_exception
def get_strategies_stats_handler():
    """获取策略统计数据 - 从数据库获取真实数据"""
    logger.info("获取策略统计数据 - 从数据库")

    try:
        # 尝试从数据库获取策略统计数据
        from backend.simple_api import get_db
        from backend.app.models.strategy import Strategy
        from sqlalchemy import func
        db = get_db()

        # 获取总数
        total = db.query(func.count(Strategy.id)).scalar() or 0

        # 获取活跃策略数
        active = db.query(func.count(Strategy.id)).filter(Strategy.is_active == True).scalar() or 0

        db.close()

        return {
            "total": total,
            "active": active,
            "inactive": total - active,
            "avg_return": 0  # 暂时设为0，后续可以从回测结果计算
        }

    except Exception as e:
        logger.error(f"从数据库获取策略统计数据失败: {e}")
        # 如果数据库查询失败，返回模拟数据
        total = len(STRATEGIES)
        active = sum(1 for s in STRATEGIES if s.get('is_active', False))
        avg_return = sum(s.get('avg_return', 0) for s in STRATEGIES) / total if total > 0 else 0

        return {
            "total": total,
            "active": active,
            "inactive": total - active,
            "avg_return": round(avg_return, 2)
        }
