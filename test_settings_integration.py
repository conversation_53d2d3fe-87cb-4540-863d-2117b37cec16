#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统设置融合测试脚本
测试统一配置管理功能是否成功融合到系统设置中
"""

import requests
import json
import time
import sys
import os

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

class SettingsIntegrationTest:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        
    def authenticate(self):
        """用户认证"""
        try:
            response = self.session.post(f"{BASE_URL}/api/v1/auth/login", json=TEST_USER)
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access_token')
                self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                print("✅ 用户认证成功")
                return True
            else:
                print(f"❌ 用户认证失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 认证异常: {e}")
            return False
    
    def test_system_config_api(self):
        """测试系统配置API"""
        print("\n🔧 测试系统配置API...")
        
        # 测试获取系统配置
        try:
            response = self.session.get(f"{BASE_URL}/api/v1/config/system")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 获取系统配置成功")
                    config = data.get('data', {})
                    print(f"   - 交易配置: {config.get('trading', {}).get('enable_live_trading', 'N/A')}")
                    print(f"   - 主题设置: {config.get('system', {}).get('theme', 'N/A')}")
                    return True
                else:
                    print(f"❌ 获取系统配置失败: {data.get('message', 'Unknown error')}")
            else:
                print(f"❌ 系统配置API请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 系统配置API异常: {e}")
        
        return False
    
    def test_api_keys_config(self):
        """测试API密钥配置"""
        print("\n🔑 测试API密钥配置...")
        
        try:
            response = self.session.get(f"{BASE_URL}/api/v1/config/api-keys")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 获取API密钥配置成功")
                    keys = data.get('data', [])
                    print(f"   - 配置的API密钥数量: {len(keys)}")
                    for key in keys:
                        print(f"   - {key.get('name', 'Unknown')}: {'启用' if key.get('enabled') else '禁用'}")
                    return True
                else:
                    print(f"❌ 获取API密钥配置失败: {data.get('message', 'Unknown error')}")
            else:
                print(f"❌ API密钥配置请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ API密钥配置异常: {e}")
        
        return False
    
    def test_system_params(self):
        """测试系统参数配置"""
        print("\n⚙️ 测试系统参数配置...")
        
        try:
            response = self.session.get(f"{BASE_URL}/api/v1/config/system-params")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 获取系统参数成功")
                    params = data.get('data', {})
                    print(f"   - 性能配置: {params.get('performance', {})}")
                    print(f"   - 网络配置: {params.get('network', {})}")
                    return True
                else:
                    print(f"❌ 获取系统参数失败: {data.get('message', 'Unknown error')}")
            else:
                print(f"❌ 系统参数请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 系统参数异常: {e}")
        
        return False
    
    def test_cache_management(self):
        """测试缓存管理"""
        print("\n🗂️ 测试缓存管理...")
        
        try:
            # 获取缓存大小
            response = self.session.get(f"{BASE_URL}/api/v1/config/cache-size")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 获取缓存大小成功")
                    cache_info = data.get('data', {})
                    print(f"   - 总缓存大小: {cache_info.get('total_size_mb', 'N/A')}")
                    return True
                else:
                    print(f"❌ 获取缓存大小失败: {data.get('message', 'Unknown error')}")
            else:
                print(f"❌ 缓存大小请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 缓存管理异常: {e}")
        
        return False
    
    def test_user_settings_api(self):
        """测试用户设置API"""
        print("\n👤 测试用户设置API...")
        
        try:
            response = self.session.get(f"{BASE_URL}/api/v1/user/settings")
            if response.status_code == 200:
                data = response.json()
                print("✅ 获取用户设置成功")
                settings = data.get('data', {}) if isinstance(data, dict) else data
                print(f"   - 主题设置: {settings.get('theme', 'N/A')}")
                print(f"   - 语言设置: {settings.get('language', 'N/A')}")
                return True
            else:
                print(f"❌ 用户设置请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 用户设置异常: {e}")
        
        return False
    
    def test_deprecated_apis(self):
        """测试已废弃的API是否已移除"""
        print("\n🚫 测试已废弃的API...")
        
        deprecated_endpoints = [
            "/api/v1/unified-config/system",
            "/api/v1/unified-config/api-keys",
            "/api/v1/unified-config/system-params"
        ]
        
        all_removed = True
        for endpoint in deprecated_endpoints:
            try:
                response = self.session.get(f"{BASE_URL}{endpoint}")
                if response.status_code == 404:
                    print(f"✅ 已废弃的API已移除: {endpoint}")
                else:
                    print(f"❌ 已废弃的API仍然存在: {endpoint} (状态码: {response.status_code})")
                    all_removed = False
            except Exception as e:
                print(f"✅ 已废弃的API已移除: {endpoint} (连接异常)")
        
        return all_removed
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始系统设置融合测试...")
        print("=" * 60)
        
        if not self.authenticate():
            print("❌ 认证失败，无法继续测试")
            return False
        
        test_results = []
        
        # 运行各项测试
        test_results.append(("系统配置API", self.test_system_config_api()))
        test_results.append(("API密钥配置", self.test_api_keys_config()))
        test_results.append(("系统参数配置", self.test_system_params()))
        test_results.append(("缓存管理", self.test_cache_management()))
        test_results.append(("用户设置API", self.test_user_settings_api()))
        test_results.append(("已废弃API移除", self.test_deprecated_apis()))
        
        # 统计结果
        print("\n" + "=" * 60)
        print("📊 测试结果汇总:")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！系统设置融合成功！")
            return True
        else:
            print("⚠️ 部分测试失败，请检查系统配置")
            return False

def main():
    """主函数"""
    print("系统设置融合测试工具")
    print("测试统一配置管理功能是否成功融合到系统设置中")
    print()
    
    # 检查后端服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ 后端服务未正常运行 (状态码: {response.status_code})")
            print("请确保后端服务已启动并运行在端口8000")
            sys.exit(1)
    except requests.exceptions.RequestException:
        print("❌ 无法连接到后端服务")
        print("请确保后端服务已启动并运行在端口8000")
        sys.exit(1)
    
    # 运行测试
    tester = SettingsIntegrationTest()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
