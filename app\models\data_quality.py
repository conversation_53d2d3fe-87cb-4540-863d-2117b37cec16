#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base

class DataQualityRecord(Base):
    """数据质量记录模型"""
    __tablename__ = "data_quality_records"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, ForeignKey("data_sources.id"), nullable=False)
    timestamp = Column(DateTime, default=func.now(), index=True)
    quality_score = Column(Float, nullable=False)
    completeness = Column(Float, nullable=False)
    accuracy = Column(Float, nullable=False)
    timeliness = Column(Float, nullable=False)
    consistency = Column(Float, nullable=True)
    anomalies_count = Column(Integer, default=0)
    gaps_count = Column(Integer, default=0)
    timeframe = Column(String(10), nullable=False)
    record_time = Column(DateTime, default=func.now())
    details = Column(JSON, nullable=True)

    # 可选字段，与backend/app/models/data_quality.py兼容
    exchange = Column(String(50), nullable=True)
    symbol = Column(String(20), nullable=True)
    quality_grade = Column(String(2), nullable=True)
    health_status = Column(String(20), nullable=True)
    anomaly_stats = Column(JSON, nullable=True)
    gap_stats = Column(JSON, nullable=True)
    data_timestamp = Column(DateTime, nullable=True)

    # 关联关系
    data_source = relationship("DataSource", back_populates="quality_records")

class DataQualityAlert(Base):
    """数据质量告警模型"""
    __tablename__ = "data_quality_alerts"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, ForeignKey("data_sources.id"), nullable=False)
    timestamp = Column(DateTime, default=func.now(), index=True)
    alert_type = Column(String(50), nullable=False)  # gap, anomaly, consistency, etc.
    severity = Column(String(20), nullable=False)  # low, medium, high
    message = Column(Text, nullable=False)
    status = Column(String(20), default="active")  # active, resolved, ignored
    resolved_at = Column(DateTime, nullable=True)
    resolved_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    resolution_note = Column(Text, nullable=True)

    # 关联关系
    data_source = relationship("DataSource", back_populates="quality_alerts")
    resolver = relationship("User", foreign_keys=[resolved_by])

class DataQualityTrend(Base):
    """数据质量趋势模型"""
    __tablename__ = "data_quality_trends"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, ForeignKey("data_sources.id"), nullable=False)
    date = Column(DateTime, index=True)
    period_type = Column(String(10), nullable=False)  # daily, weekly, monthly
    quality_score = Column(Float, nullable=False)
    completeness = Column(Float, nullable=False)
    accuracy = Column(Float, nullable=False)
    timeliness = Column(Float, nullable=False)
    consistency = Column(Float, nullable=True)
    trend_direction = Column(String(10), nullable=False)  # up, down, stable
    anomalies_count = Column(Integer, default=0)
    gaps_count = Column(Integer, default=0)

    # 关联关系
    data_source = relationship("DataSource", back_populates="quality_trends")

class QualityReportConfig(Base):
    """质量报告配置模型"""
    __tablename__ = "quality_report_configs"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    report_type = Column(String(20), nullable=False)  # daily, weekly, monthly, custom
    schedule = Column(String(50), nullable=True)  # cron expression
    sources = Column(JSON, nullable=False)  # list of source IDs
    metrics = Column(JSON, nullable=False)  # list of metrics to include
    notification_enabled = Column(Boolean, default=False)
    notification_channels = Column(JSON, nullable=True)  # email, telegram, etc.
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True)

    # 关联关系
    user = relationship("User", back_populates="report_configs")

# 更新数据源模型中的关联关系
from app.models.data import DataSource
DataSource.quality_records = relationship("DataQualityRecord", back_populates="data_source", cascade="all, delete-orphan")
DataSource.quality_alerts = relationship("DataQualityAlert", back_populates="data_source", cascade="all, delete-orphan")
DataSource.quality_trends = relationship("DataQualityTrend", back_populates="data_source", cascade="all, delete-orphan")

# 更新用户模型中的关联关系
from app.models.user import User
User.report_configs = relationship("QualityReportConfig", back_populates="user", cascade="all, delete-orphan")
