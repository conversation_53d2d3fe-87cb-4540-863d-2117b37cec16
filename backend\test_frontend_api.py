#!/usr/bin/env python3
"""
测试前端策略创建向导API调用
"""

import requests
import json

def test_frontend_api_calls():
    """测试前端会调用的API"""
    base_url = "http://localhost:8000"
    
    print("🧪 测试前端策略创建向导API调用...")
    
    # 测试1: 获取策略类型列表 (前端首先会调用这个)
    print("\n1. 测试获取策略类型列表:")
    try:
        response = requests.get(f"{base_url}/api/v1/strategy-types")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功获取策略类型")
            
            # 查找趋势跟踪和均值回归类型
            for strategy_type in data:
                if isinstance(strategy_type, dict):
                    type_value = strategy_type.get('value')
                    type_label = strategy_type.get('label')
                    categories = strategy_type.get('categories', [])
                    
                    print(f"   📊 类型: {type_label} ({type_value})")
                    
                    if type_value == 'trend_following':
                        print(f"      趋势跟踪分类:")
                        for cat in categories:
                            print(f"        - {cat.get('label')} ({cat.get('value')})")
                            if cat.get('value') == 'macd_trend':
                                print(f"          ✅ 找到MACD趋势分类!")
                    
                    elif type_value == 'mean_reversion':
                        print(f"      均值回归分类:")
                        for cat in categories:
                            print(f"        - {cat.get('label')} ({cat.get('value')})")
                            if cat.get('value') == 'bollinger_reversion':
                                print(f"          ✅ 找到布林带回归分类!")
        else:
            print(f"   ❌ 获取策略类型失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {str(e)}")
    
    # 测试2: 模拟前端选择MACD趋势后获取模板
    print("\n2. 模拟前端选择MACD趋势分类:")
    try:
        response = requests.get(f"{base_url}/api/v1/strategy-templates", params={
            'type': 'trend_following',
            'category': 'macd_trend'
        })
        if response.status_code == 200:
            data = response.json()
            templates = data if isinstance(data, list) else data.get('templates', data.get('data', []))
            print(f"   ✅ 返回 {len(templates)} 个MACD趋势模板")
            
            for template in templates:
                if isinstance(template, dict):
                    print(f"      - {template.get('name')} (ID: {template.get('id')})")
                    print(f"        描述: {template.get('description', 'N/A')}")
        else:
            print(f"   ❌ 获取MACD趋势模板失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {str(e)}")
    
    # 测试3: 模拟前端选择布林带回归后获取模板
    print("\n3. 模拟前端选择布林带回归分类:")
    try:
        response = requests.get(f"{base_url}/api/v1/strategy-templates", params={
            'type': 'mean_reversion',
            'category': 'bollinger_reversion'
        })
        if response.status_code == 200:
            data = response.json()
            templates = data if isinstance(data, list) else data.get('templates', data.get('data', []))
            print(f"   ✅ 返回 {len(templates)} 个布林带回归模板")
            
            for template in templates:
                if isinstance(template, dict):
                    print(f"      - {template.get('name')} (ID: {template.get('id')})")
                    print(f"        描述: {template.get('description', 'N/A')}")
        else:
            print(f"   ❌ 获取布林带回归模板失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {str(e)}")

if __name__ == "__main__":
    print("🎯 前端策略创建向导API测试")
    print("=" * 50)
    test_frontend_api_calls()
    print("\n" + "=" * 50)
    print("✨ 测试完成！")
