#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全面检查系统API端点脚本
扫描所有后端服务，收集完整的API端点列表
"""

import requests
import json
import logging
from typing import List, Dict, Set
import re
import os

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_flask_routes(base_url: str) -> List[str]:
    """检查Flask应用的路由"""
    endpoints = []
    try:
        # 尝试访问调试路由端点
        response = requests.get(f"{base_url}/debug/routes", timeout=5)
        if response.status_code == 200:
            routes_data = response.json()
            for route in routes_data:
                path = route.get('path', '')
                methods = route.get('methods', [])
                if path.startswith('/api/v1/') and 'GET' in methods:
                    endpoints.append(path)
        
        # 也尝试API版本的调试端点
        response = requests.get(f"{base_url}/api/v1/debug/routes", timeout=5)
        if response.status_code == 200:
            routes_data = response.json()
            for route in routes_data:
                path = route.get('path', '')
                methods = route.get('methods', [])
                if path.startswith('/api/v1/') and 'GET' in methods:
                    endpoints.append(path)
                    
    except Exception as e:
        logger.warning(f"无法获取Flask路由 {base_url}: {str(e)}")
    
    return endpoints

def scan_code_for_routes() -> List[str]:
    """扫描代码文件中的路由定义"""
    endpoints = []
    
    # 扫描simple_api.py中的路由
    try:
        with open('backend/simple_api.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找@app.route装饰器
        route_pattern = r"@app\.route\(['\"]([^'\"]+)['\"]"
        matches = re.findall(route_pattern, content)
        
        for match in matches:
            if match.startswith('/api/v1/'):
                endpoints.append(match)
                
    except Exception as e:
        logger.warning(f"扫描simple_api.py失败: {str(e)}")
    
    # 扫描performance_api_module.py中的路由
    try:
        with open('backend/performance_api_module.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找@performance_bp.route装饰器
        route_pattern = r"@performance_bp\.route\(['\"]([^'\"]+)['\"]"
        matches = re.findall(route_pattern, content)
        
        for match in matches:
            # 添加完整路径前缀
            full_path = f"/api/v1/performance{match}"
            endpoints.append(full_path)
                
    except Exception as e:
        logger.warning(f"扫描performance_api_module.py失败: {str(e)}")
    
    return endpoints

def get_comprehensive_api_list() -> List[str]:
    """获取系统中所有API端点的综合列表"""
    all_endpoints = set()
    
    # 1. 从运行中的服务获取路由
    services = [
        "http://localhost:8000",  # 主API服务
        "http://localhost:8001",  # 回测API
        "http://localhost:8002",  # 风险管理API
    ]
    
    for service_url in services:
        try:
            # 检查服务是否运行
            response = requests.get(f"{service_url}/health", timeout=3)
            if response.status_code == 200:
                logger.info(f"✅ 服务运行中: {service_url}")
                endpoints = check_flask_routes(service_url)
                all_endpoints.update(endpoints)
            else:
                logger.warning(f"⚠️ 服务响应异常: {service_url}")
        except Exception as e:
            logger.warning(f"❌ 服务不可达: {service_url} - {str(e)}")
    
    # 2. 从代码中扫描路由定义
    code_endpoints = scan_code_for_routes()
    all_endpoints.update(code_endpoints)
    
    # 3. 添加已知的核心API端点
    core_endpoints = [
        # 认证相关
        "/api/v1/auth/login",
        "/api/v1/auth/logout",
        "/api/v1/auth/token",
        "/api/v1/auth/refresh",
        
        # 用户管理
        "/api/v1/user/profile",
        "/api/v1/user/settings",
        "/api/v1/users/list",
        
        # 数据管理
        "/api/v1/data/ohlcv",
        "/api/v1/data/symbols",
        "/api/v1/data/sources",
        "/api/v1/data/sync",
        "/api/v1/data/quality",
        "/api/v1/data/gaps",
        "/api/v1/data/repair",
        
        # 策略管理
        "/api/v1/strategy/list",
        "/api/v1/strategy/create",
        "/api/v1/strategy/update",
        "/api/v1/strategy/delete",
        "/api/v1/strategies",
        
        # 回测管理
        "/api/v1/backtest/create",
        "/api/v1/backtest/results",
        "/api/v1/backtest/list",
        "/api/v1/backtest/status",
        "/api/v1/backtests",
        
        # 交易管理
        "/api/v1/trading/positions",
        "/api/v1/trading/orders",
        "/api/v1/trading/balance",
        "/api/v1/trading/history",
        
        # 风险管理
        "/api/v1/risk/metrics",
        "/api/v1/risk/limits",
        "/api/v1/risk/alerts",
        "/api/v1/risk/analysis",
        
        # 市场数据
        "/api/v1/market/symbols",
        "/api/v1/market/ticker",
        "/api/v1/market/depth",
        "/api/v1/market/klines",
        
        # 通知系统
        "/api/v1/notifications/list",
        "/api/v1/notifications/alerts",
        "/api/v1/notifications/settings",
        "/api/v1/notifications/sms-settings",
        
        # 性能监控
        "/api/v1/performance/summary",
        "/api/v1/performance/analysis",
        "/api/v1/performance/api/stats",
        "/api/v1/performance/api/endpoints",
        "/api/v1/performance/cache/stats",
        "/api/v1/performance/cache/clear",
        "/api/v1/performance/memory/usage",
        "/api/v1/performance/metrics/cpu",
        "/api/v1/performance/metrics/memory",
        "/api/v1/performance/metrics/api",
        "/api/v1/performance/metrics/cache",
        
        # 系统配置
        "/api/v1/config/system",
        "/api/v1/config/trading",
        "/api/v1/config/database",
        
        # 健康检查
        "/api/v1/health",
        "/api/v1/status",
        
        # 监控和日志
        "/api/v1/monitoring/status",
        "/api/v1/monitoring/logs",
        "/api/v1/monitoring/metrics",
    ]
    
    all_endpoints.update(core_endpoints)
    
    # 转换为排序列表
    sorted_endpoints = sorted(list(all_endpoints))
    
    logger.info(f"📊 发现API端点总数: {len(sorted_endpoints)}")
    
    return sorted_endpoints

def update_performance_api_endpoints(endpoints: List[str]):
    """更新性能API模块中的端点列表"""
    try:
        # 读取performance_api_module.py文件
        with open('backend/performance_api_module.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 构建新的端点列表代码
        endpoints_code = "        endpoints = [\n"
        for endpoint in endpoints:
            endpoints_code += f"            '{endpoint}',\n"
        endpoints_code += "        ]"
        
        # 替换现有的端点列表
        pattern = r"endpoints = \[[\s\S]*?\]"
        new_content = re.sub(pattern, endpoints_code, content)
        
        # 写回文件
        with open('backend/performance_api_module.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        logger.info("✅ 已更新performance_api_module.py中的端点列表")
        
    except Exception as e:
        logger.error(f"❌ 更新performance_api_module.py失败: {str(e)}")

def main():
    """主函数"""
    print("🔍 开始全面检查系统API端点...")
    
    # 获取所有API端点
    all_endpoints = get_comprehensive_api_list()
    
    # 按类别分组显示
    categories = {
        "认证相关": [ep for ep in all_endpoints if "/auth/" in ep],
        "用户管理": [ep for ep in all_endpoints if "/user" in ep],
        "数据管理": [ep for ep in all_endpoints if "/data/" in ep],
        "策略管理": [ep for ep in all_endpoints if "/strateg" in ep],
        "回测管理": [ep for ep in all_endpoints if "/backtest" in ep],
        "交易管理": [ep for ep in all_endpoints if "/trading/" in ep],
        "风险管理": [ep for ep in all_endpoints if "/risk/" in ep],
        "市场数据": [ep for ep in all_endpoints if "/market/" in ep],
        "通知系统": [ep for ep in all_endpoints if "/notifications/" in ep],
        "性能监控": [ep for ep in all_endpoints if "/performance/" in ep],
        "系统配置": [ep for ep in all_endpoints if "/config/" in ep],
        "监控日志": [ep for ep in all_endpoints if "/monitoring/" in ep],
        "健康检查": [ep for ep in all_endpoints if "/health" in ep or "/status" in ep],
    }
    
    print("\n📋 API端点分类统计:")
    for category, endpoints in categories.items():
        if endpoints:
            print(f"  {category}: {len(endpoints)}个")
            for ep in endpoints[:3]:  # 只显示前3个
                print(f"    - {ep}")
            if len(endpoints) > 3:
                print(f"    ... 还有{len(endpoints)-3}个")
    
    # 更新性能API模块
    update_performance_api_endpoints(all_endpoints)
    
    # 保存完整列表到文件
    with open('api_endpoints_complete.json', 'w', encoding='utf-8') as f:
        json.dump({
            "total_count": len(all_endpoints),
            "categories": categories,
            "all_endpoints": all_endpoints
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 检查完成！发现{len(all_endpoints)}个API端点")
    print("📄 详细列表已保存到: api_endpoints_complete.json")
    print("🔧 已更新后端性能监控模块的端点列表")

if __name__ == "__main__":
    main()
