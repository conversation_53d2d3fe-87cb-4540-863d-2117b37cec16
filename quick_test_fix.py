#!/usr/bin/env python3
"""
快速测试前端数据显示修复效果
"""

import requests
import json
import time

def get_auth_token():
    """获取认证令牌"""
    response = requests.post('http://localhost:8000/api/v1/auth/token', 
                           json={'username': 'admin', 'password': 'admin123'})
    return response.json()['access_token']

def test_api_data():
    """测试API数据"""
    token = get_auth_token()
    headers = {'Authorization': f'Bearer {token}'}
    
    # 测试summary API
    response = requests.get('http://localhost:8000/api/v1/performance/summary', headers=headers)
    data = response.json()
    
    print("🔍 后端API数据测试:")
    print(f"✅ API状态: {response.status_code}")
    print(f"✅ Success字段: {data.get('success')}")
    print(f"✅ 数据结构: {list(data.get('data', {}).keys())}")
    
    # 检查关键数据
    summary = data.get('data', {})
    cpu_current = summary.get('cpu', {}).get('current', 0)
    memory_current = summary.get('memory', {}).get('current', 0)
    api_current = summary.get('api', {}).get('current_avg', 0)
    cache_current = summary.get('cache', {}).get('current_hit_rate', 0)
    
    print(f"\n📊 当前性能数据:")
    print(f"   CPU使用率: {cpu_current}%")
    print(f"   内存使用率: {memory_current}%")
    print(f"   API响应时间: {api_current:.3f}s")
    print(f"   缓存命中率: {cache_current*100:.1f}%")
    
    # 检查数据是否为真实值（不是0）
    if cpu_current > 0 and memory_current > 0:
        print("\n🎉 数据验证成功！")
        print("   ✅ CPU和内存数据为真实值")
        print("   ✅ 前端应该能正确显示这些数据")
        print("   ✅ 不再显示'加载中...'或0.0%")
        return True
    else:
        print("\n❌ 数据验证失败！")
        print("   ❌ CPU或内存数据为0，可能有问题")
        return False

if __name__ == "__main__":
    print("🚀 开始快速测试...")
    success = test_api_data()
    
    if success:
        print("\n✅ 测试通过！前端页面应该正常显示真实数据了！")
        print("🔗 请访问: http://localhost:8080/#/performance")
    else:
        print("\n❌ 测试失败！需要进一步检查问题。")
