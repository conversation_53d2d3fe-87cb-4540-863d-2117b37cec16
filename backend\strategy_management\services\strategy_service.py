"""
策略管理服务
负责策略的CRUD操作、参数管理、代码生成等核心业务逻辑
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import sys
import os

# 添加正确的路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(os.path.dirname(current_dir))
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

try:
    from backend.app.models.strategy import Strategy, StrategyTemplate, StrategyParameterHistory
    from backend.app.schemas.strategy import (
        StrategyCreate, StrategyUpdate, StrategyBase,
        StrategyTemplateCreate, StrategyTemplateBase,
        CodeGenerationRequest, CodeGenerationResponse
    )
except ImportError:
    # 如果无法导入，使用简化的模型
    class Strategy:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
            self.id = None
            self.created_at = datetime.now()
            self.updated_at = datetime.now()

    class StrategyTemplate:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    class StrategyParameterHistory:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    # 简化的数据模型
    class StrategyCreate:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    class StrategyUpdate:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

        def dict(self, exclude_unset=True):
            return {k: v for k, v in self.__dict__.items() if v is not None}

    class CodeGenerationResponse:
        def __init__(self, success=False, code=None, message=None, errors=None):
            self.success = success
            self.code = code
            self.message = message
            self.errors = errors or []
from .template_service import TemplateService
from .code_generator import CodeGenerator
from .parameter_validator import ParameterValidator
import logging
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class StrategyService:
    """策略管理服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.template_service = TemplateService(db)
        self.code_generator = CodeGenerator()
        self.parameter_validator = ParameterValidator()

    def create_strategy(self, strategy_data: StrategyCreate, creator_id: int) -> Strategy:
        """创建新策略"""
        try:
            # 验证策略名称唯一性
            existing = self.db.query(Strategy).filter(
                and_(Strategy.name == strategy_data.name, Strategy.creator_id == creator_id)
            ).first()
            if existing:
                raise ValueError(f"策略名称 '{strategy_data.name}' 已存在")

            # 如果使用模板，获取模板信息
            template = None
            if strategy_data.template_id:
                template = self.template_service.get_template(strategy_data.template_id)
                if not template:
                    raise ValueError(f"模板 ID {strategy_data.template_id} 不存在")

            # 验证参数
            if strategy_data.parameters and template:
                validation_result = self.parameter_validator.validate_parameters(
                    strategy_data.parameters, template.parameter_schema
                )
                if not validation_result.is_valid:
                    raise ValueError(f"参数验证失败: {', '.join(validation_result.errors)}")

            # 生成策略代码
            code_content = strategy_data.code_content
            if template and strategy_data.parameters:
                code_content = self.code_generator.generate_code(
                    template.code_template, strategy_data.parameters
                )

            # 创建策略对象
            strategy = Strategy(
                name=strategy_data.name,
                type=strategy_data.type.value,
                category=strategy_data.category.value,
                description=strategy_data.description,
                code_type=strategy_data.code_type.value,
                code_content=code_content,
                parameters=strategy_data.parameters,
                template_id=strategy_data.template_id,
                symbol=strategy_data.symbol,
                timeframe=strategy_data.timeframe,
                creator_id=creator_id,
                status="created"
            )

            self.db.add(strategy)
            self.db.commit()
            self.db.refresh(strategy)

            # 记录参数历史
            if strategy_data.parameters:
                self._record_parameter_history(
                    strategy.id, strategy_data.parameters, creator_id, "创建策略"
                )

            logger.info(f"成功创建策略: {strategy.name} (ID: {strategy.id})")
            return strategy

        except Exception as e:
            self.db.rollback()
            logger.error(f"创建策略失败: {str(e)}")
            raise

    def get_strategy(self, strategy_id: int, creator_id: Optional[int] = None) -> Optional[Strategy]:
        """获取策略详情"""
        query = self.db.query(Strategy).filter(Strategy.id == strategy_id)
        if creator_id:
            query = query.filter(Strategy.creator_id == creator_id)
        return query.first()

    def get_strategies(
        self,
        creator_id: Optional[int] = None,
        strategy_type: Optional[str] = None,
        category: Optional[str] = None,
        status: Optional[str] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Strategy]:
        """获取策略列表"""
        query = self.db.query(Strategy)

        # 过滤条件
        if creator_id:
            query = query.filter(Strategy.creator_id == creator_id)
        if strategy_type:
            query = query.filter(Strategy.type == strategy_type)
        if category:
            query = query.filter(Strategy.category == category)
        if status:
            query = query.filter(Strategy.status == status)
        if is_active is not None:
            query = query.filter(Strategy.is_active == is_active)

        # 排序和分页
        query = query.order_by(Strategy.updated_at.desc())
        return query.offset(skip).limit(limit).all()

    def update_strategy(
        self,
        strategy_id: int,
        strategy_data: StrategyUpdate,
        updater_id: int
    ) -> Optional[Strategy]:
        """更新策略"""
        try:
            strategy = self.get_strategy(strategy_id)
            if not strategy:
                return None

            # 记录原始参数
            old_parameters = strategy.parameters

            # 更新字段
            update_data = strategy_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(strategy, field):
                    if field in ['type', 'category', 'code_type'] and hasattr(value, 'value'):
                        setattr(strategy, field, value.value)
                    else:
                        setattr(strategy, field, value)

            # 如果参数发生变化，重新生成代码
            if strategy_data.parameters and strategy_data.parameters != old_parameters:
                if strategy.template_id:
                    template = self.template_service.get_template(strategy.template_id)
                    if template:
                        # 验证新参数
                        validation_result = self.parameter_validator.validate_parameters(
                            strategy_data.parameters, template.parameter_schema
                        )
                        if not validation_result.is_valid:
                            raise ValueError(f"参数验证失败: {', '.join(validation_result.errors)}")

                        # 重新生成代码
                        strategy.code_content = self.code_generator.generate_code(
                            template.code_template, strategy_data.parameters
                        )

                # 记录参数变更历史
                self._record_parameter_history(
                    strategy_id, strategy_data.parameters, updater_id, "更新参数"
                )

            strategy.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(strategy)

            logger.info(f"成功更新策略: {strategy.name} (ID: {strategy.id})")
            return strategy

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新策略失败: {str(e)}")
            raise

    def delete_strategy(self, strategy_id: int, deleter_id: int) -> bool:
        """删除策略（软删除）"""
        try:
            strategy = self.get_strategy(strategy_id)
            if not strategy:
                return False

            strategy.is_active = False
            strategy.status = "deleted"
            strategy.updated_at = datetime.utcnow()

            self.db.commit()
            logger.info(f"成功删除策略: {strategy.name} (ID: {strategy.id})")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"删除策略失败: {str(e)}")
            raise

    def generate_code_from_template(
        self,
        template_id: int,
        parameters: Dict[str, Any]
    ) -> CodeGenerationResponse:
        """从模板生成策略代码"""
        try:
            template = self.template_service.get_template(template_id)
            if not template:
                return CodeGenerationResponse(
                    success=False,
                    message=f"模板 ID {template_id} 不存在"
                )

            # 验证参数
            validation_result = self.parameter_validator.validate_parameters(
                parameters, template.parameter_schema
            )
            if not validation_result.is_valid:
                return CodeGenerationResponse(
                    success=False,
                    message="参数验证失败",
                    errors=validation_result.errors
                )

            # 生成代码
            code = self.code_generator.generate_code(template.code_template, parameters)

            return CodeGenerationResponse(
                success=True,
                code=code,
                message="代码生成成功"
            )

        except Exception as e:
            logger.error(f"代码生成失败: {str(e)}")
            return CodeGenerationResponse(
                success=False,
                message=f"代码生成失败: {str(e)}"
            )

    def get_parameter_history(self, strategy_id: int) -> List[StrategyParameterHistory]:
        """获取策略参数变更历史"""
        return self.db.query(StrategyParameterHistory).filter(
            StrategyParameterHistory.strategy_id == strategy_id
        ).order_by(StrategyParameterHistory.created_at.desc()).all()

    def _record_parameter_history(
        self,
        strategy_id: int,
        parameters: Dict[str, Any],
        changed_by: int,
        reason: str
    ):
        """记录参数变更历史"""
        history = StrategyParameterHistory(
            strategy_id=strategy_id,
            parameters=parameters,
            changed_by=changed_by,
            change_reason=reason
        )
        self.db.add(history)
