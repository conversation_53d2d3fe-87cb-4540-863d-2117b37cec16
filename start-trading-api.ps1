# 交易API服务启动脚本
Write-Host "正在启动交易API服务..." -ForegroundColor Cyan

# 从.env文件加载API密钥
$envFile = "backend\.env"
if (Test-Path $envFile) {
    Write-Host "正在加载环境变量..." -ForegroundColor Cyan
    Get-Content $envFile | ForEach-Object {
        if ($_ -match "^\s*([^#][^=]+)=(.*)$") {
            $key = $matches[1].Trim()
            $value = $matches[2].Trim()
            # 设置环境变量
            [Environment]::SetEnvironmentVariable($key, $value, "Process")
            Write-Host "已加载环境变量: $key" -ForegroundColor Green
        }
    }
} else {
    Write-Host "警告: .env文件不存在，API密钥可能未正确设置" -ForegroundColor Yellow
}

# 检查端口是否被占用
$portBusy = $false
try {
    $testConnection = New-Object System.Net.Sockets.TcpClient
    $testConnection.Connect("localhost", 8003)
    $testConnection.Close()
    $portBusy = $true
    Write-Host "端口 8003 已被占用" -ForegroundColor Yellow
} catch {
    $portBusy = $false
}

if ($portBusy) {
    Write-Host "端口8003已被占用，请先停止现有服务" -ForegroundColor Red
    exit 1
}

# 启动交易API服务
try {
    Write-Host "正在启动交易API服务..." -ForegroundColor Cyan
    $process = Start-Process -FilePath "python" -ArgumentList "backend\trading_api.py" -PassThru -NoNewWindow
    
    if ($process) {
        Write-Host "交易API服务已启动，进程ID: $($process.Id)" -ForegroundColor Green
        
        # 等待5秒确保服务启动
        Write-Host "等待服务启动..." -ForegroundColor Cyan
        Start-Sleep -Seconds 5
        
        # 检查服务是否正常响应
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:8003/health" -Method GET -TimeoutSec 5 -ErrorAction Stop
            if ($response.status -eq "ok") {
                Write-Host "交易API服务健康检查通过" -ForegroundColor Green
                Write-Host "交易API服务状态: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Green
            } else {
                Write-Host "交易API服务响应异常: $($response | ConvertTo-Json)" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "交易API服务健康检查失败: $_" -ForegroundColor Red
        }
        
        Write-Host "按任意键停止服务..." -ForegroundColor Cyan
        $null = $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
        # 停止进程
        Stop-Process -Id $process.Id -Force
        Write-Host "交易API服务已停止" -ForegroundColor Green
    } else {
        Write-Host "启动交易API服务失败!" -ForegroundColor Red
    }
} catch {
    Write-Host "启动交易API服务时出错: $_" -ForegroundColor Red
}
