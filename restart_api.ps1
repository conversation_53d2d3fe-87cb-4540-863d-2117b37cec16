# 重启API服务脚本

# 颜色配置
$infoColor = "Cyan"
$successColor = "Green"
$errorColor = "Red"
$warningColor = "Yellow"

Write-Host "=========================================================" -ForegroundColor $infoColor
Write-Host "                 重启API服务                             " -ForegroundColor $infoColor
Write-Host "=========================================================" -ForegroundColor $infoColor
Write-Host ""

# 停止相关服务
Write-Host "正在停止相关服务..." -ForegroundColor $infoColor

# 查找并停止市场数据API服务进程
$marketApiProcesses = Get-Process -Name "python" | Where-Object { $_.MainWindowTitle -like "*data_api_fastapi.py*" -or $_.MainWindowTitle -like "*backend\data_api_fastapi.py*" } -ErrorAction SilentlyContinue
if ($marketApiProcesses) {
    foreach ($process in $marketApiProcesses) {
        try {
            Stop-Process -Id $process.Id -Force
            Write-Host "已停止市场数据API服务进程 (PID: $($process.Id))" -ForegroundColor $successColor
        } catch {
            Write-Host "停止市场数据API服务进程失败: $_" -ForegroundColor $errorColor
        }
    }
} else {
    Write-Host "未找到运行中的市场数据API服务进程，尝试停止所有可能的相关进程..." -ForegroundColor $warningColor

    # 尝试通过端口查找进程
    $netstatOutput = netstat -ano | findstr ":8005"
    if ($netstatOutput) {
        $pidMatches = $netstatOutput | Select-String -Pattern "\s+(\d+)$" -AllMatches
        if ($pidMatches) {
            foreach ($match in $pidMatches.Matches) {
                $processPid = $match.Groups[1].Value
                try {
                    Stop-Process -Id $processPid -Force
                    Write-Host "已停止端口8005上的进程 (PID: $processPid)" -ForegroundColor $successColor
                } catch {
                    Write-Host "停止进程失败: $_" -ForegroundColor $errorColor
                }
            }
        }
    }
}

# 等待进程完全停止
Write-Host "等待进程完全停止..." -ForegroundColor $infoColor
Start-Sleep -Seconds 3

# 启动市场数据API服务
Write-Host "正在启动市场数据API服务..." -ForegroundColor $infoColor

# 设置环境变量
$env:PYTHONPATH = "$PWD"

try {
    $marketApiProcess = Start-Process -FilePath "python" -ArgumentList "backend\data_api_fastapi.py" -PassThru -WindowStyle Normal
    if ($marketApiProcess) {
        Write-Host "市场数据API服务已启动，进程ID: $($marketApiProcess.Id)" -ForegroundColor $successColor

        # 等待5秒确保服务启动
        Write-Host "等待服务启动..." -ForegroundColor $infoColor
        Start-Sleep -Seconds 5
    } else {
        Write-Host "启动市场数据API服务失败!" -ForegroundColor $errorColor
    }
} catch {
    Write-Host "启动市场数据API服务时出错: $_" -ForegroundColor $errorColor
}

Write-Host ""
Write-Host "重启完成。请检查服务是否正常运行。" -ForegroundColor $successColor
Write-Host "如果服务未正常运行，请尝试运行完整的start-services.ps1脚本。" -ForegroundColor $infoColor
