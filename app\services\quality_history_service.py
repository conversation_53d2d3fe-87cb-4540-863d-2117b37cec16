#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging
from sqlalchemy.orm import Session
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class QualityHistoryService:
    """
    数据质量历史服务，用于获取和分析数据质量历史记录
    """

    @staticmethod
    async def get_source_info(db: Session, source_id: int) -> Dict[str, Any]:
        """
        获取数据源信息

        Args:
            db: 数据库会话
            source_id: 数据源ID

        Returns:
            数据源信息字典
        """
        try:
            from app.models.data import DataSource

            source = db.query(DataSource).filter(DataSource.id == source_id).first()
            if not source:
                return {}

            return {
                "id": source.id,
                "name": source.name,
                "symbol": source.symbol,
                "exchange": source.source_type,
                "timeframe": source.timeframe
            }
        except Exception as e:
            logger.error(f"获取数据源信息失败: {str(e)}")
            return {}

    @staticmethod
    async def get_active_source_ids(db: Session) -> List[int]:
        """
        获取所有活跃的数据源ID

        Args:
            db: 数据库会话

        Returns:
            活跃数据源ID列表
        """
        try:
            from app.models.data import DataSource

            sources = db.query(DataSource).filter(DataSource.is_active == True).all()
            return [source.id for source in sources]
        except Exception as e:
            logger.error(f"获取活跃数据源ID失败: {str(e)}")
            return []

    @staticmethod
    async def get_quality_records(
        db: Session,
        source_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取数据源的质量记录

        Args:
            db: 数据库会话
            source_id: 数据源ID
            start_date: 开始日期
            end_date: 结束日期
            limit: 记录数量限制

        Returns:
            质量记录列表
        """
        try:
            from app.models.data_quality import DataQualityRecord

            query = db.query(DataQualityRecord).filter(
                DataQualityRecord.source_id == source_id
            )

            if start_date:
                query = query.filter(DataQualityRecord.timestamp >= start_date)

            if end_date:
                query = query.filter(DataQualityRecord.timestamp <= end_date)

            records = query.order_by(DataQualityRecord.timestamp.desc()).limit(limit).all()

            return [
                {
                    "id": record.id,
                    "source_id": record.source_id,
                    "timestamp": record.timestamp.isoformat(),
                    "quality_score": record.quality_score,
                    "completeness": record.completeness,
                    "accuracy": record.accuracy,
                    "timeliness": record.timeliness,
                    "anomalies_count": record.anomalies_count,
                    "gaps_count": record.gaps_count
                }
                for record in records
            ]
        except Exception as e:
            logger.error(f"获取质量记录失败: {str(e)}")
            return []

    @staticmethod
    async def get_latest_quality_record(
        db: Session,
        source_id: int,
        timeframe: str = None
    ) -> Optional[Any]:
        """
        获取数据源最新的质量记录

        Args:
            db: 数据库会话
            source_id: 数据源ID
            timeframe: 时间级别

        Returns:
            最新的质量记录
        """
        try:
            # 这里应该从数据库获取实际的质量记录
            # 为了演示，我们创建一个模拟记录

            # 创建一个模拟的记录对象
            class MockRecord:
                def __init__(self):
                    self.id = f"record_{source_id}"
                    self.source_id = source_id
                    self.record_time = datetime.now()
                    self.quality_score = 75 + np.random.normal(0, 5)
                    self.completeness = 80 + np.random.normal(0, 5)
                    self.accuracy = 70 + np.random.normal(0, 5)
                    self.timeliness = 85 + np.random.normal(0, 5)
                    self.anomalies_count = int(np.random.randint(0, 5))
                    self.gaps_count = int(np.random.randint(0, 3))

            return MockRecord()
        except Exception as e:
            logger.error(f"获取最新质量记录失败: {str(e)}")
            return None

    @staticmethod
    async def get_gap_statistics(
        db: Session,
        source_ids: List[int],
        timeframe: str,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """
        获取数据缺口统计信息

        Args:
            db: 数据库会话
            source_ids: 数据源ID列表
            timeframe: 时间级别
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            缺口统计信息
        """
        try:
            # 这里应该从数据库获取实际的缺口统计信息
            # 为了演示，我们生成一些随机数据

            # 根据数据源数量生成一个稳定的随机数
            base_gaps = len(source_ids) * 2

            # 添加一些随机波动
            random_factor = np.random.randint(0, 5)
            total_gaps = base_gaps + random_factor

            # 生成缺口统计信息
            return {
                "total_gaps": total_gaps,
                "severe_gaps": total_gaps // 3,
                "avg_gap_size": round(np.random.uniform(1, 5), 1),
                "avg_delay_minutes": round(np.random.uniform(5, 60), 1),
                "sources_with_gaps": min(len(source_ids), total_gaps)
            }
        except Exception as e:
            logger.error(f"获取缺口统计信息失败: {str(e)}")
            return {
                "total_gaps": 0,
                "severe_gaps": 0,
                "avg_gap_size": 0,
                "avg_delay_minutes": 0,
                "sources_with_gaps": 0
            }

    @staticmethod
    async def get_dashboard_trends(
        db: Session,
        source_ids: List[int],
        timeframe: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        获取仪表板趋势数据

        Args:
            db: 数据库会话
            source_ids: 数据源ID列表
            timeframe: 时间级别
            days: 天数

        Returns:
            趋势数据
        """
        try:
            # 生成日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # 生成示例数据（实际应从数据库获取）
            daily_scores = []
            weekly_scores = []
            monthly_scores = []

            # 生成每日数据
            current_date = start_date
            while current_date <= end_date:
                date_str = current_date.strftime("%Y-%m-%d")

                # 生成随机分数，但保持一定的趋势
                base_score = 70 + 15 * np.sin((current_date - start_date).days / 10)
                random_factor = np.random.normal(0, 3)
                score = min(max(base_score + random_factor, 0), 100)

                daily_scores.append({
                    "date": date_str,
                    "score": round(score, 1)
                })

                current_date += timedelta(days=1)

            # 生成每周数据
            for week in range(days // 7 + 1):
                week_start = start_date + timedelta(days=week*7)
                if week_start > end_date:
                    break

                week_str = week_start.strftime("%Y-%m-%d")

                # 计算周平均分
                week_scores = [
                    score["score"] for score in daily_scores
                    if datetime.strptime(score["date"], "%Y-%m-%d") >= week_start and
                    datetime.strptime(score["date"], "%Y-%m-%d") < week_start + timedelta(days=7)
                ]

                if week_scores:
                    weekly_scores.append({
                        "date": week_str,
                        "score": round(sum(week_scores) / len(week_scores), 1)
                    })

            # 生成每月数据
            current_month = start_date.replace(day=1)
            while current_month <= end_date:
                next_month = current_month.replace(month=current_month.month % 12 + 1)
                if current_month.month == 12:
                    next_month = next_month.replace(year=current_month.year + 1)

                month_str = current_month.strftime("%Y-%m-%d")

                # 计算月平均分
                month_scores = [
                    score["score"] for score in daily_scores
                    if datetime.strptime(score["date"], "%Y-%m-%d") >= current_month and
                    datetime.strptime(score["date"], "%Y-%m-%d") < next_month
                ]

                if month_scores:
                    monthly_scores.append({
                        "date": month_str,
                        "score": round(sum(month_scores) / len(month_scores), 1)
                    })

                current_month = next_month

            return {
                "daily_health_scores": daily_scores,
                "weekly_health_scores": weekly_scores,
                "monthly_health_scores": monthly_scores
            }
        except Exception as e:
            logger.error(f"获取仪表板趋势数据失败: {str(e)}")
            return {}
