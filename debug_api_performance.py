#!/usr/bin/env python3
"""
API性能监控调试脚本
用于测试后端API是否正常工作，并验证数据格式
"""

import requests
import json
import time
from datetime import datetime

def test_api_performance():
    """测试API性能监控功能"""
    base_url = "http://localhost:8000"
    
    print("🔧 API性能监控调试测试")
    print("=" * 50)
    
    # 1. 测试API统计端点
    print("\n1. 📊 测试API统计端点...")
    try:
        # 不使用认证，直接测试
        stats_response = requests.get(f"{base_url}/api/v1/performance/api/stats")
        
        print(f"状态码: {stats_response.status_code}")
        
        if stats_response.status_code == 401:
            print("❌ 需要认证，尝试使用测试token...")
            # 尝试使用测试token
            headers = {"Authorization": "Bearer test_token"}
            stats_response = requests.get(f"{base_url}/api/v1/performance/api/stats", headers=headers)
            print(f"使用token后状态码: {stats_response.status_code}")
        
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            print("✅ API统计获取成功")
            print(f"响应数据: {json.dumps(stats_data, indent=2, ensure_ascii=False)}")
            
            # 验证数据结构
            if stats_data.get("success"):
                data = stats_data.get("data", {})
                required_fields = ["avg", "count", "max", "min"]
                
                print("\n📋 数据结构验证:")
                for field in required_fields:
                    if field in data:
                        print(f"   ✅ {field}: {data[field]}")
                    else:
                        print(f"   ❌ 缺少字段: {field}")
                
                # 检查endpoints字段
                if "endpoints" in data:
                    print(f"   ✅ endpoints: {len(data['endpoints'])} 个端点")
                    for endpoint, stats in data["endpoints"].items():
                        print(f"      - {endpoint}: avg={stats.get('avg_time', 'N/A')}s")
                else:
                    print("   ⚠️ 缺少endpoints字段")
                    
            else:
                print(f"❌ API响应失败: {stats_data}")
                
        else:
            print(f"❌ API请求失败: {stats_response.status_code}")
            print(f"响应内容: {stats_response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 2. 测试端点列表
    print("\n2. 🔗 测试端点列表...")
    try:
        endpoints_response = requests.get(f"{base_url}/api/v1/performance/api/endpoints")
        
        if endpoints_response.status_code == 401:
            headers = {"Authorization": "Bearer test_token"}
            endpoints_response = requests.get(f"{base_url}/api/v1/performance/api/endpoints", headers=headers)
        
        if endpoints_response.status_code == 200:
            endpoints_data = endpoints_response.json()
            print("✅ 端点列表获取成功")
            print(f"端点数量: {len(endpoints_data.get('data', []))}")
            
            for i, endpoint in enumerate(endpoints_data.get('data', [])[:5]):
                print(f"   {i+1}. {endpoint}")
                
        else:
            print(f"❌ 端点列表获取失败: {endpoints_response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 3. 测试趋势数据
    print("\n3. 📈 测试趋势数据...")
    try:
        trend_response = requests.get(f"{base_url}/api/v1/performance/metrics/api?limit=5")
        
        if trend_response.status_code == 401:
            headers = {"Authorization": "Bearer test_token"}
            trend_response = requests.get(f"{base_url}/api/v1/performance/metrics/api?limit=5", headers=headers)
        
        if trend_response.status_code == 200:
            trend_data = trend_response.json()
            print("✅ 趋势数据获取成功")
            print(f"数据点数量: {len(trend_data.get('data', []))}")
            
        else:
            print(f"❌ 趋势数据获取失败: {trend_response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 4. 检查服务状态
    print("\n4. 🔍 检查服务状态...")
    try:
        health_response = requests.get(f"{base_url}/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ 后端服务正常运行")
        else:
            print(f"⚠️ 健康检查异常: {health_response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确认服务是否启动")
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 调试测试完成")
    
    # 5. 生成前端测试建议
    print("\n📝 前端测试建议:")
    print("1. 打开浏览器开发者工具")
    print("2. 访问: http://localhost:8080/#/performance?tab=api")
    print("3. 检查控制台日志，查看:")
    print("   - API调用是否成功")
    print("   - 数据是否正确赋值")
    print("   - 是否有JavaScript错误")
    print("4. 如果数据仍为0，尝试硬刷新(Ctrl+F5)")

if __name__ == "__main__":
    test_api_performance()
