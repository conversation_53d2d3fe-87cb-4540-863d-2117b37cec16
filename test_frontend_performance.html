<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能优化页面测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .data-display {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>性能优化页面功能测试</h1>
        <p>此页面用于测试性能优化相关API端点和前端功能是否正常工作。</p>

        <!-- 认证测试 -->
        <div class="test-section">
            <div class="test-title">1. 用户认证测试</div>
            <button onclick="testAuthentication()">测试认证</button>
            <div id="auth-result" class="test-result loading">等待测试...</div>
        </div>

        <!-- 性能摘要测试 -->
        <div class="test-section">
            <div class="test-title">2. 系统性能摘要</div>
            <button onclick="testPerformanceSummary()">获取性能摘要</button>
            <div id="summary-result" class="test-result loading">等待测试...</div>
            <div id="summary-data" class="data-display" style="display: none;"></div>
            <div id="summary-stats" class="summary-stats" style="display: none;"></div>
        </div>

        <!-- 缓存统计测试 -->
        <div class="test-section">
            <div class="test-title">3. 缓存统计信息</div>
            <button onclick="testCacheStats()">获取缓存统计</button>
            <button onclick="testClearCache()">清空缓存</button>
            <div id="cache-result" class="test-result loading">等待测试...</div>
            <div id="cache-data" class="data-display" style="display: none;"></div>
        </div>

        <!-- API性能测试 -->
        <div class="test-section">
            <div class="test-title">4. API性能统计</div>
            <button onclick="testApiStats()">获取API统计</button>
            <button onclick="testApiEndpoints()">获取API端点</button>
            <div id="api-result" class="test-result loading">等待测试...</div>
            <div id="api-data" class="data-display" style="display: none;"></div>
        </div>

        <!-- 内存监控测试 -->
        <div class="test-section">
            <div class="test-title">5. 内存监控</div>
            <button onclick="testMemoryUsage()">获取内存使用</button>
            <button onclick="testMemoryAnalysis()">获取内存分析</button>
            <div id="memory-result" class="test-result loading">等待测试...</div>
            <div id="memory-data" class="data-display" style="display: none;"></div>
        </div>

        <!-- 大数据处理测试 -->
        <div class="test-section">
            <div class="test-title">6. 大数据处理</div>
            <button onclick="testLargeDataProcessing()">测试大数据处理</button>
            <div id="data-result" class="test-result loading">等待测试...</div>
            <div id="data-processing" class="data-display" style="display: none;"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <div class="test-title">7. 综合功能测试</div>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="all-tests-result" class="test-result loading">等待测试...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let authToken = null;
        const API_BASE = 'http://localhost:8000/api/v1';

        // 认证函数
        async function testAuthentication() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.className = 'test-result loading';
            resultDiv.textContent = '正在认证...';

            try {
                const response = await axios.post(`${API_BASE}/auth/token`, {
                    username: 'admin',
                    password: 'admin123'
                });

                if (response.data.access_token) {
                    authToken = response.data.access_token;
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ 认证成功！Token已获取';
                } else {
                    throw new Error('未获取到访问令牌');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 认证失败: ${error.message}`;
            }
        }

        // 获取认证头
        function getAuthHeaders() {
            if (!authToken) {
                throw new Error('请先进行认证');
            }
            return {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            };
        }

        // 性能摘要测试
        async function testPerformanceSummary() {
            const resultDiv = document.getElementById('summary-result');
            const dataDiv = document.getElementById('summary-data');
            const statsDiv = document.getElementById('summary-stats');

            resultDiv.className = 'test-result loading';
            resultDiv.textContent = '正在获取性能摘要...';

            try {
                const response = await axios.get(`${API_BASE}/performance/summary`, {
                    headers: getAuthHeaders()
                });

                if (response.data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ 性能摘要获取成功';
                    
                    dataDiv.style.display = 'block';
                    dataDiv.textContent = JSON.stringify(response.data.data, null, 2);

                    // 显示统计卡片
                    const data = response.data.data;
                    statsDiv.innerHTML = `
                        <div class="stat-card">
                            <div class="stat-value">${data.cpu.usage_percent.toFixed(1)}%</div>
                            <div class="stat-label">CPU使用率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.memory.percent.toFixed(1)}%</div>
                            <div class="stat-label">内存使用率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.disk.percent.toFixed(1)}%</div>
                            <div class="stat-label">磁盘使用率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.api.avg_response_time}ms</div>
                            <div class="stat-label">平均响应时间</div>
                        </div>
                    `;
                    statsDiv.style.display = 'grid';
                } else {
                    throw new Error(response.data.error || '获取失败');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 获取性能摘要失败: ${error.message}`;
                dataDiv.style.display = 'none';
                statsDiv.style.display = 'none';
            }
        }

        // 缓存统计测试
        async function testCacheStats() {
            const resultDiv = document.getElementById('cache-result');
            const dataDiv = document.getElementById('cache-data');

            resultDiv.className = 'test-result loading';
            resultDiv.textContent = '正在获取缓存统计...';

            try {
                const response = await axios.get(`${API_BASE}/performance/cache/stats`, {
                    headers: getAuthHeaders()
                });

                if (response.data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ 缓存统计获取成功';
                    
                    dataDiv.style.display = 'block';
                    dataDiv.textContent = JSON.stringify(response.data.data, null, 2);
                } else {
                    throw new Error(response.data.error || '获取失败');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 获取缓存统计失败: ${error.message}`;
                dataDiv.style.display = 'none';
            }
        }

        // 清空缓存测试
        async function testClearCache() {
            const resultDiv = document.getElementById('cache-result');

            resultDiv.className = 'test-result loading';
            resultDiv.textContent = '正在清空缓存...';

            try {
                const response = await axios.post(`${API_BASE}/performance/cache/clear`, {}, {
                    headers: getAuthHeaders()
                });

                if (response.data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ 缓存清空成功';
                } else {
                    throw new Error(response.data.error || '清空失败');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 清空缓存失败: ${error.message}`;
            }
        }

        // API统计测试
        async function testApiStats() {
            const resultDiv = document.getElementById('api-result');
            const dataDiv = document.getElementById('api-data');

            resultDiv.className = 'test-result loading';
            resultDiv.textContent = '正在获取API统计...';

            try {
                const response = await axios.get(`${API_BASE}/performance/api/stats`, {
                    headers: getAuthHeaders()
                });

                if (response.data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ API统计获取成功';
                    
                    dataDiv.style.display = 'block';
                    dataDiv.textContent = JSON.stringify(response.data.data, null, 2);
                } else {
                    throw new Error(response.data.error || '获取失败');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 获取API统计失败: ${error.message}`;
                dataDiv.style.display = 'none';
            }
        }

        // API端点测试
        async function testApiEndpoints() {
            const resultDiv = document.getElementById('api-result');
            const dataDiv = document.getElementById('api-data');

            resultDiv.className = 'test-result loading';
            resultDiv.textContent = '正在获取API端点...';

            try {
                const response = await axios.get(`${API_BASE}/performance/api/endpoints`, {
                    headers: getAuthHeaders()
                });

                if (response.data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ API端点获取成功';
                    
                    dataDiv.style.display = 'block';
                    dataDiv.textContent = JSON.stringify(response.data.data, null, 2);
                } else {
                    throw new Error(response.data.error || '获取失败');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 获取API端点失败: ${error.message}`;
                dataDiv.style.display = 'none';
            }
        }

        // 内存使用测试
        async function testMemoryUsage() {
            const resultDiv = document.getElementById('memory-result');
            const dataDiv = document.getElementById('memory-data');

            resultDiv.className = 'test-result loading';
            resultDiv.textContent = '正在获取内存使用情况...';

            try {
                const response = await axios.get(`${API_BASE}/performance/memory/usage`, {
                    headers: getAuthHeaders()
                });

                if (response.data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ 内存使用情况获取成功';
                    
                    dataDiv.style.display = 'block';
                    dataDiv.textContent = JSON.stringify(response.data.data, null, 2);
                } else {
                    throw new Error(response.data.error || '获取失败');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 获取内存使用情况失败: ${error.message}`;
                dataDiv.style.display = 'none';
            }
        }

        // 内存分析测试
        async function testMemoryAnalysis() {
            const resultDiv = document.getElementById('memory-result');
            const dataDiv = document.getElementById('memory-data');

            resultDiv.className = 'test-result loading';
            resultDiv.textContent = '正在获取内存分析...';

            try {
                const response = await axios.get(`${API_BASE}/performance/memory/analysis`, {
                    headers: getAuthHeaders()
                });

                if (response.data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ 内存分析获取成功';
                    
                    dataDiv.style.display = 'block';
                    dataDiv.textContent = JSON.stringify(response.data.data, null, 2);
                } else {
                    throw new Error(response.data.error || '获取失败');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 获取内存分析失败: ${error.message}`;
                dataDiv.style.display = 'none';
            }
        }

        // 大数据处理测试
        async function testLargeDataProcessing() {
            const resultDiv = document.getElementById('data-result');
            const dataDiv = document.getElementById('data-processing');

            resultDiv.className = 'test-result loading';
            resultDiv.textContent = '正在处理大数据...';

            try {
                const response = await axios.get(`${API_BASE}/performance/process-large-dataframe`, {
                    headers: getAuthHeaders()
                });

                if (response.data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ 大数据处理完成';
                    
                    dataDiv.style.display = 'block';
                    dataDiv.textContent = JSON.stringify(response.data.data, null, 2);
                } else {
                    throw new Error(response.data.error || '处理失败');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 大数据处理失败: ${error.message}`;
                dataDiv.style.display = 'none';
            }
        }

        // 运行所有测试
        async function runAllTests() {
            const resultDiv = document.getElementById('all-tests-result');
            resultDiv.className = 'test-result loading';
            resultDiv.textContent = '正在运行所有测试...';

            try {
                // 先进行认证
                await testAuthentication();
                await new Promise(resolve => setTimeout(resolve, 500));

                // 运行所有测试
                await testPerformanceSummary();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testCacheStats();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testApiStats();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testMemoryUsage();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testLargeDataProcessing();

                resultDiv.className = 'test-result success';
                resultDiv.textContent = '✓ 所有测试完成！请查看各个测试结果';
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ 测试过程中出现错误: ${error.message}`;
            }
        }

        // 页面加载完成后自动运行认证测试
        window.addEventListener('load', function() {
            setTimeout(testAuthentication, 1000);
        });
    </script>
</body>
</html>
