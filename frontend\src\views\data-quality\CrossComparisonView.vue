<template>
  <div class="cross-comparison-container">
    <h2>交叉数据对比</h2>

    <el-card class="comparison-card">
      <div slot="header" class="card-header">
        <span>数据源对比配置</span>
        <el-button
          type="primary"
          size="small"
          :loading="loading"
          @click="runComparison"
          :disabled="!isFormValid">
          开始对比分析
        </el-button>
      </div>

      <el-form :model="comparisonForm" label-width="120px" size="small">
        <!-- 对比类型选择 -->
        <el-form-item label="对比类型">
          <el-radio-group v-model="comparisonForm.type">
            <el-radio label="sources">数据源对比</el-radio>
            <el-radio label="periods">时间段对比</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 数据源对比配置 -->
        <template v-if="comparisonForm.type === 'sources'">
          <el-form-item label="主数据源">
            <el-select v-model="comparisonForm.primarySource" placeholder="选择主数据源" style="width: 100%">
              <el-option
                v-for="source in dataSources"
                :key="source.id"
                :label="source.name"
                :value="source.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="对比数据源">
            <el-select
              v-model="comparisonForm.secondarySources"
              multiple
              placeholder="选择对比数据源"
              style="width: 100%">
              <el-option
                v-for="source in filteredSecondarySources"
                :key="source.id"
                :label="source.name"
                :value="source.id">
              </el-option>
            </el-select>
          </el-form-item>
        </template>

        <!-- 时间段对比配置 -->
        <template v-else>
          <el-form-item label="数据源">
            <el-select v-model="comparisonForm.source" placeholder="选择数据源" style="width: 100%">
              <el-option
                v-for="source in dataSources"
                :key="source.id"
                :label="source.name"
                :value="source.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="第一时间段">
            <el-date-picker
              v-model="comparisonForm.periodA"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="第二时间段">
            <el-date-picker
              v-model="comparisonForm.periodB"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>
        </template>

        <!-- 通用配置 -->
        <el-form-item label="时间级别">
          <el-select v-model="comparisonForm.timeframe" placeholder="选择时间级别" style="width: 100%">
            <el-option label="1秒" value="1s"></el-option>
            <el-option label="1分钟" value="1m"></el-option>
            <el-option label="3分钟" value="3m"></el-option>
            <el-option label="5分钟" value="5m"></el-option>
            <el-option label="15分钟" value="15m"></el-option>
            <el-option label="30分钟" value="30m"></el-option>
            <el-option label="1小时" value="1h"></el-option>
            <el-option label="2小时" value="2h"></el-option>
            <el-option label="4小时" value="4h"></el-option>
            <el-option label="6小时" value="6h"></el-option>
            <el-option label="8小时" value="8h"></el-option>
            <el-option label="12小时" value="12h"></el-option>
            <el-option label="1天" value="1d"></el-option>
            <el-option label="3天" value="3d"></el-option>
            <el-option label="1周" value="1w"></el-option>
            <el-option label="1月" value="1M"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="对比指标">
          <el-checkbox-group v-model="comparisonForm.metrics">
            <el-checkbox label="price">价格</el-checkbox>
            <el-checkbox label="volume">交易量</el-checkbox>
            <el-checkbox label="completeness">数据完整性</el-checkbox>
            <el-checkbox label="accuracy">数据准确性</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 对比结果展示 -->
    <el-card class="comparison-card" v-if="comparisonResult">
      <div slot="header" class="card-header">
        <span>对比分析结果</span>
        <el-button
          type="success"
          size="small"
          @click="exportResults">
          导出结果
        </el-button>
      </div>

      <!-- 结果摘要 -->
      <div class="result-summary">
        <el-alert
          :title="comparisonResult.summary"
          type="info"
          :closable="false"
          show-icon>
        </el-alert>
      </div>

      <!-- 图表展示 -->
      <div class="chart-container" v-if="comparisonResult.charts">
        <h3>数据对比图表</h3>
        <div class="chart" ref="comparisonChart" style="height: 400px;"></div>
      </div>

      <!-- 详细对比数据表格 -->
      <div class="comparison-table" v-if="comparisonResult.details">
        <h3>详细对比数据</h3>
        <el-table :data="comparisonResult.details" border style="width: 100%">
          <el-table-column prop="metric" label="指标"></el-table-column>
          <el-table-column prop="source1" :label="comparisonResult.source1Name || '数据源1'"></el-table-column>
          <el-table-column prop="source2" :label="comparisonResult.source2Name || '数据源2'"></el-table-column>
          <el-table-column prop="difference" label="差异">
            <template slot-scope="scope">
              <span :class="getDifferenceClass(scope.row.difference_pct)">
                {{ scope.row.difference }}
                ({{ scope.row.difference_pct }}%)
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 显著发现 -->
      <div class="significant-findings" v-if="comparisonResult.findings && comparisonResult.findings.length > 0">
        <h3>显著发现</h3>
        <el-timeline>
          <el-timeline-item
            v-for="(finding, index) in comparisonResult.findings"
            :key="index"
            :type="finding.type === 'improvement' ? 'success' : finding.type === 'degradation' ? 'danger' : 'info'"
            :timestamp="finding.timestamp">
            {{ finding.description }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>

    <!-- 无数据提示 -->
    <el-card class="comparison-card" v-else-if="showEmptyState">
      <div class="empty-state">
        <i class="el-icon-data-analysis"></i>
        <p>请配置对比参数并点击"开始对比分析"按钮</p>
      </div>
    </el-card>
  </div>
</template>

<script>
import axios from 'axios';
import * as echarts from 'echarts';

// 创建一个带有CORS配置的axios实例
const apiClient = axios.create({
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  // 允许跨域请求携带凭证
  withCredentials: false
});

export default {
  name: 'CrossComparisonView',
  data() {
    return {
      loading: false,
      dataSources: [],
      comparisonForm: {
        type: 'sources',
        primarySource: null,
        secondarySources: [],
        source: null,
        periodA: null,
        periodB: null,
        timeframe: '1h',
        metrics: ['price', 'volume']
      },
      comparisonResult: null,
      chart: null,
      showEmptyState: true
    };
  },
  computed: {
    filteredSecondarySources() {
      if (!this.comparisonForm.primarySource) {
        return this.dataSources;
      }
      return this.dataSources.filter(source => source.id !== this.comparisonForm.primarySource);
    },
    isFormValid() {
      if (this.comparisonForm.type === 'sources') {
        return this.comparisonForm.primarySource &&
               this.comparisonForm.secondarySources.length > 0 &&
               this.comparisonForm.timeframe &&
               this.comparisonForm.metrics.length > 0;
      } else {
        return this.comparisonForm.source &&
               this.comparisonForm.periodA &&
               this.comparisonForm.periodB &&
               this.comparisonForm.timeframe &&
               this.comparisonForm.metrics.length > 0;
      }
    }
  },
  created() {
    this.fetchDataSources();
  },
  methods: {
    async fetchDataSources() {
      try {
        this.loading = true;
        // 使用正确的API URL和apiClient实例
        const apiUrl = 'http://localhost:8005/api/v1/data/sources';
        console.log('获取数据源列表，请求URL:', apiUrl);
        const response = await apiClient.get(apiUrl);

        if (response.data && Array.isArray(response.data)) {
          this.dataSources = response.data;
        } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
          this.dataSources = response.data.data;
        } else {
          this.$message.error('获取数据源列表失败：无效的响应格式');
        }
      } catch (error) {
        console.error('获取数据源列表失败:', error);
        this.$message.error('获取数据源列表失败：' + (error.message || '未知错误'));

        // 如果API调用失败，提供一些模拟数据以便测试UI
        this.dataSources = [
          { id: 1, name: 'Binance BTC/USDT', exchange: 'binance', symbol: 'BTC/USDT', timeframe: '1h' },
          { id: 2, name: 'Huobi BTC/USDT', exchange: 'huobi', symbol: 'BTC/USDT', timeframe: '1h' },
          { id: 3, name: 'OKEx BTC/USDT', exchange: 'okex', symbol: 'BTC/USDT', timeframe: '1h' }
        ];
        console.log('使用模拟数据源:', this.dataSources);
      } finally {
        this.loading = false;
      }
    },

    async runComparison() {
      if (!this.isFormValid) {
        this.$message.warning('请完成所有必填项');
        return;
      }

      this.loading = true;
      this.showEmptyState = false;

      try {
        let requestData = {};

        if (this.comparisonForm.type === 'sources') {
          // 数据源对比
          requestData = {
            primary_source_id: this.comparisonForm.primarySource,
            secondary_source_ids: this.comparisonForm.secondarySources,
            timeframe: this.comparisonForm.timeframe,
            metrics: this.comparisonForm.metrics,
            comparison_type: 'sources'
          };
        } else {
          // 时间段对比
          requestData = {
            source_id: this.comparisonForm.source,
            period_a: {
              start_date: this.formatDate(this.comparisonForm.periodA[0]),
              end_date: this.formatDate(this.comparisonForm.periodA[1])
            },
            period_b: {
              start_date: this.formatDate(this.comparisonForm.periodB[0]),
              end_date: this.formatDate(this.comparisonForm.periodB[1])
            },
            timeframe: this.comparisonForm.timeframe,
            metrics: this.comparisonForm.metrics,
            comparison_type: 'periods'
          };
        }

        // 调用API - 使用正确的API URL和apiClient实例
        const apiUrl = 'http://localhost:8005/api/v1/data-quality/cross-comparison';
        console.log('发送交叉数据对比请求到:', apiUrl);
        const response = await apiClient.post(apiUrl, requestData);

        if (response.data && response.data.success) {
          this.comparisonResult = response.data.data;
          this.$nextTick(() => {
            this.initChart();
          });
          this.$message.success('数据对比分析完成');
        } else {
          this.$message.error(response.data?.message || '数据对比分析失败');
        }
      } catch (error) {
        console.error('数据对比分析失败:', error);
        this.$message.error('数据对比分析失败：' + (error.message || '未知错误'));

        // 模拟数据用于开发测试
        this.generateMockResult();
      } finally {
        this.loading = false;
      }
    },

    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    },

    initChart() {
      if (!this.comparisonResult || !this.comparisonResult.charts || !this.$refs.comparisonChart) {
        return;
      }

      // 销毁之前的图表实例
      if (this.chart) {
        this.chart.dispose();
      }

      // 初始化图表
      this.chart = echarts.init(this.$refs.comparisonChart);

      // 设置图表选项
      const option = {
        title: {
          text: '数据对比分析',
          left: 'center',
          textStyle: {
            color: '#00F7FF'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: this.comparisonResult.charts.legend || [],
          top: 30,
          textStyle: {
            color: '#fff'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.comparisonResult.charts.xAxis || [],
          axisLabel: {
            color: '#fff'
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff'
          }
        },
        series: this.comparisonResult.charts.series || []
      };

      // 设置图表
      this.chart.setOption(option);

      // 响应窗口大小变化
      window.addEventListener('resize', this.resizeChart);
    },

    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    },

    getDifferenceClass(value) {
      if (!value) return '';
      const numValue = parseFloat(value);
      if (numValue > 5) return 'text-danger';
      if (numValue < -5) return 'text-success';
      return 'text-warning';
    },

    exportResults() {
      if (!this.comparisonResult) {
        this.$message.warning('没有可导出的结果');
        return;
      }

      // 创建导出内容
      const content = JSON.stringify(this.comparisonResult, null, 2);
      const blob = new Blob([content], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      // 创建下载链接
      const a = document.createElement('a');
      a.href = url;
      a.download = `data-comparison-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);

      this.$message.success('结果已导出');
    },

    // 生成模拟数据用于开发测试
    generateMockResult() {
      this.comparisonResult = {
        summary: "数据对比分析完成。发现2个显著差异，其中价格数据差异为2.3%，交易量数据差异为8.7%。",
        source1Name: "Binance BTC/USDT",
        source2Name: "Huobi BTC/USDT",
        details: [
          {
            metric: "价格平均值",
            source1: "32456.78",
            source2: "32987.65",
            difference: "530.87",
            difference_pct: "1.63"
          },
          {
            metric: "价格最大值",
            source1: "33789.45",
            source2: "34123.56",
            difference: "334.11",
            difference_pct: "0.99"
          },
          {
            metric: "价格最小值",
            source1: "31234.56",
            source2: "31345.67",
            difference: "111.11",
            difference_pct: "0.36"
          },
          {
            metric: "交易量平均值",
            source1: "156.78",
            source2: "170.45",
            difference: "13.67",
            difference_pct: "8.72"
          },
          {
            metric: "数据完整性",
            source1: "99.8%",
            source2: "97.5%",
            difference: "-2.3%",
            difference_pct: "-2.30"
          }
        ],
        findings: [
          {
            type: "info",
            timestamp: "2025-04-21 18:30:45",
            description: "两个数据源的价格数据整体差异在可接受范围内（<2%）"
          },
          {
            type: "warning",
            timestamp: "2025-04-21 18:30:46",
            description: "交易量数据存在显著差异（8.72%），建议进一步检查"
          },
          {
            type: "improvement",
            timestamp: "2025-04-21 18:30:47",
            description: "Binance数据源的完整性评分高于Huobi数据源"
          }
        ],
        charts: {
          legend: ["Binance BTC/USDT", "Huobi BTC/USDT"],
          xAxis: ["00:00", "04:00", "08:00", "12:00", "16:00", "20:00", "00:00"],
          series: [
            {
              name: "Binance BTC/USDT",
              type: "line",
              data: [32100, 32300, 32500, 32700, 32900, 33100, 33300],
              smooth: true,
              lineStyle: {
                width: 2,
                color: "#00F7FF"
              },
              itemStyle: {
                color: "#00F7FF"
              }
            },
            {
              name: "Huobi BTC/USDT",
              type: "line",
              data: [32200, 32450, 32700, 32950, 33200, 33450, 33700],
              smooth: true,
              lineStyle: {
                width: 2,
                color: "#FF3477"
              },
              itemStyle: {
                color: "#FF3477"
              }
            }
          ]
        }
      };

      this.$nextTick(() => {
        this.initChart();
      });
    }
  },
  beforeDestroy() {
    // 清理事件监听
    window.removeEventListener('resize', this.resizeChart);

    // 销毁图表实例
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
};
</script>

<style scoped>
.cross-comparison-container {
  padding: 20px;
  background-color: #081428;
  color: #FFFFFF;
  min-height: 100vh;
}

h2 {
  color: #00F7FF;
  text-shadow: 0 0 10px rgba(0,247,255,0.7);
  margin-bottom: 20px;
}

.comparison-card {
  margin-bottom: 20px;
  background-color: rgba(0, 21, 40, 0.8);
  border: 1px solid #00F7FF;
  box-shadow: 0 0 15px rgba(0,247,255,0.3);
  color: #fff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  color: #00F7FF;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(0,247,255,0.7);
  font-size: 16px;
}

.result-summary {
  margin-bottom: 20px;
}

.chart-container, .comparison-table, .significant-findings {
  margin-top: 20px;
  margin-bottom: 20px;
}

h3 {
  color: #00F7FF;
  margin-bottom: 15px;
  font-size: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.empty-state i {
  font-size: 48px;
  color: #00F7FF;
  margin-bottom: 20px;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
}

.text-danger {
  color: #FF3477;
}

.text-success {
  color: #00FF94;
}

.text-warning {
  color: #FFAA00;
}
</style>
