#!/usr/bin/env python3
"""
测试API趋势数据端点
验证超时问题是否已修复
"""

import requests
import json
import time

def test_api_trend():
    """测试API趋势数据"""
    
    print("🚀 开始测试API趋势数据端点...")
    
    # 1. 登录获取token
    print("\n1. 登录获取认证token...")
    login_url = "http://localhost:8000/api/v1/auth/token"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False
    
    # 2. 测试API趋势数据端点
    print("\n2. 测试API趋势数据端点...")
    trend_url = "http://localhost:8000/api/v1/performance/metrics/api"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试不同的limit参数
    test_cases = [
        {"limit": 10, "name": "小数据量"},
        {"limit": 100, "name": "中等数据量"},
        {"limit": 1000, "name": "大数据量"}
    ]
    
    for test_case in test_cases:
        limit = test_case["limit"]
        name = test_case["name"]
        
        print(f"\n   测试 {name} (limit={limit})...")
        
        try:
            start_time = time.time()
            response = requests.get(f"{trend_url}?limit={limit}", headers=headers, timeout=10)
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                trend_data = response.json()
                if trend_data.get("success") and trend_data.get("data"):
                    data_points = len(trend_data["data"])
                    print(f"   ✅ {name}测试成功")
                    print(f"      - 响应时间: {elapsed_time:.3f}s")
                    print(f"      - 数据点数: {data_points}")
                    print(f"      - 请求的数据点: {limit}")
                    
                    if data_points > 0:
                        sample_point = trend_data["data"][0]
                        print(f"      - 示例数据: avg={sample_point.get('avg', 'N/A')}, max={sample_point.get('max', 'N/A')}")
                    
                    if elapsed_time > 5:
                        print(f"   ⚠️ 响应时间较长: {elapsed_time:.3f}s")
                    else:
                        print(f"   ✅ 响应时间正常: {elapsed_time:.3f}s")
                else:
                    print(f"   ❌ {name}测试失败: 响应格式错误")
                    print(f"      响应: {trend_data}")
                    return False
            else:
                print(f"   ❌ {name}测试失败: HTTP {response.status_code}")
                print(f"      响应: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            print(f"   ❌ {name}测试超时 (>10秒)")
            return False
        except Exception as e:
            print(f"   ❌ {name}测试异常: {e}")
            return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 API趋势数据超时修复验证测试")
    print("=" * 60)
    
    success = test_api_trend()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试通过！API趋势数据超时问题已修复！")
        print("✅ 所有数据量级别都能正常响应")
        print("✅ 响应时间在合理范围内")
        print("✅ 数据格式正确完整")
    else:
        print("❌ 测试失败！需要进一步检查修复")
    print("=" * 60)

if __name__ == "__main__":
    main()
