"""
策略管理系统性能测试
测试API响应时间、并发处理能力、内存使用等性能指标
"""

import unittest
import requests
import time
import threading
import statistics
import sys
import os
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

class TestStrategyAPIPerformance(unittest.TestCase):
    """策略管理API性能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.base_url = "http://localhost:8000/api/v1"
        self.performance_results = {}
    
    def measure_response_time(self, url, method='GET', data=None, iterations=10):
        """测量API响应时间"""
        response_times = []
        
        for _ in range(iterations):
            start_time = time.time()
            
            try:
                if method == 'GET':
                    response = requests.get(url, timeout=10)
                elif method == 'POST':
                    response = requests.post(url, json=data, timeout=10)
                
                end_time = time.time()
                
                if response.status_code in [200, 201]:
                    response_times.append((end_time - start_time) * 1000)  # 转换为毫秒
                
            except requests.exceptions.RequestException as e:
                print(f"请求失败: {e}")
                continue
        
        if response_times:
            return {
                'avg': statistics.mean(response_times),
                'min': min(response_times),
                'max': max(response_times),
                'median': statistics.median(response_times),
                'count': len(response_times)
            }
        else:
            return None
    
    def test_strategy_types_performance(self):
        """测试策略类型API性能"""
        url = f"{self.base_url}/strategy-types"
        
        result = self.measure_response_time(url, iterations=20)
        
        if result:
            self.performance_results['strategy_types'] = result
            
            # 性能断言
            self.assertLess(result['avg'], 200, "策略类型API平均响应时间应小于200ms")
            self.assertLess(result['max'], 500, "策略类型API最大响应时间应小于500ms")
            
            print(f"策略类型API性能: 平均{result['avg']:.2f}ms, 最大{result['max']:.2f}ms")
        else:
            self.skipTest("策略类型API无响应")
    
    def test_strategy_templates_performance(self):
        """测试策略模板API性能"""
        url = f"{self.base_url}/strategy-templates"
        
        result = self.measure_response_time(url, iterations=20)
        
        if result:
            self.performance_results['strategy_templates'] = result
            
            # 性能断言
            self.assertLess(result['avg'], 300, "策略模板API平均响应时间应小于300ms")
            self.assertLess(result['max'], 800, "策略模板API最大响应时间应小于800ms")
            
            print(f"策略模板API性能: 平均{result['avg']:.2f}ms, 最大{result['max']:.2f}ms")
        else:
            self.skipTest("策略模板API无响应")
    
    def test_code_validation_performance(self):
        """测试代码验证API性能"""
        url = f"{self.base_url}/strategies/validate-code"
        
        test_code = '''
def strategy_logic(data, params):
    """测试策略逻辑"""
    signals = []
    
    # 计算技术指标
    short_ma = data['close'].rolling(5).mean()
    long_ma = data['close'].rolling(20).mean()
    rsi = calculate_rsi(data['close'], 14)
    
    # 生成交易信号
    for i in range(1, len(data)):
        if (short_ma.iloc[i] > long_ma.iloc[i] and 
            short_ma.iloc[i-1] <= long_ma.iloc[i-1] and 
            rsi.iloc[i] < 70):
            signals.append({
                'action': 'BUY',
                'price': data['close'].iloc[i],
                'timestamp': i
            })
        elif (short_ma.iloc[i] < long_ma.iloc[i] and 
              short_ma.iloc[i-1] >= long_ma.iloc[i-1]):
            signals.append({
                'action': 'SELL',
                'price': data['close'].iloc[i],
                'timestamp': i
            })
    
    return signals

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))
'''
        
        data = {
            "code": test_code,
            "code_type": "python"
        }
        
        result = self.measure_response_time(url, method='POST', data=data, iterations=15)
        
        if result:
            self.performance_results['code_validation'] = result
            
            # 性能断言
            self.assertLess(result['avg'], 500, "代码验证API平均响应时间应小于500ms")
            self.assertLess(result['max'], 1000, "代码验证API最大响应时间应小于1000ms")
            
            print(f"代码验证API性能: 平均{result['avg']:.2f}ms, 最大{result['max']:.2f}ms")
        else:
            self.skipTest("代码验证API无响应")
    
    def test_pinescript_conversion_performance(self):
        """测试Pine Script转换API性能"""
        url = f"{self.base_url}/strategies/convert-pinescript"
        
        pine_code = '''
//@version=5
strategy("Performance Test Strategy", overlay=true)

// 输入参数
short_length = input.int(5, title="Short MA Length", minval=1)
long_length = input.int(20, title="Long MA Length", minval=1)
rsi_length = input.int(14, title="RSI Length", minval=1)
rsi_overbought = input.float(70, title="RSI Overbought", minval=50, maxval=100)
rsi_oversold = input.float(30, title="RSI Oversold", minval=0, maxval=50)

// 计算技术指标
short_ma = ta.sma(close, short_length)
long_ma = ta.sma(close, long_length)
rsi = ta.rsi(close, rsi_length)

// 交易条件
long_condition = ta.crossover(short_ma, long_ma) and rsi < rsi_overbought
short_condition = ta.crossunder(short_ma, long_ma) and rsi > rsi_oversold

// 执行交易
if long_condition
    strategy.entry("Long", strategy.long)

if short_condition
    strategy.entry("Short", strategy.short)

// 绘制指标
plot(short_ma, color=color.blue, title="Short MA")
plot(long_ma, color=color.red, title="Long MA")
'''
        
        data = {"pine_code": pine_code}
        
        result = self.measure_response_time(url, method='POST', data=data, iterations=10)
        
        if result:
            self.performance_results['pinescript_conversion'] = result
            
            # 性能断言
            self.assertLess(result['avg'], 1000, "Pine Script转换API平均响应时间应小于1000ms")
            self.assertLess(result['max'], 2000, "Pine Script转换API最大响应时间应小于2000ms")
            
            print(f"Pine Script转换API性能: 平均{result['avg']:.2f}ms, 最大{result['max']:.2f}ms")
        else:
            self.skipTest("Pine Script转换API无响应")
    
    def test_concurrent_requests_performance(self):
        """测试并发请求性能"""
        url = f"{self.base_url}/strategy-types"
        concurrent_users = 10
        requests_per_user = 5
        
        def make_request():
            """单个请求函数"""
            start_time = time.time()
            try:
                response = requests.get(url, timeout=10)
                end_time = time.time()
                return {
                    'success': response.status_code == 200,
                    'response_time': (end_time - start_time) * 1000
                }
            except requests.exceptions.RequestException:
                return {'success': False, 'response_time': None}
        
        # 执行并发测试
        all_results = []
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            # 提交所有任务
            futures = []
            for _ in range(concurrent_users * requests_per_user):
                futures.append(executor.submit(make_request))
            
            # 收集结果
            for future in as_completed(futures):
                result = future.result()
                all_results.append(result)
        
        end_time = time.time()
        total_time = (end_time - start_time) * 1000
        
        # 分析结果
        successful_requests = [r for r in all_results if r['success']]
        success_rate = len(successful_requests) / len(all_results) * 100
        
        if successful_requests:
            response_times = [r['response_time'] for r in successful_requests]
            avg_response_time = statistics.mean(response_times)
            max_response_time = max(response_times)
            
            # 计算吞吐量 (请求/秒)
            throughput = len(successful_requests) / (total_time / 1000)
            
            self.performance_results['concurrent_test'] = {
                'success_rate': success_rate,
                'avg_response_time': avg_response_time,
                'max_response_time': max_response_time,
                'throughput': throughput,
                'total_requests': len(all_results),
                'successful_requests': len(successful_requests)
            }
            
            # 性能断言
            self.assertGreaterEqual(success_rate, 95, "并发测试成功率应大于等于95%")
            self.assertLess(avg_response_time, 500, "并发测试平均响应时间应小于500ms")
            self.assertGreater(throughput, 10, "吞吐量应大于10请求/秒")
            
            print(f"并发测试结果: 成功率{success_rate:.1f}%, 平均响应时间{avg_response_time:.2f}ms, 吞吐量{throughput:.2f}请求/秒")
        else:
            self.fail("并发测试中没有成功的请求")
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'performance_results') and self.performance_results:
            print("\n=== 性能测试总结 ===")
            for api_name, result in self.performance_results.items():
                if isinstance(result, dict) and 'avg' in result:
                    print(f"{api_name}: 平均{result['avg']:.2f}ms, 最大{result['max']:.2f}ms")
                elif isinstance(result, dict) and 'success_rate' in result:
                    print(f"{api_name}: 成功率{result['success_rate']:.1f}%, 吞吐量{result['throughput']:.2f}请求/秒")

class TestMemoryUsage(unittest.TestCase):
    """内存使用测试"""
    
    def test_memory_leak_detection(self):
        """检测内存泄漏"""
        try:
            import psutil
            import gc
        except ImportError:
            self.skipTest("需要安装psutil库进行内存测试")
        
        # 获取当前进程
        process = psutil.Process()
        
        # 记录初始内存使用
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行大量API请求
        url = f"http://localhost:8000/api/v1/strategy-types"
        for _ in range(100):
            try:
                requests.get(url, timeout=5)
            except requests.exceptions.RequestException:
                continue
        
        # 强制垃圾回收
        gc.collect()
        
        # 记录最终内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"内存使用: 初始{initial_memory:.2f}MB, 最终{final_memory:.2f}MB, 增长{memory_increase:.2f}MB")
        
        # 内存增长不应超过50MB
        self.assertLess(memory_increase, 50, "内存增长应小于50MB")

if __name__ == '__main__':
    # 运行性能测试
    unittest.main(verbosity=2)
