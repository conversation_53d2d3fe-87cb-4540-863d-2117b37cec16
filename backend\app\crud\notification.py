"""
通知系统CRUD操作
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime
import uuid

from backend.app.models.notification import AlertRule, Notification


def get_alert_rules(db: Session, user_id: Optional[int] = None, skip: int = 0, limit: int = 100) -> List[AlertRule]:
    """获取告警规则列表"""
    query = db.query(AlertRule)

    if user_id is not None:
        query = query.filter(AlertRule.user_id == user_id)

    return query.order_by(AlertRule.created_at.desc()).offset(skip).limit(limit).all()


def get_alert_rule_by_id(db: Session, rule_id: str, user_id: Optional[int] = None) -> Optional[AlertRule]:
    """根据ID获取告警规则"""
    import logging
    logger = logging.getLogger("simple_api")

    logger.info(f"查找规则: rule_id={rule_id}, user_id={user_id}")

    # 打印所有规则，用于调试
    all_rules = db.query(AlertRule).all()
    logger.info(f"数据库中的所有规则: {[rule.rule_id for rule in all_rules]}")

    query = db.query(AlertRule).filter(AlertRule.rule_id == rule_id)

    if user_id is not None:
        query = query.filter(AlertRule.user_id == user_id)

    rule = query.first()
    logger.info(f"查找结果: {rule}")
    return rule


def create_alert_rule(db: Session, rule_data: Dict[str, Any], user_id: Optional[int] = None) -> AlertRule:
    """创建告警规则"""
    import logging
    import json
    logger = logging.getLogger("simple_api")

    # 记录输入数据
    logger.info(f"创建规则数据: {rule_data}, user_id: {user_id}")

    # 生成规则ID
    rule_id = rule_data.get('id') or f"rule-{str(uuid.uuid4())[:8]}"
    logger.info(f"生成规则ID: {rule_id}")

    try:
        # 处理通知渠道，确保是JSON字符串
        notify_channels = rule_data.get('notify_channels', ['app'])
        if not isinstance(notify_channels, str):
            notify_channels = json.dumps(notify_channels)

        # 处理条件，确保是JSON字符串
        conditions = rule_data.get('conditions', [])
        if not isinstance(conditions, str):
            conditions = json.dumps(conditions)

        # 准备数据
        db_rule = AlertRule(
            rule_id=rule_id,
            name=rule_data.get('name'),
            type=rule_data.get('type'),
            description=rule_data.get('description', ''),
            level=rule_data.get('level', 'warning'),
            notify_channels=notify_channels,
            conditions=conditions,
            enabled=rule_data.get('enabled', True),
            user_id=user_id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        logger.info(f"创建规则对象: {db_rule}")

        # 保存到数据库
        db.add(db_rule)
        # 注意：这里不提交，让调用者决定何时提交
        # 这样可以在出错时回滚整个事务

        logger.info(f"规则已添加到会话: {db_rule.rule_id}")
        return db_rule

    except Exception as e:
        logger.error(f"创建规则时出错: {str(e)}")
        raise


def update_alert_rule(db: Session, rule_id: str, rule_data: Dict[str, Any], user_id: Optional[int] = None) -> Optional[AlertRule]:
    """更新告警规则"""
    import logging
    import json
    logger = logging.getLogger("simple_api")

    logger.info(f"更新规则: rule_id={rule_id}, user_id={user_id}, data={rule_data}")

    # 查找规则
    db_rule = get_alert_rule_by_id(db, rule_id, user_id)

    if not db_rule:
        logger.warning(f"规则不存在: {rule_id}")
        return None

    try:
        # 更新字段
        if 'name' in rule_data:
            db_rule.name = rule_data['name']
        if 'type' in rule_data:
            db_rule.type = rule_data['type']
        if 'description' in rule_data:
            db_rule.description = rule_data['description']
        if 'level' in rule_data:
            db_rule.level = rule_data['level']
        if 'notify_channels' in rule_data:
            # 处理通知渠道，确保是JSON字符串
            notify_channels = rule_data['notify_channels']
            if not isinstance(notify_channels, str):
                notify_channels = json.dumps(notify_channels)
            db_rule.notify_channels = notify_channels
        if 'conditions' in rule_data:
            # 处理条件，确保是JSON字符串
            conditions = rule_data['conditions']
            if not isinstance(conditions, str):
                conditions = json.dumps(conditions)
            db_rule.conditions = conditions
        if 'enabled' in rule_data:
            db_rule.enabled = rule_data['enabled']

        # 更新时间
        db_rule.updated_at = datetime.now()

        # 注意：这里不提交，让调用者决定何时提交
        # 这样可以在出错时回滚整个事务
        logger.info(f"规则已更新: {db_rule.rule_id}")

        return db_rule

    except Exception as e:
        logger.error(f"更新规则时出错: {str(e)}")
        raise


def delete_alert_rule(db: Session, rule_id: str, user_id: Optional[int] = None) -> bool:
    """删除告警规则"""
    import logging
    logger = logging.getLogger("simple_api")

    logger.info(f"删除规则: rule_id={rule_id}, user_id={user_id}")

    # 查找规则
    db_rule = get_alert_rule_by_id(db, rule_id, user_id)

    if not db_rule:
        logger.warning(f"规则不存在: {rule_id}")
        return False

    try:
        # 删除规则
        db.delete(db_rule)
        # 注意：这里不提交，让调用者决定何时提交
        # 这样可以在出错时回滚整个事务

        logger.info(f"规则已标记为删除: {rule_id}")
        return True

    except Exception as e:
        logger.error(f"删除规则时出错: {str(e)}")
        raise


def get_notifications(db: Session, user_id: Optional[int] = None, skip: int = 0, limit: int = 100, is_read: Optional[bool] = None) -> List[Notification]:
    """获取通知列表"""
    query = db.query(Notification)

    if user_id is not None:
        query = query.filter(Notification.user_id == user_id)

    if is_read is not None:
        query = query.filter(Notification.is_read == is_read)

    return query.order_by(Notification.created_at.desc()).offset(skip).limit(limit).all()


def get_notification_by_id(db: Session, notification_id: int, user_id: Optional[int] = None) -> Optional[Notification]:
    """根据ID获取通知"""
    query = db.query(Notification).filter(Notification.id == notification_id)

    if user_id is not None:
        query = query.filter(Notification.user_id == user_id)

    return query.first()


def create_notification(db: Session, notification_data: Dict[str, Any], user_id: Optional[int] = None) -> Notification:
    """创建通知"""
    # 准备数据
    db_notification = Notification(
        title=notification_data.get('title'),
        content=notification_data.get('content'),
        level=notification_data.get('level', 'info'),
        is_read=notification_data.get('is_read', False),
        user_id=user_id,
        created_at=datetime.now()
    )

    # 保存到数据库
    db.add(db_notification)
    db.commit()
    db.refresh(db_notification)

    return db_notification


def update_notification(db: Session, notification_id: int, notification_data: Dict[str, Any], user_id: Optional[int] = None) -> Optional[Notification]:
    """更新通知"""
    # 查找通知
    db_notification = get_notification_by_id(db, notification_id, user_id)

    if not db_notification:
        return None

    # 更新字段
    if 'title' in notification_data:
        db_notification.title = notification_data['title']
    if 'content' in notification_data:
        db_notification.content = notification_data['content']
    if 'level' in notification_data:
        db_notification.level = notification_data['level']
    if 'is_read' in notification_data:
        db_notification.is_read = notification_data['is_read']

    # 保存到数据库
    db.commit()
    db.refresh(db_notification)

    return db_notification


def delete_notification(db: Session, notification_id: int, user_id: Optional[int] = None) -> bool:
    """删除通知"""
    # 查找通知
    db_notification = get_notification_by_id(db, notification_id, user_id)

    if not db_notification:
        return False

    # 删除通知
    db.delete(db_notification)
    db.commit()

    return True
