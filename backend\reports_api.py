#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask, jsonify, request, Response
import logging
import os
from datetime import datetime, timedelta
from flask_cors import CORS
import markdown

# 设置日志记录
logging.basicConfig(level=logging.DEBUG,
                  format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 健康检查接口
@app.route('/api/v1/health', methods=['GET'])
def health_check():
    logger.info("健康检查请求")
    return jsonify({
        "status": "ok",
        "service": "reports-api",
        "time": datetime.now().isoformat(),
        "version": "1.0.0"
    })

# 报告列表API
@app.route('/api/v1/data-quality/reports/list/<report_type>', methods=['GET'])
def get_reports_list(report_type):
    """获取指定类型的数据质量报告列表"""
    logger.info(f"获取{report_type}报告列表请求")

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)

    # 检查报告目录是否存在
    report_dir = os.path.join("reports", report_type)
    if not os.path.exists(report_dir):
        # 创建目录
        try:
            os.makedirs(report_dir, exist_ok=True)
            logger.info(f"创建报告目录: {report_dir}")
        except Exception as e:
            logger.error(f"创建报告目录失败: {str(e)}")
            return jsonify({
                "success": False,
                "error": {"message": f"报告目录创建失败: {str(e)}"}
            }), 500

    # 获取真实报告文件列表
    reports = []
    try:
        # 获取目录中的所有.md文件
        report_files = [f for f in os.listdir(report_dir) if f.endswith('.md')]

        # 按文件修改时间排序（最新的在前）
        report_files.sort(key=lambda x: os.path.getmtime(os.path.join(report_dir, x)), reverse=True)

        # 处理每个报告文件
        for i, filename in enumerate(report_files):
            file_path = os.path.join(report_dir, filename)
            file_stats = os.stat(file_path)

            # 获取文件大小（KB）
            size_kb = round(file_stats.st_size / 1024, 1)

            # 获取创建时间
            created_time = datetime.fromtimestamp(file_stats.st_mtime)

            # 从文件名或内容中提取日期信息
            date_info = None
            try:
                # 尝试从文件名中提取日期
                if '_' in filename and filename.split('_')[-1].replace('.md', '').isdigit():
                    date_str = filename.split('_')[-1].replace('.md', '')
                    if len(date_str) == 8:  # YYYYMMDD格式
                        date_info = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"

                # 如果从文件名中无法提取，则使用文件修改时间
                if not date_info:
                    date_info = created_time.strftime("%Y-%m-%d")
            except Exception as e:
                logger.warning(f"提取报告日期失败: {str(e)}")
                date_info = created_time.strftime("%Y-%m-%d")

            # 添加报告信息
            reports.append({
                "id": i + 1,
                "filename": filename,
                "path": f"/reports/{report_type}/{filename}",
                "date_info": date_info,
                "size_kb": size_kb,
                "created_time": created_time.isoformat()
            })

        # 如果没有找到报告文件，创建一个示例报告
        if not reports:
            logger.warning(f"未找到{report_type}报告文件，创建示例报告")

            # 创建示例报告文件
            example_filename = f"example_{report_type}_report.md"
            example_path = os.path.join(report_dir, example_filename)

            with open(example_path, 'w', encoding='utf-8') as f:
                f.write(f"""# {report_type.capitalize()} 数据质量报告示例

## 报告概述

- **报告类型**: {report_type}
- **创建时间**: {datetime.now().isoformat()}
- **状态**: 示例报告

## 说明

这是一个示例报告文件。您可以通过API生成真实的数据质量报告。
""")

            # 获取文件信息
            file_stats = os.stat(example_path)
            size_kb = round(file_stats.st_size / 1024, 1)
            created_time = datetime.fromtimestamp(file_stats.st_mtime)

            # 添加示例报告信息
            reports.append({
                "id": 1,
                "filename": example_filename,
                "path": f"/reports/{report_type}/{example_filename}",
                "date_info": created_time.strftime("%Y-%m-%d"),
                "size_kb": size_kb,
                "created_time": created_time.isoformat()
            })
    except Exception as e:
        logger.error(f"获取报告列表失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": {"message": f"获取报告列表失败: {str(e)}"}
        }), 500

    # 计算总报告数
    total = len(reports)

    # 分页处理
    start_idx = (page - 1) * limit
    end_idx = min(start_idx + limit, total)
    paged_reports = reports[start_idx:end_idx]

    return jsonify({
        "success": True,
        "data": {
            "report_type": report_type,
            "reports": paged_reports,
            "total": total
        }
    })

# 报告内容API
@app.route('/api/v1/data-quality/reports/content/<report_type>/<filename>', methods=['GET'])
def get_report_content(report_type, filename):
    """获取报告内容"""
    logger.info(f"获取报告内容请求: {report_type}/{filename}")

    # 检查报告文件是否存在
    report_path = os.path.join("reports", report_type, filename)
    if not os.path.exists(report_path):
        logger.error(f"报告文件不存在: {report_path}")
        return jsonify({
            "success": False,
            "error": {"message": f"报告文件不存在: {filename}"}
        }), 404

    try:
        # 读取报告文件内容
        with open(report_path, 'r', encoding='utf-8') as f:
            markdown_content = f.read()

        # 获取文件修改时间作为报告日期
        file_stats = os.stat(report_path)
        report_date = datetime.fromtimestamp(file_stats.st_mtime).isoformat()

        # 如果报告内容为空，生成一个默认内容
        if not markdown_content.strip():
            logger.warning(f"报告文件内容为空: {report_path}")
            title = f"{report_type.capitalize()} 数据质量报告"
            markdown_content = f"""# {title}

## 报告概述

- **报告类型**: {report_type}
- **创建时间**: {report_date}
- **状态**: 已完成

## 说明

此报告文件内容为空，请使用报告生成API创建新的报告。
"""

        return jsonify({
            "success": True,
            "data": {
                "content": markdown_content,
                "date": report_date
            }
        })
    except Exception as e:
        logger.error(f"读取报告内容失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": {"message": f"读取报告内容失败: {str(e)}"}
        }), 500

# 报告下载API
@app.route('/api/v1/data-quality/reports/download/<report_type>/<filename>', methods=['GET'])
def download_report(report_type, filename):
    """下载报告"""
    logger.info(f"下载报告请求: {report_type}/{filename}, 格式: {request.args.get('format', 'markdown')}")

    # 检查报告文件是否存在
    report_path = os.path.join("reports", report_type, filename)
    if not os.path.exists(report_path):
        logger.error(f"报告文件不存在: {report_path}")
        return jsonify({
            "success": False,
            "error": {"message": f"报告文件不存在: {filename}"}
        }), 404

    # 获取请求的格式
    format_type = request.args.get('format', 'markdown')

    try:
        # 读取报告文件内容
        with open(report_path, 'r', encoding='utf-8') as f:
            markdown_content = f.read()

        # 获取文件修改时间作为报告日期
        file_stats = os.stat(report_path)
        report_date = datetime.fromtimestamp(file_stats.st_mtime).isoformat()

        # 如果报告内容为空，生成一个默认内容
        if not markdown_content.strip():
            logger.warning(f"报告文件内容为空: {report_path}")
            title = f"{report_type.capitalize()} 数据质量报告"
            markdown_content = f"""# {title}

## 报告概述

- **报告类型**: {report_type}
- **创建时间**: {report_date}
- **状态**: 已完成

## 说明

此报告文件内容为空，请使用报告生成API创建新的报告。
"""

        # 提取报告标题
        title = filename.replace('.md', '').replace('_', ' ').title()
        if markdown_content.startswith('# '):
            title = markdown_content.split('\n')[0].replace('# ', '')

        # 根据请求的格式返回不同的内容
        if format_type == 'markdown':
            # 返回Markdown格式
            return Response(
                markdown_content,
                mimetype='text/markdown',
                headers={
                    'Content-Disposition': f'attachment; filename={filename}'
                }
            )
        elif format_type == 'html':
            # 转换为HTML格式
            try:
                html_content = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>{title}</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; }}
                        h1 {{ border-bottom: 1px solid #eee; padding-bottom: 10px; }}
                        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                        th, td {{ padding: 8px; text-align: left; border: 1px solid #ddd; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    {markdown.markdown(markdown_content, extensions=['tables'])}
                </body>
                </html>
                """

                return Response(
                    html_content,
                    mimetype='text/html',
                    headers={
                        'Content-Disposition': f'attachment; filename={filename.replace(".md", ".html")}'
                    }
                )
            except Exception as e:
                logger.error(f"HTML转换错误: {str(e)}")
                # 如果转换失败，返回原始Markdown
                return Response(
                    markdown_content,
                    mimetype='text/markdown',
                    headers={
                        'Content-Disposition': f'attachment; filename={filename}'
                    }
                )
        elif format_type == 'pdf':
            # 转换为PDF格式
            try:
                # 简化处理，直接返回HTML格式，因为PDF转换需要额外的依赖
                html_content = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>{title}</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; }}
                        h1 {{ border-bottom: 1px solid #eee; padding-bottom: 10px; }}
                        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                        th, td {{ padding: 8px; text-align: left; border: 1px solid #ddd; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    {markdown.markdown(markdown_content, extensions=['tables'])}
                </body>
                </html>
                """

                return Response(
                    html_content,
                    mimetype='text/html',
                    headers={
                        'Content-Disposition': f'attachment; filename={filename.replace(".md", ".html")}'
                    }
                )
            except Exception as e:
                logger.error(f"PDF转换错误: {str(e)}")
                # 如果转换失败，返回原始Markdown
                return Response(
                    markdown_content,
                    mimetype='text/markdown',
                    headers={
                        'Content-Disposition': f'attachment; filename={filename}'
                    }
                )
        else:
            # 不支持的格式
            return jsonify({
                "success": False,
                "error": {"message": f"不支持的格式: {format_type}"}
            }), 400
    except Exception as e:
        logger.error(f"下载报告失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": {"message": f"下载报告失败: {str(e)}"}
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8006, debug=True)
