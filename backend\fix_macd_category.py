#!/usr/bin/env python3
"""
修复MACD策略的分类值，使其与前端期望的分类值匹配
"""

import sqlite3
import sys
import os

def fix_macd_category():
    """修复MACD策略的分类值"""
    try:
        # 连接数据库
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # 检查当前MACD策略的分类
        cursor.execute("SELECT id, name, category FROM strategy_templates WHERE name LIKE '%MACD%'")
        macd_templates = cursor.fetchall()
        
        print("修复前的MACD策略:")
        for template in macd_templates:
            print(f"  ID: {template[0]}, 名称: {template[1]}, 分类: {template[2]}")
        
        # 更新MACD策略的分类从 macd_cross 改为 macd_trend
        cursor.execute("""
            UPDATE strategy_templates 
            SET category = 'macd_trend' 
            WHERE category = 'macd_cross' AND name LIKE '%MACD%'
        """)
        
        affected_rows = cursor.rowcount
        print(f"\n已更新 {affected_rows} 个MACD策略模板的分类")
        
        # 验证修复结果
        cursor.execute("SELECT id, name, category FROM strategy_templates WHERE name LIKE '%MACD%'")
        updated_templates = cursor.fetchall()
        
        print("\n修复后的MACD策略:")
        for template in updated_templates:
            print(f"  ID: {template[0]}, 名称: {template[1]}, 分类: {template[2]}")
        
        # 提交更改
        conn.commit()
        print("\n✅ MACD策略分类修复完成!")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            conn.close()
    
    return True

if __name__ == "__main__":
    print("🔧 开始修复MACD策略分类...")
    success = fix_macd_category()
    
    if success:
        print("\n🎉 修复完成! 现在前端应该能正确显示MACD策略模板了。")
    else:
        print("\n💥 修复失败，请检查错误信息。")
        sys.exit(1)
