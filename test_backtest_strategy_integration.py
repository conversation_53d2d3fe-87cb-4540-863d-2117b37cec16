#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回测策略对接测试脚本
验证回测创建页面是否正确对接策略管理中心的数据
"""

import requests
import json
import time
import sys

def test_backtest_strategy_integration():
    """测试回测与策略管理的对接"""
    print("=" * 60)
    print("回测策略对接测试")
    print("=" * 60)
    
    backend_url = "http://localhost:8000"
    frontend_url = "http://localhost:8080"
    
    # 测试结果统计
    results = {
        "strategy_management_api": False,
        "frontend_strategy_api": False,
        "strategy_data_consistency": False,
        "backtest_create_integration": False
    }
    
    strategy_management_data = None
    frontend_strategy_data = None
    
    # 1. 测试策略管理中心API
    print("\n1. 测试策略管理中心API...")
    try:
        response = requests.get(f"{backend_url}/api/v1/strategies", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                strategy_management_data = data['data']
                print(f"✓ 策略管理中心API正常，策略数量: {len(strategy_management_data)}")
                results["strategy_management_api"] = True
                
                # 显示策略详情
                for i, strategy in enumerate(strategy_management_data[:3]):  # 只显示前3个
                    print(f"  策略{i+1}: ID={strategy.get('id')}, 名称={strategy.get('name')}, 类型={strategy.get('type')}")
            else:
                print(f"✗ 策略管理中心API返回格式错误: {data}")
        else:
            print(f"✗ 策略管理中心API失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 策略管理中心API异常: {e}")
    
    # 2. 测试前端策略API
    print(f"\n2. 测试前端策略API...")
    try:
        response = requests.get(f"{frontend_url}/api/v1/strategies", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                frontend_strategy_data = data['data']
                print(f"✓ 前端策略API正常，策略数量: {len(frontend_strategy_data)}")
                results["frontend_strategy_api"] = True
                
                # 显示策略详情
                for i, strategy in enumerate(frontend_strategy_data[:3]):  # 只显示前3个
                    print(f"  策略{i+1}: ID={strategy.get('id')}, 名称={strategy.get('name')}, 类型={strategy.get('type')}")
            else:
                print(f"✗ 前端策略API返回格式错误: {data}")
        else:
            print(f"✗ 前端策略API失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 前端策略API异常: {e}")
    
    # 3. 测试数据一致性
    print(f"\n3. 测试数据一致性...")
    if strategy_management_data and frontend_strategy_data:
        try:
            # 比较策略数量
            backend_count = len(strategy_management_data)
            frontend_count = len(frontend_strategy_data)
            
            if backend_count == frontend_count:
                print(f"✓ 策略数量一致: {backend_count} 个")
                
                # 比较策略ID和名称
                backend_strategies = {str(s.get('id')): s.get('name') for s in strategy_management_data}
                frontend_strategies = {str(s.get('id')): s.get('name') for s in frontend_strategy_data}
                
                missing_in_frontend = set(backend_strategies.keys()) - set(frontend_strategies.keys())
                extra_in_frontend = set(frontend_strategies.keys()) - set(backend_strategies.keys())
                
                if not missing_in_frontend and not extra_in_frontend:
                    print("✓ 策略ID完全一致")
                    
                    # 检查策略名称是否一致
                    name_mismatches = []
                    for strategy_id in backend_strategies:
                        if backend_strategies[strategy_id] != frontend_strategies.get(strategy_id):
                            name_mismatches.append(strategy_id)
                    
                    if not name_mismatches:
                        print("✓ 策略名称完全一致")
                        results["strategy_data_consistency"] = True
                    else:
                        print(f"✗ 策略名称不一致，涉及策略ID: {name_mismatches}")
                else:
                    if missing_in_frontend:
                        print(f"✗ 前端缺少策略ID: {missing_in_frontend}")
                    if extra_in_frontend:
                        print(f"✗ 前端多出策略ID: {extra_in_frontend}")
            else:
                print(f"✗ 策略数量不一致: 后端{backend_count}个, 前端{frontend_count}个")
        except Exception as e:
            print(f"✗ 数据一致性检查异常: {e}")
    else:
        print("✗ 无法进行数据一致性检查，缺少必要数据")
    
    # 4. 测试回测创建页面集成
    print(f"\n4. 测试回测创建页面集成...")
    try:
        # 模拟前端页面获取策略列表的请求
        response = requests.get(f"{frontend_url}/api/v1/strategies", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                strategies = data['data']
                if len(strategies) > 0:
                    print(f"✓ 回测创建页面可以获取到 {len(strategies)} 个策略")
                    
                    # 检查策略数据格式是否适合下拉框
                    first_strategy = strategies[0]
                    required_fields = ['id', 'name']
                    has_required_fields = all(field in first_strategy for field in required_fields)
                    
                    if has_required_fields:
                        print("✓ 策略数据格式符合下拉框要求")
                        print(f"  示例策略: ID={first_strategy.get('id')}, 名称={first_strategy.get('name')}")
                        results["backtest_create_integration"] = True
                    else:
                        print(f"✗ 策略数据格式不完整，缺少必要字段: {required_fields}")
                else:
                    print("✗ 回测创建页面获取到的策略列表为空")
            else:
                print(f"✗ 回测创建页面API返回格式错误")
        else:
            print(f"✗ 回测创建页面API请求失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 回测创建页面集成测试异常: {e}")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:30} : {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！回测与策略管理对接正常！")
        print("\n✅ 修复成功:")
        print("- 回测创建页面现在从策略管理中心获取真实策略数据")
        print("- 策略选择下拉框显示的是实际存在的策略")
        print("- 前后端数据完全一致")
        return True
    else:
        print("⚠️  部分测试失败，对接仍有问题")
        
        if not results["strategy_management_api"]:
            print("\n❌ 策略管理中心API有问题")
        if not results["frontend_strategy_api"]:
            print("\n❌ 前端策略API有问题")
        if not results["strategy_data_consistency"]:
            print("\n❌ 前后端数据不一致")
        if not results["backtest_create_integration"]:
            print("\n❌ 回测创建页面集成有问题")
        
        return False

def test_specific_strategy_selection():
    """测试特定策略选择功能"""
    print("\n" + "=" * 60)
    print("策略选择功能详细测试")
    print("=" * 60)
    
    frontend_url = "http://localhost:8080"
    
    try:
        # 获取策略列表
        response = requests.get(f"{frontend_url}/api/v1/strategies", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                strategies = data['data']
                
                print(f"可选择的策略列表 ({len(strategies)} 个):")
                for i, strategy in enumerate(strategies):
                    print(f"  {i+1}. ID: {strategy.get('id')}, 名称: {strategy.get('name')}")
                    print(f"     类型: {strategy.get('type', '未知')}, 分类: {strategy.get('category', '未知')}")
                    print(f"     状态: {strategy.get('status', '未知')}")
                    print()
                
                if len(strategies) > 0:
                    print("✅ 策略选择功能正常，用户可以看到真实的策略列表")
                else:
                    print("⚠️  策略列表为空，用户无法选择策略")
            else:
                print("❌ 策略数据格式错误")
        else:
            print(f"❌ 获取策略列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 策略选择功能测试异常: {e}")

if __name__ == "__main__":
    success = test_backtest_strategy_integration()
    test_specific_strategy_selection()
    
    if success:
        print(f"\n🎯 修复总结:")
        print("- 已将回测页面的模拟数据替换为真实API调用")
        print("- 回测创建页面现在正确对接策略管理中心")
        print("- 用户在创建回测时可以看到实际存在的策略")
        print("- 前后端数据保持一致")
    
    sys.exit(0 if success else 1)
