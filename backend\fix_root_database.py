#!/usr/bin/env python3
"""
修复根目录数据库中的策略模板问题
"""

import sqlite3
import json
from datetime import datetime

def fix_root_database():
    """修复根目录数据库"""
    print("🔧 开始修复根目录数据库...")
    
    try:
        # 连接根目录数据库
        conn = sqlite3.connect('../app.db')
        cursor = conn.cursor()
        
        print("1. 修复MACD策略分类...")
        # 更新MACD策略的分类从 macd_cross 改为 macd_trend
        cursor.execute("""
            UPDATE strategy_templates 
            SET category = 'macd_trend' 
            WHERE category = 'macd_cross' AND name LIKE '%MACD%'
        """)
        
        affected_rows = cursor.rowcount
        print(f"   ✅ 已更新 {affected_rows} 个MACD策略模板的分类")
        
        print("2. 检查是否存在布林带回归策略...")
        cursor.execute("SELECT COUNT(*) FROM strategy_templates WHERE name LIKE '%布林带回归%'")
        bollinger_count = cursor.fetchone()[0]
        
        if bollinger_count == 0:
            print("   ⚠️ 没有布林带回归策略，正在添加...")
            
            # 添加布林带回归策略模板
            bollinger_code = '''
def initialize(context):
    """初始化函数"""
    context.symbol = 'BTCUSDT'
    context.period = 20  # 布林带周期
    context.std_dev = 2.0  # 标准差倍数
    context.reversion_threshold = 0.8  # 回归阈值
    
def handle_bar(context, data):
    """处理每个K线数据"""
    # 获取历史价格数据
    prices = data.history(context.symbol, 'close', context.period + 1)
    
    if len(prices) < context.period:
        return {'action': 'HOLD'}
    
    # 计算布林带
    sma = prices.rolling(window=context.period).mean().iloc[-1]
    std = prices.rolling(window=context.period).std().iloc[-1]
    
    upper_band = sma + (std * context.std_dev)
    lower_band = sma - (std * context.std_dev)
    
    current_price = prices.iloc[-1]
    
    # 计算价格在布林带中的位置 (0-1之间)
    band_width = upper_band - lower_band
    if band_width > 0:
        price_position = (current_price - lower_band) / band_width
    else:
        return {'action': 'HOLD'}
    
    # 均值回归策略：当价格接近上轨时卖出，接近下轨时买入
    if price_position >= context.reversion_threshold:
        # 价格接近上轨，预期回归，卖出
        return {
            'action': 'SELL',
            'quantity': 0.5,
            'reason': f'价格位置 {price_position:.2f} 接近上轨，预期回归'
        }
    elif price_position <= (1 - context.reversion_threshold):
        # 价格接近下轨，预期回归，买入
        return {
            'action': 'BUY', 
            'quantity': 0.5,
            'reason': f'价格位置 {price_position:.2f} 接近下轨，预期回归'
        }
    
    return {'action': 'HOLD'}
            '''
            
            parameter_schema = {
                "parameters": [
                    {
                        "name": "period",
                        "type": "integer",
                        "default": 20,
                        "min": 10,
                        "max": 50,
                        "description": "布林带计算周期",
                        "required": True
                    },
                    {
                        "name": "std_dev",
                        "type": "float",
                        "default": 2.0,
                        "min": 1.0,
                        "max": 3.0,
                        "description": "标准差倍数",
                        "required": True
                    },
                    {
                        "name": "reversion_threshold",
                        "type": "float",
                        "default": 0.8,
                        "min": 0.6,
                        "max": 0.95,
                        "description": "回归阈值 (0.8表示接近上下轨80%位置时交易)",
                        "required": True
                    }
                ]
            }
            
            default_parameters = {
                "period": 20,
                "std_dev": 2.0,
                "reversion_threshold": 0.8
            }
            
            # 插入布林带回归策略模板
            cursor.execute('''
                INSERT INTO strategy_templates (
                    name, type, category, description, code_template,
                    parameter_schema, default_parameters, is_builtin, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                '布林带回归策略',
                'mean_reversion',
                'bollinger_reversion',
                '基于布林带的均值回归策略，当价格偏离均值时进行反向交易，等待价格回归',
                bollinger_code.strip(),
                json.dumps(parameter_schema),
                json.dumps(default_parameters),
                1,
                datetime.now().isoformat()
            ))
            
            print("   ✅ 已添加布林带回归策略模板")
        else:
            print(f"   ✅ 布林带回归策略已存在 ({bollinger_count} 个)")
        
        # 提交更改
        conn.commit()
        
        print("3. 验证修复结果...")
        # 验证MACD策略
        cursor.execute("SELECT name, category FROM strategy_templates WHERE name LIKE '%MACD%'")
        macd_results = cursor.fetchall()
        for row in macd_results:
            print(f"   MACD策略: {row[0]} - 分类: {row[1]}")
        
        # 验证布林带回归策略
        cursor.execute("SELECT name, category FROM strategy_templates WHERE name LIKE '%布林带回归%'")
        bollinger_results = cursor.fetchall()
        for row in bollinger_results:
            print(f"   布林带回归策略: {row[0]} - 分类: {row[1]}")
        
        conn.close()
        print("\n✅ 根目录数据库修复完成!")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False
    
    return True

if __name__ == "__main__":
    print("🎯 修复根目录数据库中的策略模板")
    print("=" * 50)
    success = fix_root_database()
    
    if success:
        print("\n🎉 修复完成! 现在前端应该能正确显示策略模板了。")
    else:
        print("\n💥 修复失败，请检查错误信息。")
