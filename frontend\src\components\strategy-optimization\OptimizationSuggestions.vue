<template>
  <div class="optimization-suggestions-container">
    <div v-if="loading" class="loading-container">
      <div class="el-loading-spinner">
        <i class="el-icon-loading"></i>
        <p class="el-loading-text">加载中...</p>
      </div>
    </div>
    <div v-else>
      <div class="current-performance-section">
        <h3>当前策略性能</h3>
        <div class="metrics-grid">
          <div v-for="(value, key) in currentPerformance" :key="key" class="metric-card">
            <div class="metric-title">{{ getMetricTitle(key) }}</div>
            <div class="metric-value">
              {{ formatMetricValue(key, value) }}
            </div>
          </div>
        </div>
      </div>

      <div class="suggestions-section">
        <h3>优化建议</h3>
        <div v-if="!optimizationSuggestions.length" class="no-suggestions">
          当前没有可用的优化建议
        </div>
        <div v-else>
          <div v-for="(suggestion, index) in optimizationSuggestions" :key="index" class="suggestion-card">
            <div class="suggestion-header">
              <div class="parameter-name">{{ getParameterTitle(suggestion.parameter) }}</div>
              <div class="apply-button">
                <el-button type="primary" size="small" @click="applySuggestion(suggestion)">
                  应用建议
                </el-button>
              </div>
            </div>

            <div class="parameter-values">
              <div class="current-value">
                <div class="value-label">当前值</div>
                <div class="value">{{ suggestion.current_value }}</div>
              </div>
              <el-divider direction="vertical" />
              <div class="suggested-value">
                <div class="value-label">建议值</div>
                <div class="value highlight">{{ suggestion.suggested_value }}</div>
              </div>
            </div>

            <div class="expected-improvements">
              <div class="improvements-title">预期改进</div>
              <div class="improvements-grid">
                <div
                  v-for="(value, key) in suggestion.expected_improvement"
                  :key="key"
                  class="improvement-item"
                >
                  <div class="item-label">{{ getMetricTitle(key) }}</div>
                  <div class="item-value positive">
                    {{ formatMetricValue(key, value) }}
                    <span class="change-indicator">
                      ({{ calculateImprovement(currentPerformance[key], value) }})
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="suggestion-reason">
              <div class="reason-title">优化理由</div>
              <div class="reason-text">{{ suggestion.reason }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优化确认对话框 -->
    <el-dialog
      :visible.sync="dialogVisible"
      title="确认应用优化"
      width="30%"
      :before-close="handleDialogClose"
      center
      :modal="true"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      :append-to-body="true"
      custom-class="optimization-dialog"
    >
      <div class="dialog-content">
        <p>您确定要将 <strong>{{ selectedParameter }}</strong> 从 <strong>{{ currentValue }}</strong> 修改为 <strong>{{ suggestedValue }}</strong> 吗？</p>
        <p>这将修改策略参数并可能影响策略性能。</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmApplyOptimization">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getStrategyOptimization, applyOptimization } from '@/api';
import { Message } from 'element-ui';

export default {
  name: 'OptimizationSuggestions',
  props: {
    strategyId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      optimizationData: null,
      dialogVisible: false,
      selectedSuggestion: null
    };
  },
  computed: {
    // 对话框显示数据
    selectedParameter() {
      if (!this.selectedSuggestion) return '';
      return this.getParameterTitle(this.selectedSuggestion.parameter);
    },
    currentValue() {
      if (!this.selectedSuggestion) return '';
      return this.selectedSuggestion.current_value;
    },
    suggestedValue() {
      if (!this.selectedSuggestion) return '';
      return this.selectedSuggestion.suggested_value;
    },
    // 计算属性
    currentPerformance() {
      if (!this.optimizationData) return {};
      return this.optimizationData.current_performance;
    },
    optimizationSuggestions() {
      if (!this.optimizationData) return [];
      return this.optimizationData.optimization_suggestions;
    }
  },
  methods: {
    // 加载优化数据
    async loadOptimizationData() {
      this.loading = true;
      try {
        const response = await getStrategyOptimization(this.strategyId);

        // 处理响应数据格式
        let data = response;
        if (response && response.data) {
          data = response.data;
        }

        this.optimizationData = data;
        this.$emit('data-loaded', data);
      } catch (error) {
        console.error('加载策略优化数据失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // 处理应用优化建议
    applySuggestion(suggestion) {
      this.selectedSuggestion = suggestion;
      this.dialogVisible = true;
    },

    // 确认应用优化
    async confirmApplyOptimization() {
      // 增强的空值检查
      if (!this.selectedSuggestion) {
        console.error('selectedSuggestion为空，无法应用优化');
        Message.error('选择的优化建议无效');
        this.dialogVisible = false;
        return;
      }

      // 检查必要的属性
      if (!this.selectedSuggestion.parameter || this.selectedSuggestion.suggested_value === undefined) {
        console.error('优化建议缺少必要属性:', this.selectedSuggestion);
        Message.error('优化建议数据不完整');
        this.dialogVisible = false;
        return;
      }

      // 检查策略ID
      if (!this.strategyId) {
        console.error('策略ID为空，无法应用优化');
        Message.error('策略ID无效');
        this.dialogVisible = false;
        return;
      }

      try {
        const optimizationParams = {
          parameter: this.selectedSuggestion.parameter,
          value: this.selectedSuggestion.suggested_value
        };

        console.log('应用优化参数:', {
          strategyId: this.strategyId,
          params: optimizationParams
        });

        const response = await applyOptimization(this.strategyId, optimizationParams);

        // 处理响应数据格式
        let data = response;
        if (response && response.data) {
          data = response.data;
        }

        console.log('优化应用响应:', data);

        if (data && data.success) {
          Message.success('策略优化参数已应用');
          // 重新加载数据
          this.loadOptimizationData();
          this.$emit('optimization-applied', {
            parameter: this.selectedSuggestion.parameter,
            value: this.selectedSuggestion.suggested_value
          });
        } else {
          const errorMsg = data && data.message ? data.message : '应用策略优化失败';
          console.error('优化应用失败:', data);
          Message.error(errorMsg);
        }
      } catch (error) {
        console.error('应用策略优化失败:', error);

        // 更详细的错误处理
        let errorMessage = '应用策略优化失败';
        if (error.response) {
          // 服务器响应错误
          const status = error.response.status;
          const data = error.response.data;
          errorMessage = `服务器错误 (${status}): ${data.error || data.message || '未知错误'}`;
        } else if (error.request) {
          // 网络错误
          errorMessage = '网络连接失败，请检查网络连接';
        } else {
          // 其他错误
          errorMessage = error.message || '未知错误';
        }

        Message.error(errorMessage);
      } finally {
        this.dialogVisible = false;
      }
    },

    // 关闭对话框
    handleDialogClose() {
      this.dialogVisible = false;
    },

    // 格式化指标名称
    getMetricTitle(key) {
      const titles = {
        annualized_return: '年化收益率',
        sharpe_ratio: '夏普比率',
        max_drawdown: '最大回撤'
      };
      return titles[key] || key;
    },

    // 格式化参数名称
    getParameterTitle(key) {
      const titles = {
        moving_average_period: '移动平均周期',
        profit_take_threshold: '止盈阈值',
        stop_loss_threshold: '止损阈值',
        entry_threshold: '入场阈值',
        position_size: '仓位大小'
      };
      return titles[key] || key;
    },

    // 格式化指标值
    formatMetricValue(key, value) {
      if (key === 'annualized_return' || key === 'max_drawdown') {
        return `${value}%`;
      }
      return value;
    },

    // 计算改进百分比
    calculateImprovement(current, improved) {
      if (!current || !improved) return '';

      const diff = improved - current;
      const percentChange = (diff / Math.abs(current)) * 100;

      if (percentChange > 0) {
        return `+${percentChange.toFixed(1)}%`;
      } else {
        return `${percentChange.toFixed(1)}%`;
      }
    }
  },
  mounted() {
    this.loadOptimizationData();
  },
  watch: {
    strategyId() {
      this.loadOptimizationData();
    }
  }
};
</script>

<style scoped>
.optimization-suggestions-container {
  padding: 20px;
  background-color: var(--card-bg-color, #222639);
  border-radius: 8px;
  margin-bottom: 20px;
}

h3 {
  color: var(--text-color, #e1e1ff);
  margin-bottom: 16px;
  font-size: 18px;
  border-bottom: 1px solid var(--border-color, #2f3451);
  padding-bottom: 8px;
}

.current-performance-section {
  margin-bottom: 24px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.metric-card {
  background-color: var(--card-inner-bg-color, #2f3451);
  padding: 16px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.metric-title {
  font-size: 14px;
  color: var(--text-secondary-color, #a9a9c8);
  margin-bottom: 8px;
}

.metric-value {
  font-size: 20px;
  font-weight: bold;
  color: var(--text-color, #e1e1ff);
}

.suggestion-card {
  background-color: var(--card-inner-bg-color, #2f3451);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.parameter-name {
  font-size: 18px;
  font-weight: bold;
  color: var(--text-color, #e1e1ff);
}

.parameter-values {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 12px;
  border-radius: 6px;
}

.current-value, .suggested-value {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.value-label {
  font-size: 14px;
  color: var(--text-secondary-color, #a9a9c8);
  margin-bottom: 8px;
}

.value {
  font-size: 18px;
  font-weight: bold;
  color: var(--text-color, #e1e1ff);
}

.highlight {
  color: var(--primary-color, #00a8ff);
}

.expected-improvements {
  margin-bottom: 20px;
}

.improvements-title, .reason-title {
  font-size: 16px;
  color: var(--text-color, #e1e1ff);
  margin-bottom: 12px;
}

.improvements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 12px;
  border-radius: 6px;
}

.improvement-item {
  display: flex;
  flex-direction: column;
}

.item-label {
  font-size: 14px;
  color: var(--text-secondary-color, #a9a9c8);
  margin-bottom: 6px;
}

.item-value {
  font-size: 16px;
  font-weight: bold;
}

.positive {
  color: var(--success-color, #67c23a);
}

.change-indicator {
  font-size: 14px;
  font-weight: normal;
  margin-left: 4px;
}

.suggestion-reason {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 12px;
  border-radius: 6px;
}

.reason-text {
  color: var(--text-secondary-color, #a9a9c8);
  line-height: 1.5;
}

.no-suggestions {
  text-align: center;
  padding: 40px;
  color: var(--text-secondary-color, #a9a9c8);
  font-style: italic;
}

.loading-container {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-content {
  margin-bottom: 20px;
}

/* 优化弹窗样式 */
.optimization-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  z-index: 2000 !important;
}

/* 确保弹窗遮罩层正确显示 */
.optimization-dialog .el-dialog__wrapper {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 2000 !important;
}

/* 弹窗内容样式优化 */
.optimization-dialog .el-dialog {
  margin: 0 !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  background-color: var(--card-bg-color, #222639) !important;
  border: 1px solid var(--border-color, #2f3451) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

.optimization-dialog .el-dialog__header {
  background-color: var(--card-bg-color, #222639) !important;
  border-bottom: 1px solid var(--border-color, #2f3451) !important;
  padding: 20px 24px 16px !important;
}

.optimization-dialog .el-dialog__title {
  color: var(--text-color, #e1e1ff) !important;
  font-size: 18px !important;
  font-weight: 600 !important;
}

.optimization-dialog .el-dialog__body {
  background-color: var(--card-bg-color, #222639) !important;
  padding: 20px 24px !important;
  color: var(--text-color, #e1e1ff) !important;
}

.optimization-dialog .el-dialog__footer {
  background-color: var(--card-bg-color, #222639) !important;
  border-top: 1px solid var(--border-color, #2f3451) !important;
  padding: 16px 24px 20px !important;
  text-align: right !important;
}

.optimization-dialog .el-dialog__close {
  color: var(--text-secondary-color, #a9a9c8) !important;
}

.optimization-dialog .el-dialog__close:hover {
  color: var(--text-color, #e1e1ff) !important;
}
</style>