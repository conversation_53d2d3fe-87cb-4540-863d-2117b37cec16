<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API性能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #007bff;
        }
        .button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🚀 API性能监控测试</h1>
        
        <div class="section">
            <h3>1. 登录获取Token</h3>
            <button class="button" onclick="login()">登录</button>
            <div id="loginResult" class="result"></div>
        </div>

        <div class="section">
            <h3>2. 测试API统计</h3>
            <button class="button" onclick="testApiStats()">获取API统计</button>
            <div id="apiStatsResult" class="result"></div>
            <div id="apiStatsDisplay" class="stats-grid"></div>
        </div>

        <div class="section">
            <h3>3. 测试API端点列表</h3>
            <button class="button" onclick="testApiEndpoints()">获取API端点</button>
            <div id="apiEndpointsResult" class="result"></div>
        </div>

        <div class="section">
            <h3>4. 测试性能摘要</h3>
            <button class="button" onclick="testPerformanceSummary()">获取性能摘要</button>
            <div id="performanceSummaryResult" class="result"></div>
        </div>
    </div>

    <script>
        let authToken = null;

        async function login() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.textContent = '正在登录...';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.access_token) {
                    authToken = data.access_token;
                    resultDiv.innerHTML = `<span class="success">✅ 登录成功！</span>\nToken: ${authToken.substring(0, 50)}...`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 登录失败</span>\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n${error.message}`;
            }
        }

        async function testApiStats() {
            const resultDiv = document.getElementById('apiStatsResult');
            const displayDiv = document.getElementById('apiStatsDisplay');
            
            if (!authToken) {
                resultDiv.innerHTML = '<span class="error">❌ 请先登录获取Token</span>';
                return;
            }

            resultDiv.textContent = '正在获取API统计...';
            displayDiv.innerHTML = '';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/performance/api/stats', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<span class="success">✅ API统计获取成功！</span>\n${JSON.stringify(data, null, 2)}`;
                    
                    // 显示统计卡片
                    if (data.success && data.data) {
                        const stats = data.data;
                        displayDiv.innerHTML = `
                            <div class="stat-card">
                                <div class="stat-number">${stats.count || 0}</div>
                                <div class="stat-label">总调用次数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${stats.avg || 0}s</div>
                                <div class="stat-label">平均响应时间</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${stats.total_endpoints || 0}</div>
                                <div class="stat-label">总端点数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${stats.active_endpoints || 0}</div>
                                <div class="stat-label">活跃端点数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${stats.p95 || 0}s</div>
                                <div class="stat-label">P95响应时间</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${Object.keys(stats.endpoints || {}).length}</div>
                                <div class="stat-label">监控端点数</div>
                            </div>
                        `;
                    }
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 获取失败</span>\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n${error.message}`;
            }
        }

        async function testApiEndpoints() {
            const resultDiv = document.getElementById('apiEndpointsResult');
            
            if (!authToken) {
                resultDiv.innerHTML = '<span class="error">❌ 请先登录获取Token</span>';
                return;
            }

            resultDiv.textContent = '正在获取API端点列表...';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/performance/api/endpoints', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    const endpoints = data.success ? data.data : data;
                    resultDiv.innerHTML = `<span class="success">✅ API端点获取成功！总数: ${Array.isArray(endpoints) ? endpoints.length : 0}</span>\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 获取失败</span>\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n${error.message}`;
            }
        }

        async function testPerformanceSummary() {
            const resultDiv = document.getElementById('performanceSummaryResult');
            
            if (!authToken) {
                resultDiv.innerHTML = '<span class="error">❌ 请先登录获取Token</span>';
                return;
            }

            resultDiv.textContent = '正在获取性能摘要...';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/performance/summary', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<span class="success">✅ 性能摘要获取成功！</span>\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 获取失败</span>\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n${error.message}`;
            }
        }

        // 页面加载时自动登录
        window.addEventListener('load', function() {
            console.log('🚀 API性能测试页面已加载');
        });
    </script>
</body>
</html>
