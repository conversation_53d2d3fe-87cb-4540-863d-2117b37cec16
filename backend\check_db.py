#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os
import json

# 数据库路径
DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market_data.db')

def check_db():
    """检查数据库"""
    print(f"检查数据库: {DB_PATH}")
    
    if not os.path.exists(DB_PATH):
        print(f"数据库文件不存在: {DB_PATH}")
        return
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # 获取所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print("数据库中的表:")
    for table in tables:
        print(f"  - {table[0]}")
    
    # 检查quality_reports表
    if ('quality_reports',) in tables:
        print("\n质量报告表:")
        cursor.execute("PRAGMA table_info(quality_reports)")
        columns = cursor.fetchall()
        print("  列:")
        for column in columns:
            print(f"    - {column[1]} ({column[2]})")
        
        # 获取报告数量
        cursor.execute("SELECT COUNT(*) FROM quality_reports")
        count = cursor.fetchone()[0]
        print(f"  报告数量: {count}")
        
        # 获取最新的报告
        if count > 0:
            cursor.execute("SELECT id, title, report_type, source_id, status, created_at FROM quality_reports ORDER BY created_at DESC LIMIT 1")
            report = cursor.fetchone()
            print("\n最新报告:")
            print(f"  ID: {report[0]}")
            print(f"  标题: {report[1]}")
            print(f"  类型: {report[2]}")
            print(f"  数据源ID: {report[3]}")
            print(f"  状态: {report[4]}")
            print(f"  创建时间: {report[5]}")
    
    conn.close()

if __name__ == "__main__":
    check_db()
