#!/usr/bin/env python3
"""
测试缓存历史数据功能
"""

import requests
import json
import time

def test_cache_history():
    """测试缓存历史数据功能"""
    
    print("🔍 测试缓存历史数据功能...")
    print("=" * 60)
    
    # 获取认证token
    print("\n1. 🔐 获取认证令牌...")
    try:
        auth_response = requests.post('http://localhost:8000/api/v1/auth/token', 
                                     json={'username': 'admin', 'password': 'admin123'})
        
        if auth_response.status_code != 200:
            print(f"❌ 认证失败: {auth_response.status_code}")
            return
        
        token = auth_response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print(f"✅ 认证成功")
        
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return
    
    # 生成一些缓存活动来创建历史数据
    print("\n2. 🔄 生成缓存活动...")
    for i in range(5):
        print(f"   第{i+1}次调用API...")
        
        # 调用性能摘要API
        response = requests.get('http://localhost:8000/api/v1/performance/summary', headers=headers)
        if response.status_code == 200:
            print(f"      性能摘要: ✅")
        else:
            print(f"      性能摘要: ❌ {response.status_code}")
        
        # 调用性能分析API
        response = requests.get('http://localhost:8000/api/v1/performance/analysis', headers=headers)
        if response.status_code == 200:
            print(f"      性能分析: ✅")
        else:
            print(f"      性能分析: ❌ {response.status_code}")
        
        # 等待一下
        time.sleep(1)
    
    # 检查缓存统计
    print("\n3. 📊 检查当前缓存统计...")
    response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
    if response.status_code == 200:
        stats = response.json()['data']
        print(f"   当前缓存统计:")
        print(f"      大小: {stats['size']}")
        print(f"      命中: {stats['hits']}")
        print(f"      未命中: {stats['misses']}")
        print(f"      命中率: {stats['hit_rate']:.2%}")
    else:
        print(f"   ❌ 获取缓存统计失败: {response.status_code}")
        return
    
    # 测试缓存历史数据API
    print("\n4. 📈 测试缓存历史数据API...")
    response = requests.get('http://localhost:8000/api/v1/performance/metrics/cache?limit=10', headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            history_data = result['data']
            print(f"   ✅ 获取缓存历史数据成功")
            print(f"   历史记录数量: {len(history_data)}")
            
            if history_data:
                print(f"   最新记录:")
                latest = history_data[-1]
                print(f"      时间: {latest['timestamp']}")
                print(f"      命中率: {latest['hit_rate']:.2%}")
                print(f"      缓存大小: {latest['size']}")
                print(f"      命中次数: {latest['hits']}")
                print(f"      未命中次数: {latest['misses']}")
                
                print(f"\n   📊 历史数据验证:")
                print(f"      是否有真实时间戳: {'✅' if latest['timestamp'] else '❌'}")
                print(f"      是否有真实命中率: {'✅' if latest['hit_rate'] > 0 else '❌'}")
                print(f"      是否有真实缓存大小: {'✅' if latest['size'] > 0 else '❌'}")
            else:
                print(f"   ⚠️ 暂无历史数据（这是正常的，需要时间积累）")
        else:
            print(f"   ❌ API返回失败: {result.get('message', '未知错误')}")
    else:
        print(f"   ❌ 请求失败: {response.status_code}")
        try:
            error_data = response.json()
            print(f"   错误详情: {error_data}")
        except:
            print(f"   响应内容: {response.text}")
    
    # 等待一段时间让历史记录生成
    print("\n5. ⏰ 等待历史记录生成...")
    print("   等待65秒让系统记录历史数据...")
    
    # 在等待期间继续调用API
    for i in range(13):  # 65秒，每5秒调用一次
        time.sleep(5)
        print(f"   等待中... {(i+1)*5}/65秒")
        
        # 调用API保持缓存活动
        requests.get('http://localhost:8000/api/v1/performance/summary', headers=headers)
        requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
    
    # 再次检查历史数据
    print("\n6. 🔍 再次检查历史数据...")
    response = requests.get('http://localhost:8000/api/v1/performance/metrics/cache?limit=10', headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            history_data = result['data']
            print(f"   ✅ 获取缓存历史数据成功")
            print(f"   历史记录数量: {len(history_data)}")
            
            if len(history_data) > 0:
                print(f"   🎉 成功生成历史数据！")
                print(f"   最新记录:")
                latest = history_data[-1]
                print(f"      时间: {latest['timestamp']}")
                print(f"      命中率: {latest['hit_rate']:.2%}")
                print(f"      缓存大小: {latest['size']}")
                
                if len(history_data) > 1:
                    print(f"   历史趋势:")
                    for i, record in enumerate(history_data[-3:]):
                        print(f"      记录{i+1}: 时间={record['timestamp'][:19]}, 命中率={record['hit_rate']:.2%}, 大小={record['size']}")
            else:
                print(f"   ⚠️ 仍然没有历史数据，可能需要更长时间")
        else:
            print(f"   ❌ API返回失败: {result.get('message', '未知错误')}")
    else:
        print(f"   ❌ 请求失败: {response.status_code}")
    
    print("\n" + "=" * 60)
    print("🔍 缓存历史数据功能测试完成")

if __name__ == "__main__":
    test_cache_history()
