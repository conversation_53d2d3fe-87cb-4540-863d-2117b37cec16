#!/usr/bin/env python3
"""
检查所有数据库文件中的MACD策略分类
"""

import sqlite3
import os

def check_database(db_path):
    """检查单个数据库中的策略模板"""
    print(f"\n🔍 检查数据库: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"   ❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否有strategy_templates表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='strategy_templates'")
        if not cursor.fetchone():
            print(f"   ⚠️ 没有strategy_templates表")
            conn.close()
            return
        
        # 查询MACD策略
        cursor.execute("SELECT name, category, type FROM strategy_templates WHERE name LIKE '%MACD%'")
        macd_results = cursor.fetchall()
        
        if macd_results:
            print(f"   ✅ 找到 {len(macd_results)} 个MACD策略:")
            for row in macd_results:
                print(f"      - {row[0]} (类型: {row[2]}, 分类: {row[1]})")
        else:
            print(f"   ❌ 没有找到MACD策略")
        
        # 查询布林带回归策略
        cursor.execute("SELECT name, category, type FROM strategy_templates WHERE name LIKE '%布林带回归%'")
        bollinger_results = cursor.fetchall()
        
        if bollinger_results:
            print(f"   ✅ 找到 {len(bollinger_results)} 个布林带回归策略:")
            for row in bollinger_results:
                print(f"      - {row[0]} (类型: {row[2]}, 分类: {row[1]})")
        else:
            print(f"   ❌ 没有找到布林带回归策略")
        
        # 统计总模板数
        cursor.execute("SELECT COUNT(*) FROM strategy_templates")
        total_count = cursor.fetchone()[0]
        print(f"   📊 总模板数: {total_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ 检查失败: {str(e)}")

def main():
    """主函数"""
    print("🎯 检查所有数据库中的策略模板")
    print("=" * 60)
    
    # 数据库文件列表
    db_files = [
        'app.db',
        'celery.db', 
        'market_data.db',
        'trading_system.db',
        'app/database.db',
        'app/db/database.db',
        'backend/app.db',
        '../app.db'  # 根目录的app.db
    ]
    
    for db_file in db_files:
        check_database(db_file)
    
    print("\n" + "=" * 60)
    print("✨ 检查完成！")

if __name__ == "__main__":
    main()
