#!/usr/bin/env python3
"""
测试图表显示修复
验证API趋势图是否能正常显示
"""

import requests
import json
import time
from datetime import datetime

def test_chart_display():
    """测试图表显示功能"""
    
    print("🎯 测试API趋势图显示修复...")
    
    # 1. 登录获取token
    print("\n1. 登录获取认证token...")
    login_url = "http://localhost:8000/api/v1/auth/token"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. 测试API趋势数据的时间戳
    print("\n2. 测试API趋势数据的时间戳...")
    trend_url = "http://localhost:8000/api/v1/performance/metrics/api?limit=10"
    
    try:
        response = requests.get(trend_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            trend_data = response.json()
            print(f"✅ API请求成功")
            
            if trend_data.get("success") and trend_data.get("data"):
                data_points = trend_data["data"]
                print(f"✅ 数据格式正确")
                print(f"   - 数据点数量: {len(data_points)}")
                
                if len(data_points) > 0:
                    # 分析时间戳
                    now = datetime.now()
                    print(f"\n📅 时间戳分析:")
                    print(f"   - 当前时间: {now.isoformat()}")
                    
                    for i, point in enumerate(data_points[:3]):
                        timestamp = point.get('timestamp')
                        try:
                            parsed_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                            time_diff = (now - parsed_time.replace(tzinfo=None)).total_seconds()
                            print(f"   - 数据点{i+1}: {timestamp}")
                            print(f"     解析时间: {parsed_time}")
                            print(f"     时间差: {time_diff:.1f}秒")
                            print(f"     avg_time: {point.get('avg_time')}")
                            print(f"     max_time: {point.get('max_time')}")
                        except Exception as e:
                            print(f"   - 数据点{i+1}时间戳解析失败: {e}")
                    
                    # 检查时间范围过滤
                    print(f"\n🔍 时间范围过滤测试:")
                    
                    # 测试24小时过滤
                    now_ms = now.timestamp() * 1000
                    start_24h = now_ms - 24 * 60 * 60 * 1000
                    
                    valid_points = []
                    for point in data_points:
                        timestamp = point.get('timestamp')
                        try:
                            parsed_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                            point_ms = parsed_time.timestamp() * 1000
                            if point_ms >= start_24h:
                                valid_points.append(point)
                        except:
                            pass
                    
                    print(f"   - 24小时内数据点: {len(valid_points)}/{len(data_points)}")
                    
                    if len(valid_points) > 0:
                        print(f"   ✅ 有效数据点存在，图表应该能显示")
                        return True
                    else:
                        print(f"   ❌ 没有24小时内的数据点")
                        
                        # 检查数据是否都是未来时间
                        future_count = 0
                        past_count = 0
                        for point in data_points:
                            timestamp = point.get('timestamp')
                            try:
                                parsed_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                if parsed_time > now:
                                    future_count += 1
                                else:
                                    past_count += 1
                            except:
                                pass
                        
                        print(f"   - 未来时间数据点: {future_count}")
                        print(f"   - 过去时间数据点: {past_count}")
                        
                        if future_count > 0:
                            print(f"   ⚠️ 检测到未来时间数据，可能是时区问题")
                        
                        return False
                else:
                    print(f"   ❌ 没有数据点")
                    return False
            else:
                print(f"❌ API响应格式错误")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 API趋势图显示修复验证测试")
    print("=" * 60)
    
    success = test_chart_display()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 图表显示修复验证成功！")
        print("✅ 时间戳格式正确")
        print("✅ 数据在有效时间范围内")
        print("✅ 前端图表应该能正常显示")
        print("\n📋 修复内容:")
        print("  - 修复了时间过滤逻辑")
        print("  - 增强了时间戳调试信息")
        print("  - 添加了过滤失败的备用方案")
        print("  - 确保数据不会被错误过滤")
        print("\n🎊 请检查浏览器页面确认图表是否正确显示！")
    else:
        print("❌ 图表显示修复验证失败！")
        print("需要进一步检查时间戳或过滤逻辑问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
