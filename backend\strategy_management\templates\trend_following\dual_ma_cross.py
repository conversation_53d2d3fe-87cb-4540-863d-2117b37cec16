"""
双均线交叉策略模板
适用于趋势跟踪交易

策略逻辑：
- 当短期均线上穿长期均线时，产生买入信号（金叉）
- 当短期均线下穿长期均线时，产生卖出信号（死叉）
- 支持止损功能
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List

def initialize(context):
    """策略初始化"""
    context.short_period = {{ short_period | default(5) }}
    context.long_period = {{ long_period | default(20) }}
    context.price_type = "{{ price_type | default('close') }}"
    context.use_stop_loss = {{ use_stop_loss | default(True) }}
    context.stop_loss_rate = {{ stop_loss_rate | default(0.02) }}
    
    # 内部状态
    context.position = 0  # 当前仓位
    context.entry_price = 0  # 入场价格
    context.signals = []  # 信号历史

def handle_bar(context, data):
    """处理每个K线数据"""
    # 获取价格数据
    price = data[context.price_type]
    
    # 检查数据长度是否足够
    if len(price) < context.long_period:
        return {'action': 'HOLD', 'reason': '数据不足'}
    
    # 计算均线
    short_ma = price.rolling(window=context.short_period).mean()
    long_ma = price.rolling(window=context.long_period).mean()
    
    # 获取当前和前一个值
    current_short = short_ma.iloc[-1]
    current_long = long_ma.iloc[-1]
    prev_short = short_ma.iloc[-2]
    prev_long = long_ma.iloc[-2]
    current_price = price.iloc[-1]
    
    # 检查均线数据有效性
    if pd.isna(current_short) or pd.isna(current_long) or pd.isna(prev_short) or pd.isna(prev_long):
        return {'action': 'HOLD', 'reason': '均线数据无效'}
    
    # 金叉信号 - 买入
    if prev_short <= prev_long and current_short > current_long and context.position <= 0:
        context.position = 1
        context.entry_price = current_price
        signal = {
            'action': 'BUY',
            'price': current_price,
            'reason': '金叉买入信号',
            'short_ma': current_short,
            'long_ma': current_long,
            'timestamp': data.index[-1] if hasattr(data, 'index') else None
        }
        context.signals.append(signal)
        return signal
    
    # 死叉信号 - 卖出
    if prev_short >= prev_long and current_short < current_long and context.position >= 0:
        context.position = -1
        signal = {
            'action': 'SELL',
            'price': current_price,
            'reason': '死叉卖出信号',
            'short_ma': current_short,
            'long_ma': current_long,
            'timestamp': data.index[-1] if hasattr(data, 'index') else None
        }
        if context.position > 0:
            signal['profit_loss'] = current_price - context.entry_price
            signal['profit_loss_pct'] = (current_price - context.entry_price) / context.entry_price * 100
        context.signals.append(signal)
        return signal
    
    # 止损检查
    if context.use_stop_loss and context.position > 0 and context.entry_price > 0:
        loss_rate = (context.entry_price - current_price) / context.entry_price
        if loss_rate > context.stop_loss_rate:
            context.position = 0
            signal = {
                'action': 'SELL',
                'price': current_price,
                'reason': f'止损 (损失{loss_rate:.2%})',
                'profit_loss': current_price - context.entry_price,
                'profit_loss_pct': -loss_rate * 100,
                'timestamp': data.index[-1] if hasattr(data, 'index') else None
            }
            context.signals.append(signal)
            return signal
    
    return {'action': 'HOLD'}

def get_indicators(context, data):
    """获取技术指标数据（用于图表显示）"""
    price = data[context.price_type]
    
    if len(price) < context.long_period:
        return {}
    
    short_ma = price.rolling(window=context.short_period).mean()
    long_ma = price.rolling(window=context.long_period).mean()
    
    return {
        'short_ma': short_ma,
        'long_ma': long_ma,
        'price': price
    }

def get_performance_metrics(context):
    """获取策略绩效指标"""
    if not context.signals:
        return {}
    
    # 计算交易统计
    buy_signals = [s for s in context.signals if s['action'] == 'BUY']
    sell_signals = [s for s in context.signals if s['action'] == 'SELL' and 'profit_loss' in s]
    
    total_trades = len(sell_signals)
    if total_trades == 0:
        return {'total_trades': 0}
    
    # 盈亏统计
    profits = [s['profit_loss'] for s in sell_signals if s['profit_loss'] > 0]
    losses = [s['profit_loss'] for s in sell_signals if s['profit_loss'] < 0]
    
    win_rate = len(profits) / total_trades * 100 if total_trades > 0 else 0
    avg_profit = np.mean(profits) if profits else 0
    avg_loss = np.mean(losses) if losses else 0
    profit_factor = abs(sum(profits) / sum(losses)) if losses and sum(losses) != 0 else float('inf')
    
    return {
        'total_trades': total_trades,
        'win_rate': win_rate,
        'avg_profit': avg_profit,
        'avg_loss': avg_loss,
        'profit_factor': profit_factor,
        'total_profit_loss': sum(s['profit_loss'] for s in sell_signals)
    }

# 策略元数据
STRATEGY_METADATA = {
    'name': '双均线交叉策略',
    'category': 'trend_following',
    'description': '基于快慢双均线交叉的趋势跟踪策略',
    'parameters': [
        'short_period', 'long_period', 'price_type', 'use_stop_loss', 'stop_loss_rate'
    ],
    'indicators': ['short_ma', 'long_ma'],
    'timeframes': ['1h', '4h', '1d'],
    'markets': ['crypto', 'forex', 'stocks']
}
