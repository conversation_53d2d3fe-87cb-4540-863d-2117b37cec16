// 前端数据流调试脚本
// 在浏览器控制台中运行此脚本来调试数据问题

async function debugFrontendData() {
    console.log('🔍 开始调试前端数据流...');
    
    // 1. 检查认证状态
    console.log('\n1. 检查认证状态...');
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    console.log('Token存在:', !!token);
    if (token) {
        console.log('Token前20字符:', token.substring(0, 20) + '...');
    }
    
    // 2. 测试API调用
    console.log('\n2. 测试API调用...');
    
    try {
        // 获取认证令牌
        const authResponse = await fetch('http://localhost:8000/api/v1/auth/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123'
            })
        });
        
        if (!authResponse.ok) {
            throw new Error(`认证失败: ${authResponse.status}`);
        }
        
        const authData = await authResponse.json();
        const authToken = authData.access_token;
        console.log('✅ 认证成功');
        
        // 3. 测试summary API
        console.log('\n3. 测试summary API...');
        const summaryResponse = await fetch('http://localhost:8000/api/v1/performance/summary', {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!summaryResponse.ok) {
            throw new Error(`Summary API失败: ${summaryResponse.status}`);
        }
        
        const summaryData = await summaryResponse.json();
        console.log('✅ Summary API响应:', summaryData);
        
        // 检查数据结构
        if (summaryData.success && summaryData.data) {
            console.log('📊 Summary数据结构:');
            console.log('- CPU:', summaryData.data.cpu);
            console.log('- Memory:', summaryData.data.memory);
            console.log('- API:', summaryData.data.api);
            console.log('- Cache:', summaryData.data.cache);
            
            // 检查前端期望的字段
            console.log('\n🔍 检查前端期望的字段:');
            const cpu = summaryData.data.cpu;
            if (cpu) {
                console.log('CPU.current:', cpu.current, '(期望: 数字)');
                console.log('CPU.avg:', cpu.avg, '(期望: 数字)');
                console.log('CPU.max:', cpu.max, '(期望: 数字)');
            }
            
            const memory = summaryData.data.memory;
            if (memory) {
                console.log('Memory.current:', memory.current, '(期望: 数字)');
                console.log('Memory.avg:', memory.avg, '(期望: 数字)');
                console.log('Memory.max:', memory.max, '(期望: 数字)');
            }
            
            const api = summaryData.data.api;
            if (api) {
                console.log('API.current_avg:', api.current_avg, '(期望: 数字)');
                console.log('API.overall_avg:', api.overall_avg, '(期望: 数字)');
                console.log('API.max:', api.max, '(期望: 数字)');
            }
            
            const cache = summaryData.data.cache;
            if (cache) {
                console.log('Cache.current_hit_rate:', cache.current_hit_rate, '(期望: 0-1之间的数字)');
                console.log('Cache.avg_hit_rate:', cache.avg_hit_rate, '(期望: 0-1之间的数字)');
                console.log('Cache.size:', cache.size, '(期望: 数字)');
                console.log('Cache.max_size:', cache.max_size, '(期望: 数字)');
            }
        } else {
            console.error('❌ Summary数据格式错误:', summaryData);
        }
        
        // 4. 测试analysis API
        console.log('\n4. 测试analysis API...');
        const analysisResponse = await fetch('http://localhost:8000/api/v1/performance/analysis', {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!analysisResponse.ok) {
            throw new Error(`Analysis API失败: ${analysisResponse.status}`);
        }
        
        const analysisData = await analysisResponse.json();
        console.log('✅ Analysis API响应:', analysisData);
        
        // 5. 测试metrics API
        console.log('\n5. 测试metrics API...');
        const categories = ['cpu', 'memory', 'api', 'cache'];
        
        for (const category of categories) {
            const metricsResponse = await fetch(`http://localhost:8000/api/v1/performance/metrics/${category}?limit=5`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (metricsResponse.ok) {
                const metricsData = await metricsResponse.json();
                console.log(`✅ ${category} metrics:`, metricsData.data?.length || 0, '条记录');
                if (metricsData.data && metricsData.data.length > 0) {
                    console.log(`   示例数据:`, metricsData.data[0]);
                }
            } else {
                console.error(`❌ ${category} metrics失败:`, metricsResponse.status);
            }
        }
        
        // 6. 检查Vue组件状态
        console.log('\n6. 检查Vue组件状态...');
        
        // 尝试获取Vue实例
        const vueApp = document.querySelector('#app').__vue__;
        if (vueApp) {
            console.log('✅ 找到Vue应用实例');
            
            // 查找PerformanceDashboard组件
            function findPerformanceComponent(component) {
                if (component.$options.name === 'PerformanceDashboard') {
                    return component;
                }
                
                if (component.$children) {
                    for (const child of component.$children) {
                        const found = findPerformanceComponent(child);
                        if (found) return found;
                    }
                }
                
                return null;
            }
            
            const perfComponent = findPerformanceComponent(vueApp);
            if (perfComponent) {
                console.log('✅ 找到PerformanceDashboard组件');
                console.log('组件数据状态:');
                console.log('- loading:', perfComponent.loading);
                console.log('- summary:', perfComponent.summary);
                console.log('- analysis:', perfComponent.analysis);
                console.log('- metrics:', Object.keys(perfComponent.metrics).map(key => `${key}: ${perfComponent.metrics[key]?.length || 0}条`));
                
                // 检查summary数据是否正确设置
                if (perfComponent.summary) {
                    console.log('\n🔍 组件summary详细数据:');
                    console.log('- CPU:', perfComponent.summary.cpu);
                    console.log('- Memory:', perfComponent.summary.memory);
                    console.log('- API:', perfComponent.summary.api);
                    console.log('- Cache:', perfComponent.summary.cache);
                }
            } else {
                console.log('❌ 未找到PerformanceDashboard组件');
            }
        } else {
            console.log('❌ 未找到Vue应用实例');
        }
        
        console.log('\n🎉 调试完成！');
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
    }
}

// 运行调试
debugFrontendData();
