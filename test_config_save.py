#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

def test_config_save():
    """测试配置保存功能"""
    
    # 测试数据
    test_config = {
        "trading": {
            "enable_live_trading": True,
            "risk_level": "high",
            "max_position_size": 0.3
        }
    }
    
    print("测试系统配置保存功能...")
    
    # 1. 获取当前配置
    print("1. 获取当前配置...")
    response = requests.get("http://localhost:8000/api/v1/unified-config/system")
    if response.status_code == 200:
        current_config = response.json()
        print(f"当前配置: {json.dumps(current_config, indent=2, ensure_ascii=False)}")
    else:
        print(f"获取配置失败: {response.status_code} - {response.text}")
        return
    
    # 2. 更新配置
    print("\n2. 更新配置...")
    response = requests.put(
        "http://localhost:8000/api/v1/unified-config/system",
        json=test_config,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"更新结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    else:
        print(f"更新配置失败: {response.status_code} - {response.text}")
        return
    
    # 3. 再次获取配置验证
    print("\n3. 验证配置是否已保存...")
    response = requests.get("http://localhost:8000/api/v1/unified-config/system")
    if response.status_code == 200:
        updated_config = response.json()
        print(f"更新后配置: {json.dumps(updated_config, indent=2, ensure_ascii=False)}")
        
        # 检查是否保存成功
        if updated_config['data']['trading']['enable_live_trading'] == True:
            print("✅ 配置保存成功！")
        else:
            print("❌ 配置保存失败！")
    else:
        print(f"获取配置失败: {response.status_code} - {response.text}")

if __name__ == "__main__":
    test_config_save()
