#!/usr/bin/env python3
"""
策略管理功能自动化测试脚本
测试模板生成、代码生成、策略创建等核心功能
"""

import requests
import json
import time
import sys

# 配置
BASE_URL = "http://localhost:8000"
API_PREFIX = "/api/v1"

def test_api_endpoint(endpoint, method="GET", data=None, expected_status=200):
    """测试API端点"""
    url = f"{BASE_URL}{API_PREFIX}{endpoint}"

    try:
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        elif method == "PUT":
            response = requests.put(url, json=data, timeout=10)
        elif method == "DELETE":
            response = requests.delete(url, timeout=10)

        print(f"✓ {method} {endpoint} - 状态码: {response.status_code}")

        if response.status_code == expected_status:
            try:
                result = response.json()
                return True, result
            except:
                return True, response.text
        else:
            print(f"  ❌ 期望状态码 {expected_status}, 实际 {response.status_code}")
            try:
                error_detail = response.json()
                print(f"  错误详情: {error_detail}")
            except:
                print(f"  响应内容: {response.text[:200]}")
            return False, None

    except requests.exceptions.RequestException as e:
        print(f"❌ {method} {endpoint} - 请求失败: {str(e)}")
        return False, None

def test_strategy_templates():
    """测试策略模板功能"""
    print("\n=== 测试策略模板功能 ===")

    # 1. 获取模板列表
    success, templates = test_api_endpoint("/strategy-templates")
    if not success:
        return False

    print(f"  📋 找到 {len(templates.get('data', []))} 个模板")

    if not templates.get('data'):
        print("  ❌ 没有找到任何模板")
        return False

    # 2. 测试代码生成
    template = templates['data'][0]  # 使用第一个模板
    template_id = template['id']

    print(f"  🧪 测试模板: {template['name']} (ID: {template_id})")

    # 使用默认参数生成代码
    generate_data = {
        "parameters": template.get('default_parameters', {})
    }

    success, result = test_api_endpoint(
        f"/strategy-templates/{template_id}/generate",
        method="POST",
        data=generate_data
    )

    if success and result.get('success'):
        print(f"  ✅ 代码生成成功")
        print(f"  📝 生成的代码长度: {len(result.get('code', ''))}")
        return True, template, result.get('code')
    else:
        print(f"  ❌ 代码生成失败: {result}")
        return False

def test_strategy_creation():
    """测试策略创建功能"""
    print("\n=== 测试策略创建功能 ===")

    # 先获取模板和生成代码
    template_result = test_strategy_templates()
    if not template_result:
        return False

    success, template, generated_code = template_result

    # 创建策略数据
    strategy_data = {
        "name": f"测试策略_{int(time.time())}",
        "type": template['type'],
        "category": template['category'],
        "description": "自动化测试创建的策略",
        "code_type": "python",
        "code_content": generated_code,
        "parameters": template.get('default_parameters', {}),
        "template_id": template['id'],
        "symbol": "BTCUSDT",
        "timeframe": "1h"
    }

    success, result = test_api_endpoint(
        "/strategies",
        method="POST",
        data=strategy_data,
        expected_status=201  # 创建资源应该返回201
    )

    if success and result.get('success'):
        strategy_id = result['data']['id']
        print(f"  ✅ 策略创建成功，ID: {strategy_id}")
        return True, strategy_id
    else:
        print(f"  ❌ 策略创建失败: {result}")
        return False

def test_strategy_management():
    """测试策略管理功能"""
    print("\n=== 测试策略管理功能 ===")

    # 1. 获取策略列表
    success, strategies = test_api_endpoint("/strategies")
    if not success:
        return False

    print(f"  📋 找到 {len(strategies.get('data', []))} 个策略")

    # 2. 创建新策略
    creation_result = test_strategy_creation()
    if not creation_result:
        return False

    success, strategy_id = creation_result

    # 3. 获取策略详情
    success, strategy_detail = test_api_endpoint(f"/strategies/{strategy_id}")
    if success:
        print(f"  ✅ 策略详情获取成功")
    else:
        print(f"  ❌ 策略详情获取失败")
        return False

    # 4. 更新策略
    update_data = {
        "description": "更新后的策略描述"
    }

    success, result = test_api_endpoint(
        f"/strategies/{strategy_id}",
        method="PUT",
        data=update_data
    )

    if success and result.get('success'):
        print(f"  ✅ 策略更新成功")
    else:
        print(f"  ❌ 策略更新失败: {result}")

    return True

def test_code_validation():
    """测试代码验证功能"""
    print("\n=== 测试代码验证功能 ===")

    # 测试有效的Python代码
    valid_code = """
def initialize(context):
    context.position = 0

def handle_bar(context, data):
    return {'action': 'HOLD'}
"""

    validation_data = {
        "code": valid_code,
        "code_type": "python"
    }

    success, result = test_api_endpoint(
        "/strategies/validate-code",
        method="POST",
        data=validation_data
    )

    if success and result.get('valid'):
        print(f"  ✅ 代码验证成功")
        return True
    else:
        print(f"  ❌ 代码验证失败: {result}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始策略管理功能自动化测试")
    print("=" * 50)

    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(3)

    # 测试API连通性
    success, _ = test_api_endpoint("/health", expected_status=200)
    if not success:
        # 尝试根路径
        success, _ = test_api_endpoint("/", expected_status=200)
        if not success:
            print("❌ API服务不可用，请检查服务是否正常启动")
            return False

    print("✅ API服务连通正常")

    # 执行各项测试
    tests = [
        ("策略模板功能", test_strategy_templates),
        ("策略管理功能", test_strategy_management),
        ("代码验证功能", test_code_validation)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")

    # 测试结果汇总
    print("\n" + "=" * 50)
    print(f"📊 测试结果汇总: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！策略管理功能正常工作")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
