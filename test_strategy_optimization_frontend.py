#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化前端功能测试脚本
测试前端页面是否能正确加载和显示数据
"""

import requests
import json
import time

def test_frontend_api_calls():
    """测试前端API调用"""
    print("开始测试策略优化前端API调用...")
    print("=" * 60)
    
    base_url = "http://localhost:8080/api/v1"
    
    # 测试1: 策略列表API
    print("1. 测试策略列表API...")
    try:
        response = requests.get(f"{base_url}/strategy", timeout=10)
        if response.status_code == 200:
            strategies = response.json()
            print(f"✓ 策略列表API正常，返回 {len(strategies)} 个策略")
            if strategies:
                strategy_id = strategies[0]['id']
                strategy_name = strategies[0]['name']
                print(f"  选择策略: ID={strategy_id}, 名称={strategy_name}")
            else:
                print("✗ 策略列表为空")
                return False
        else:
            print(f"✗ 策略列表API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 策略列表API异常: {str(e)}")
        return False
    
    # 测试2: 策略绩效API
    print(f"\n2. 测试策略绩效API (策略ID: {strategy_id})...")
    try:
        response = requests.get(f"{base_url}/strategies/{strategy_id}/performance", timeout=10)
        if response.status_code == 200:
            performance_data = response.json()
            print("✓ 策略绩效API正常")
            print(f"  策略名称: {performance_data.get('name', 'N/A')}")
            
            # 检查核心指标
            metrics = performance_data.get('metrics', {})
            if metrics:
                print(f"  年化收益率: {metrics.get('annualized_return', 'N/A')}%")
                print(f"  夏普比率: {metrics.get('sharpe_ratio', 'N/A')}")
                print(f"  最大回撤: {metrics.get('max_drawdown', 'N/A')}%")
            
            # 检查月度收益数据
            monthly_returns = performance_data.get('monthly_returns', [])
            if monthly_returns:
                print(f"  月度收益数据: {len(monthly_returns)} 个月")
            
            # 检查风险指标
            risk_metrics = performance_data.get('risk_metrics', {})
            if risk_metrics:
                print(f"  风险指标: 波动率={risk_metrics.get('volatility', 'N/A')}%")
        else:
            print(f"✗ 策略绩效API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 策略绩效API异常: {str(e)}")
        return False
    
    # 测试3: 策略优化建议API
    print(f"\n3. 测试策略优化建议API (策略ID: {strategy_id})...")
    try:
        response = requests.get(f"{base_url}/strategies/{strategy_id}/optimization", timeout=10)
        if response.status_code == 200:
            optimization_data = response.json()
            print("✓ 策略优化建议API正常")
            print(f"  策略名称: {optimization_data.get('name', 'N/A')}")
            
            # 检查当前绩效
            current_performance = optimization_data.get('current_performance', {})
            if current_performance:
                print(f"  当前年化收益率: {current_performance.get('annualized_return', 'N/A')}%")
                print(f"  当前夏普比率: {current_performance.get('sharpe_ratio', 'N/A')}")
            
            # 检查优化建议
            suggestions = optimization_data.get('optimization_suggestions', [])
            if suggestions:
                print(f"  优化建议数量: {len(suggestions)}")
                for i, suggestion in enumerate(suggestions[:2], 1):  # 只显示前2个
                    print(f"    建议{i}: {suggestion.get('parameter', 'N/A')}")
                    print(f"      当前值: {suggestion.get('current_value', 'N/A')}")
                    print(f"      建议值: {suggestion.get('suggested_value', 'N/A')}")
        else:
            print(f"✗ 策略优化建议API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 策略优化建议API异常: {str(e)}")
        return False
    
    # 测试4: 策略比较API
    print(f"\n4. 测试策略比较API...")
    try:
        # 使用前两个策略进行比较
        if len(strategies) >= 2:
            strategy_ids = [str(strategies[0]['id']), str(strategies[1]['id'])]
            ids_param = '&strategy_ids='.join(strategy_ids)
            response = requests.get(f"{base_url}/strategies/compare?strategy_ids={ids_param}", timeout=10)
            
            if response.status_code == 200:
                comparison_data = response.json()
                print("✓ 策略比较API正常")
                
                compared_strategies = comparison_data.get('strategies', [])
                if compared_strategies:
                    print(f"  比较策略数量: {len(compared_strategies)}")
                    for strategy in compared_strategies:
                        print(f"    策略: {strategy.get('name', 'N/A')}")
                        print(f"      年化收益率: {strategy.get('annualized_return', 'N/A')}%")
                        print(f"      夏普比率: {strategy.get('sharpe_ratio', 'N/A')}")
            else:
                print(f"✗ 策略比较API失败: {response.status_code}")
                return False
        else:
            print("⚠ 策略数量不足，跳过比较测试")
    except Exception as e:
        print(f"✗ 策略比较API异常: {str(e)}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有前端API调用测试通过！")
    print("✅ 策略列表加载正常")
    print("✅ 策略绩效数据正常")
    print("✅ 策略优化建议正常")
    print("✅ 策略比较功能正常")
    print("✅ 前端页面应该能正确显示数据")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = test_frontend_api_calls()
    if not success:
        print("\n❌ 测试失败，请检查相关问题")
        exit(1)
    else:
        print("\n✅ 测试成功，策略优化页面应该正常工作")
