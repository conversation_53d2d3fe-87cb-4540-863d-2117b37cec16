#!/usr/bin/env python3
"""
策略创建向导修复验证脚本

此脚本验证策略创建向导中模板代码生成功能是否正常工作
"""

import requests
import json
import time
import sys

# 配置
BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:8080"

def test_api_health():
    """测试API服务健康状态"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务运行正常")
            return True
        else:
            print(f"❌ API服务异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到API服务: {e}")
        return False

def test_strategy_types():
    """测试策略类型API"""
    try:
        response = requests.get(f"{BASE_URL}/api/v1/strategy-types", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                print(f"✅ 策略类型API正常，共{len(data['data'])}种类型")
                return True
            else:
                print("❌ 策略类型API返回数据异常")
                return False
        else:
            print(f"❌ 策略类型API异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 策略类型API请求失败: {e}")
        return False

def test_strategy_templates():
    """测试策略模板API"""
    try:
        # 测试获取模板列表
        response = requests.get(f"{BASE_URL}/api/v1/strategy-templates", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                templates = data['data']
                print(f"✅ 策略模板API正常，共{len(templates)}个模板")
                
                # 测试第一个模板的代码生成
                if templates:
                    template = templates[0]
                    template_id = template['id']
                    print(f"📝 测试模板代码生成: {template['name']}")
                    
                    # 准备测试参数
                    test_params = {}
                    if template.get('default_parameters'):
                        test_params = template['default_parameters']
                    
                    # 测试代码生成
                    gen_response = requests.post(
                        f"{BASE_URL}/api/v1/strategy-templates/{template_id}/generate",
                        json={"parameters": test_params},
                        timeout=10
                    )
                    
                    if gen_response.status_code == 200:
                        gen_data = gen_response.json()
                        if gen_data.get('success') and gen_data.get('code'):
                            print("✅ 模板代码生成功能正常")
                            print(f"📄 生成的代码长度: {len(gen_data['code'])} 字符")
                            return True
                        else:
                            print(f"❌ 代码生成失败: {gen_data.get('message', '未知错误')}")
                            return False
                    else:
                        print(f"❌ 代码生成API异常，状态码: {gen_response.status_code}")
                        return False
                else:
                    print("⚠️ 没有可用的模板进行测试")
                    return True
            else:
                print("❌ 策略模板API返回数据异常")
                return False
        else:
            print(f"❌ 策略模板API异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 策略模板API请求失败: {e}")
        return False

def test_code_validation():
    """测试代码验证API"""
    try:
        test_code = """
def initialize(context):
    context.short_period = 5
    context.long_period = 20

def handle_data(context, data):
    pass
"""
        
        response = requests.post(
            f"{BASE_URL}/api/v1/strategies/validate-code",
            json={
                "code": test_code,
                "code_type": "python"
            },
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'valid' in data:
                print(f"✅ 代码验证API正常，验证结果: {'通过' if data['valid'] else '失败'}")
                return True
            else:
                print("❌ 代码验证API返回数据异常")
                return False
        else:
            print(f"❌ 代码验证API异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 代码验证API请求失败: {e}")
        return False

def test_frontend_access():
    """测试前端访问"""
    try:
        response = requests.get(f"{FRONTEND_URL}", timeout=10)
        if response.status_code == 200:
            print("✅ 前端服务访问正常")
            return True
        else:
            print(f"❌ 前端服务异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法访问前端服务: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始验证策略创建向导修复...")
    print("=" * 60)
    
    # 等待服务完全启动
    print("⏳ 等待服务完全启动...")
    time.sleep(5)
    
    tests = [
        ("API服务健康检查", test_api_health),
        ("策略类型API测试", test_strategy_types),
        ("策略模板API测试", test_strategy_templates),
        ("代码验证API测试", test_code_validation),
        ("前端服务访问测试", test_frontend_access),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！策略创建向导修复成功！")
        print("\n📝 修复内容:")
        print("  ✅ 添加了第3步代码编辑界面")
        print("  ✅ 添加了第4步代码验证界面")
        print("  ✅ 添加了第5步部署配置界面")
        print("  ✅ 实现了模板代码自动生成功能")
        print("  ✅ 实现了代码验证功能")
        print("  ✅ 添加了Monaco代码编辑器")
        print("  ✅ 完善了量子科技美学样式")
        
        print(f"\n🌐 请访问 {FRONTEND_URL}/#/strategy/wizard 测试策略创建向导")
        return True
    else:
        print("❌ 部分测试失败，请检查相关服务")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
