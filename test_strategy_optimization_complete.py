#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化功能完整测试脚本
验证所有修复的功能是否正常工作
"""

import requests
import json
import time

def test_complete_strategy_optimization():
    """完整测试策略优化功能"""
    print("=" * 60)
    print("策略优化功能完整测试")
    print("=" * 60)
    
    frontend_url = "http://localhost:8080"
    
    # 测试1: 策略列表
    print("\n1. 测试策略列表API...")
    try:
        response = requests.get(f"{frontend_url}/api/v1/strategy", timeout=10)
        if response.status_code == 200:
            strategies = response.json()
            print(f"✓ 策略列表API正常，返回 {len(strategies)} 个策略")
            
            if strategies:
                test_strategy_id = strategies[0]['id']
                print(f"选择策略ID {test_strategy_id} 进行测试")
                return test_strategy_id, strategies
            else:
                print("✗ 没有可用的策略")
                return None, []
        else:
            print(f"✗ 策略列表API失败: {response.status_code}")
            return None, []
    except Exception as e:
        print(f"✗ 策略列表API异常: {e}")
        return None, []

def test_strategy_performance(strategy_id):
    """测试策略绩效API"""
    print(f"\n2. 测试策略绩效API (策略ID: {strategy_id})...")
    
    frontend_url = "http://localhost:8080"
    
    try:
        url = f"{frontend_url}/api/v1/strategies/{strategy_id}/performance"
        params = {
            'start_date': '2025-02-26',
            'end_date': '2025-05-27'
        }
        
        response = requests.get(url, params=params, timeout=10)
        if response.status_code == 200:
            performance_data = response.json()
            print(f"✓ 策略绩效API正常")
            print(f"  策略名称: {performance_data.get('name', 'N/A')}")
            print(f"  年化收益率: {performance_data.get('metrics', {}).get('annualized_return', 'N/A')}%")
            print(f"  夏普比率: {performance_data.get('metrics', {}).get('sharpe_ratio', 'N/A')}")
            print(f"  最大回撤: {performance_data.get('metrics', {}).get('max_drawdown', 'N/A')}%")
            
            monthly_returns = performance_data.get('monthly_returns', [])
            print(f"  月度收益数据: {len(monthly_returns)} 个月")
            
            if monthly_returns:
                print("  月度收益详情:")
                for month_data in monthly_returns[:3]:  # 显示前3个月
                    print(f"    {month_data.get('month', 'N/A')}: {month_data.get('return', 'N/A')}%")
                if len(monthly_returns) > 3:
                    print(f"    ... 还有 {len(monthly_returns) - 3} 个月的数据")
            
            return True, performance_data
        else:
            print(f"✗ 策略绩效API失败: {response.status_code}")
            return False, None
    except Exception as e:
        print(f"✗ 策略绩效API异常: {e}")
        return False, None

def test_strategy_optimization(strategy_id):
    """测试策略优化建议API"""
    print(f"\n3. 测试策略优化建议API (策略ID: {strategy_id})...")
    
    frontend_url = "http://localhost:8080"
    
    try:
        url = f"{frontend_url}/api/v1/strategies/{strategy_id}/optimization"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            optimization_data = response.json()
            print(f"✓ 策略优化建议API正常")
            print(f"  策略名称: {optimization_data.get('name', 'N/A')}")
            
            current_perf = optimization_data.get('current_performance', {})
            print(f"  当前年化收益率: {current_perf.get('annualized_return', 'N/A')}%")
            print(f"  当前夏普比率: {current_perf.get('sharpe_ratio', 'N/A')}")
            print(f"  当前最大回撤: {current_perf.get('max_drawdown', 'N/A')}%")
            
            suggestions = optimization_data.get('optimization_suggestions', [])
            print(f"  优化建议数量: {len(suggestions)}")
            
            for i, suggestion in enumerate(suggestions, 1):
                print(f"    建议{i}: {suggestion.get('parameter', 'N/A')}")
                print(f"      当前值: {suggestion.get('current_value', 'N/A')}")
                print(f"      建议值: {suggestion.get('suggested_value', 'N/A')}")
                print(f"      理由: {suggestion.get('reason', 'N/A')[:50]}...")
            
            improvements = optimization_data.get('potential_improvements', {})
            print(f"  潜在收益提升: {improvements.get('return_increase', 'N/A')}%")
            print(f"  风险降低: {improvements.get('risk_reduction', 'N/A')}%")
            
            return True, optimization_data
        else:
            print(f"✗ 策略优化建议API失败: {response.status_code}")
            return False, None
    except Exception as e:
        print(f"✗ 策略优化建议API异常: {e}")
        return False, None

def test_strategy_comparison():
    """测试策略比较API"""
    print(f"\n4. 测试策略比较API...")
    
    frontend_url = "http://localhost:8080"
    
    try:
        url = f"{frontend_url}/api/v1/strategies/compare"
        params = {
            'strategy_ids': ['1', '2']
        }
        
        response = requests.get(url, params=params, timeout=10)
        if response.status_code == 200:
            comparison_data = response.json()
            print(f"✓ 策略比较API正常")
            
            strategies = comparison_data.get('strategies', [])
            print(f"  比较策略数量: {len(strategies)}")
            
            for strategy in strategies:
                print(f"    策略: {strategy.get('name', 'N/A')}")
                print(f"      年化收益率: {strategy.get('annualized_return', 'N/A')}%")
                print(f"      夏普比率: {strategy.get('sharpe_ratio', 'N/A')}")
                print(f"      最大回撤: {strategy.get('max_drawdown', 'N/A')}%")
            
            metrics = comparison_data.get('comparison_metrics', {})
            print(f"  最佳收益策略: {metrics.get('best_return', 'N/A')}")
            print(f"  最佳夏普策略: {metrics.get('best_sharpe', 'N/A')}")
            print(f"  最低回撤策略: {metrics.get('lowest_drawdown', 'N/A')}")
            
            return True
        else:
            print(f"✗ 策略比较API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 策略比较API异常: {e}")
        return False

def test_optimization_application(strategy_id):
    """测试优化应用API"""
    print(f"\n5. 测试优化应用API (策略ID: {strategy_id})...")
    
    frontend_url = "http://localhost:8080"
    
    try:
        url = f"{frontend_url}/api/v1/strategies/{strategy_id}/optimize"
        data = {
            'parameter': 'moving_average_period',
            'value': 15
        }
        
        response = requests.post(url, json=data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 优化应用API正常")
            print(f"  成功消息: {result.get('message', 'N/A')}")
            print(f"  应用参数: {result.get('applied_parameter', 'N/A')}")
            print(f"  应用值: {result.get('applied_value', 'N/A')}")
            return True
        else:
            print(f"✗ 优化应用API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 优化应用API异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始策略优化功能完整测试...")
    
    # 等待服务启动
    time.sleep(3)
    
    # 测试策略列表
    strategy_id, strategies = test_complete_strategy_optimization()
    
    if strategy_id is None:
        print("\n❌ 无法获取测试策略，终止测试")
        return False
    
    # 测试绩效API
    performance_ok, performance_data = test_strategy_performance(strategy_id)
    
    # 测试优化建议API
    optimization_ok, optimization_data = test_strategy_optimization(strategy_id)
    
    # 测试策略比较API
    comparison_ok = test_strategy_comparison()
    
    # 测试优化应用API
    application_ok = test_optimization_application(strategy_id)
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    results = [
        ("策略列表API", strategy_id is not None),
        ("策略绩效API", performance_ok),
        ("策略优化建议API", optimization_ok),
        ("策略比较API", comparison_ok),
        ("优化应用API", application_ok)
    ]
    
    all_passed = True
    for test_name, passed in results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！策略优化功能完全正常！")
        print("✅ 策略ID类型问题已修复")
        print("✅ API路径问题已修复")
        print("✅ 前端代理功能正常")
        print("✅ 数据加载功能正常")
        print("✅ 优化建议数据结构已修复")
        print("✅ 策略比较功能正常")
        print("✅ 优化应用功能正常")
        print("✅ 月度收益率图表数据正常")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    main()
