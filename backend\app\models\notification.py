"""
通知系统数据库模型
"""
from datetime import datetime
import json
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from backend.app.db.database import Base


class AlertRule(Base):
    """告警规则模型"""
    __tablename__ = "alert_rules"

    id = Column(Integer, primary_key=True, index=True)
    rule_id = Column(String(50), unique=True, index=True, nullable=False, comment="规则ID，格式为rule-xxx")
    name = Column(String(100), nullable=False, comment="规则名称")
    type = Column(String(50), nullable=False, comment="规则类型：price, volume, performance, custom等")
    description = Column(String(500), nullable=True, comment="规则描述")
    level = Column(String(20), nullable=False, comment="告警级别：info, warning, error, critical")
    notify_channels = Column(Text, nullable=False, comment="通知渠道列表，JSON字符串格式")
    conditions = Column(Text, nullable=False, comment="触发条件，JSON字符串格式")
    enabled = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    user_id = Column(Integer, nullable=True, comment="用户ID")

    def to_dict(self):
        """转换为字典"""
        # 解析JSON字符串为Python对象
        notify_channels = json.loads(self.notify_channels) if isinstance(self.notify_channels, str) else self.notify_channels
        conditions = json.loads(self.conditions) if isinstance(self.conditions, str) else self.conditions

        return {
            "id": self.rule_id,  # 使用rule_id作为前端的id
            "name": self.name,
            "type": self.type,
            "description": self.description,
            "level": self.level,
            "notify_channels": notify_channels,
            "conditions": conditions,
            "enabled": self.enabled,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class Notification(Base):
    """通知记录模型"""
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False, comment="通知标题")
    content = Column(Text, nullable=False, comment="通知内容")
    level = Column(String(20), nullable=False, comment="通知级别：info, warning, error, success")
    is_read = Column(Boolean, default=False, comment="是否已读")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    user_id = Column(Integer, nullable=True, comment="用户ID")

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "level": self.level,
            "status": "已读" if self.is_read else "未读",
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "actions": ["查看", "忽略"]  # 默认操作
        }
