<template>
  <div class="notification-detail">
    <el-card shadow="hover" class="detail-card">
      <div slot="header" class="card-header">
        <span class="title">{{ notification.title }}</span>
        <el-tag :type="getLevelType(notification.level)" size="small">{{ getLevelText(notification.level) }}</el-tag>
      </div>
      
      <div class="card-content">
        <div class="meta-info">
          <p class="time"><i class="el-icon-time"></i> {{ formatDate(notification.created_at) }}</p>
          <p v-if="notification.type" class="type">
            <i class="el-icon-info"></i> 类型: {{ getTypeText(notification.type) }}
          </p>
        </div>
        
        <el-divider></el-divider>
        
        <div class="content-section">
          <p>{{ notification.content }}</p>
        </div>
        
        <div v-if="notification.data" class="extra-data">
          <h4>附加信息</h4>
          <pre>{{ JSON.stringify(notification.data, null, 2) }}</pre>
        </div>
      </div>
      
      <div class="actions">
        <el-button @click="$emit('close')" plain>关闭</el-button>
        <el-button 
          v-if="!notification.is_read" 
          type="primary" 
          @click="markAsRead">标为已读</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'NotificationDetail',
  props: {
    notification: {
      type: Object,
      required: true
    }
  },
  methods: {
    formatDate(timestamp) {
      if (!timestamp) return '-';
      const date = new Date(timestamp);
      return date.toLocaleString();
    },
    getLevelType(level) {
      const types = {
        info: 'info',
        warning: 'warning',
        error: 'danger',
        success: 'success'
      };
      return types[level] || 'info';
    },
    getLevelText(level) {
      const texts = {
        info: '信息',
        warning: '警告',
        error: '错误',
        success: '成功'
      };
      return texts[level] || '信息';
    },
    getTypeText(type) {
      const types = {
        market: '市场事件',
        system: '系统事件',
        feature: '功能更新',
        risk: '风险事件',
        account: '账户事件'
      };
      return types[type] || type;
    },
    markAsRead() {
      this.$emit('mark-read', this.notification);
    }
  }
};
</script>

<style scoped>
.notification-detail {
  width: 100%;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.meta-info {
  color: #909399;
  font-size: 14px;
}

.time, .type {
  margin: 5px 0;
}

.content-section {
  margin: 15px 0;
  line-height: 1.6;
}

.extra-data {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  margin-top: 15px;
}

.extra-data pre {
  margin: 0;
  white-space: pre-wrap;
}

.actions {
  margin-top: 20px;
  text-align: right;
}
</style>
