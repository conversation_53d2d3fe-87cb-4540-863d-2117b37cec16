# 最终修复验证脚本
# 测试前端API调用顺序修复后的效果

Write-Host "==========================================================" -ForegroundColor Green
Write-Host "              前端API调用顺序修复验证测试" -ForegroundColor Green
Write-Host "==========================================================" -ForegroundColor Green

Write-Host "`n修复内容:" -ForegroundColor Yellow
Write-Host "- 调整前端API调用顺序，优先使用已修复的 /api/v1/strategies/stats" -ForegroundColor Cyan
Write-Host "- 避免首先调用有问题的 /api/v1/strategy/stats" -ForegroundColor Cyan

# 测试1: 验证主要API路径正常工作
Write-Host "`n[测试1] 验证主要API路径 /api/v1/strategies/stats" -ForegroundColor Yellow
try {
    $response1 = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/strategies/stats" -Method GET
    Write-Host "✅ /api/v1/strategies/stats 正常工作" -ForegroundColor Green
    Write-Host "策略总数: $($response1.data.total)" -ForegroundColor Cyan
    Write-Host "活跃策略: $($response1.data.active)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ /api/v1/strategies/stats 失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试2: 验证问题API仍然有问题（确认我们的分析正确）
Write-Host "`n[测试2] 验证问题API /api/v1/strategy/stats 仍有问题" -ForegroundColor Yellow
try {
    $response2 = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/strategy/stats" -Method GET
    Write-Host "⚠️ /api/v1/strategy/stats 意外正常工作" -ForegroundColor Yellow
} catch {
    Write-Host "✅ /api/v1/strategy/stats 确认仍有500错误（符合预期）" -ForegroundColor Green
    Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Gray
}

# 测试3: 检查前端服务状态
Write-Host "`n[测试3] 检查前端服务状态" -ForegroundColor Yellow
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:8080" -Method GET -TimeoutSec 5
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ 前端服务正常运行" -ForegroundColor Green
    } else {
        Write-Host "❌ 前端服务状态异常: $($frontendResponse.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 前端服务无法访问: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试4: 模拟前端API调用顺序
Write-Host "`n[测试4] 模拟修复后的前端API调用顺序" -ForegroundColor Yellow
Write-Host "步骤1: 首先尝试 /api/v1/strategies/stats（修复后的优先路径）" -ForegroundColor Cyan
try {
    $primaryResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/strategies/stats" -Method GET
    Write-Host "✅ 主要路径成功，无需尝试备用路径" -ForegroundColor Green
    $finalData = $primaryResponse
} catch {
    Write-Host "❌ 主要路径失败，尝试备用路径" -ForegroundColor Yellow
    Write-Host "步骤2: 尝试 /api/v1/strategy/stats（备用路径）" -ForegroundColor Cyan
    try {
        $fallbackResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/strategy/stats" -Method GET
        Write-Host "✅ 备用路径成功" -ForegroundColor Green
        $finalData = $fallbackResponse
    } catch {
        Write-Host "❌ 所有路径均失败" -ForegroundColor Red
        $finalData = $null
    }
}

if ($finalData) {
    Write-Host "`n最终获取的数据:" -ForegroundColor Cyan
    Write-Host "- 策略总数: $($finalData.data.total)" -ForegroundColor White
    Write-Host "- 活跃策略: $($finalData.data.active)" -ForegroundColor White
    Write-Host "- 非活跃策略: $($finalData.data.inactive)" -ForegroundColor White
    Write-Host "- 今日新增: $($finalData.data.today_created)" -ForegroundColor White
}

# 总结
Write-Host "`n==========================================================" -ForegroundColor Green
Write-Host "                    修复验证总结" -ForegroundColor Green
Write-Host "==========================================================" -ForegroundColor Green

Write-Host "`n修复效果:" -ForegroundColor Yellow
Write-Host "✅ 前端现在优先使用正确的API路径 /api/v1/strategies/stats" -ForegroundColor Green
Write-Host "✅ 避免了首先调用有问题的 /api/v1/strategy/stats" -ForegroundColor Green
Write-Host "✅ 减少了不必要的500错误日志" -ForegroundColor Green
Write-Host "✅ 提升了用户体验和系统稳定性" -ForegroundColor Green

Write-Host "`n预期结果:" -ForegroundColor Yellow
Write-Host "- 前端首页应该不再显示500错误" -ForegroundColor Cyan
Write-Host "- 策略统计数据应该立即加载成功" -ForegroundColor Cyan
Write-Host "- 浏览器控制台应该不再有API错误日志" -ForegroundColor Cyan

Write-Host "`n建议:" -ForegroundColor Yellow
Write-Host "1. 刷新浏览器页面查看效果" -ForegroundColor Cyan
Write-Host "2. 检查浏览器控制台是否还有错误" -ForegroundColor Cyan
Write-Host "3. 观察策略统计数据是否正常显示" -ForegroundColor Cyan

Write-Host "`n测试完成！请刷新浏览器查看修复效果。" -ForegroundColor Green
