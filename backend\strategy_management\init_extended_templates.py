#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
扩展策略模板初始化脚本
创建剩余的策略模板：相对强弱、套利策略、网格策略、自定义策略
"""

import os
import sys
import sqlite3
import json
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_extended_templates(conn):
    """创建扩展策略模板"""
    cursor = conn.cursor()

    # 相对强弱策略模板 (动量策略类型)
    relative_strength_code = """
def initialize(context):
    context.rs_period = {{ rs_period | default(14) }}
    context.rs_threshold = {{ rs_threshold | default(0.7) }}
    context.benchmark_symbol = {{ benchmark_symbol | default('BTCUSDT') }}
    context.position = 0

def calculate_relative_strength(prices, benchmark_prices, period=14):
    price_change = (prices.iloc[-1] - prices.iloc[-period-1]) / prices.iloc[-period-1]
    benchmark_change = (benchmark_prices.iloc[-1] - benchmark_prices.iloc[-period-1]) / benchmark_prices.iloc[-period-1]
    return price_change / benchmark_change if benchmark_change != 0 else 1.0

def handle_bar(context, data):
    close_prices = data['close']
    # 假设基准数据在data中
    benchmark_prices = data.get('benchmark', close_prices)

    if len(close_prices) < context.rs_period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}

    rs = calculate_relative_strength(close_prices, benchmark_prices, context.rs_period)
    current_price = close_prices.iloc[-1]

    # 相对强势买入
    if rs > context.rs_threshold and context.position <= 0:
        context.position = 1
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'相对强势买入 (RS:{rs:.4f})'
        }

    # 相对弱势卖出
    elif rs < 1/context.rs_threshold and context.position >= 0:
        context.position = -1
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'相对弱势卖出 (RS:{rs:.4f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("相对强弱策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "相对强弱策略",
            "momentum",
            "relative_strength",
            "基于相对强弱的动量策略，相对强势时买入，相对弱势时卖出",
            relative_strength_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "rs_period",
                        "type": "integer",
                        "default": 14,
                        "min": 5,
                        "max": 30,
                        "description": "相对强弱计算周期",
                        "required": True
                    },
                    {
                        "name": "rs_threshold",
                        "type": "float",
                        "default": 0.7,
                        "min": 0.5,
                        "max": 2.0,
                        "description": "相对强弱阈值",
                        "required": True
                    },
                    {
                        "name": "benchmark_symbol",
                        "type": "string",
                        "default": "BTCUSDT",
                        "description": "基准交易对",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "rs_period": 14,
                "rs_threshold": 0.7,
                "benchmark_symbol": "BTCUSDT"
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建相对强弱策略模板")
    else:
        logger.info("相对强弱策略模板已存在")

    # 统计套利策略模板 (套利策略类型)
    statistical_arbitrage_code = """
def initialize(context):
    context.lookback_period = {{ lookback_period | default(20) }}
    context.entry_threshold = {{ entry_threshold | default(2.0) }}
    context.exit_threshold = {{ exit_threshold | default(0.5) }}
    context.symbol_a = {{ symbol_a | default('BTCUSDT') }}
    context.symbol_b = {{ symbol_b | default('ETHUSDT') }}
    context.position = 0

def calculate_spread_zscore(prices_a, prices_b, period=20):
    spread = prices_a - prices_b
    spread_mean = spread.rolling(period).mean().iloc[-1]
    spread_std = spread.rolling(period).std().iloc[-1]
    current_spread = spread.iloc[-1]
    return (current_spread - spread_mean) / spread_std if spread_std != 0 else 0

def handle_bar(context, data):
    # 假设数据包含两个交易对的价格
    prices_a = data.get('symbol_a', data['close'])
    prices_b = data.get('symbol_b', data['close'])

    if len(prices_a) < context.lookback_period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}

    zscore = calculate_spread_zscore(prices_a, prices_b, context.lookback_period)
    current_price = prices_a.iloc[-1]

    # 价差过大，做空价差（买入B，卖出A）
    if zscore > context.entry_threshold and context.position <= 0:
        context.position = 1
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'统计套利买入 (Z-Score:{zscore:.4f})'
        }

    # 价差过小，做多价差（买入A，卖出B）
    elif zscore < -context.entry_threshold and context.position >= 0:
        context.position = -1
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'统计套利卖出 (Z-Score:{zscore:.4f})'
        }

    # 价差回归，平仓
    elif abs(zscore) < context.exit_threshold and context.position != 0:
        context.position = 0
        return {
            'action': 'CLOSE',
            'price': current_price,
            'reason': f'统计套利平仓 (Z-Score:{zscore:.4f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("统计套利策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "统计套利策略",
            "arbitrage",
            "statistical_arbitrage",
            "基于统计套利的策略，利用两个相关资产的价差进行交易",
            statistical_arbitrage_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "lookback_period",
                        "type": "integer",
                        "default": 20,
                        "min": 10,
                        "max": 50,
                        "description": "回看周期",
                        "required": True
                    },
                    {
                        "name": "entry_threshold",
                        "type": "float",
                        "default": 2.0,
                        "min": 1.0,
                        "max": 3.0,
                        "description": "入场阈值",
                        "required": True
                    },
                    {
                        "name": "exit_threshold",
                        "type": "float",
                        "default": 0.5,
                        "min": 0.1,
                        "max": 1.0,
                        "description": "出场阈值",
                        "required": True
                    },
                    {
                        "name": "symbol_a",
                        "type": "string",
                        "default": "BTCUSDT",
                        "description": "交易对A",
                        "required": True
                    },
                    {
                        "name": "symbol_b",
                        "type": "string",
                        "default": "ETHUSDT",
                        "description": "交易对B",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "lookback_period": 20,
                "entry_threshold": 2.0,
                "exit_threshold": 0.5,
                "symbol_a": "BTCUSDT",
                "symbol_b": "ETHUSDT"
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建统计套利策略模板")
    else:
        logger.info("统计套利策略模板已存在")

    # 三角套利策略模板 (套利策略类型)
    triangular_arbitrage_code = """
def initialize(context):
    context.min_profit_threshold = {{ min_profit_threshold | default(0.001) }}
    context.symbol_a = {{ symbol_a | default('BTCUSDT') }}
    context.symbol_b = {{ symbol_b | default('ETHUSDT') }}
    context.symbol_c = {{ symbol_c | default('ETHBTC') }}
    context.position = 0

def calculate_triangular_arbitrage(price_a, price_b, price_c):
    # A/USDT * B/USDT * C/B 应该等于 1
    # 如果不等于1，存在套利机会
    implied_rate = price_a * price_b * price_c
    return abs(implied_rate - 1.0)

def handle_bar(context, data):
    # 假设数据包含三个交易对的价格
    price_a = data.get('symbol_a', data['close']).iloc[-1]
    price_b = data.get('symbol_b', data['close']).iloc[-1]
    price_c = data.get('symbol_c', data['close']).iloc[-1]

    arbitrage_opportunity = calculate_triangular_arbitrage(price_a, price_b, price_c)

    # 存在套利机会
    if arbitrage_opportunity > context.min_profit_threshold and context.position <= 0:
        context.position = 1
        return {
            'action': 'BUY',
            'price': price_a,
            'reason': f'三角套利机会 (偏差:{arbitrage_opportunity:.6f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("三角套利策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "三角套利策略",
            "arbitrage",
            "triangular_arbitrage",
            "基于三角套利的策略，利用三个交易对之间的价格差异进行套利",
            triangular_arbitrage_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "min_profit_threshold",
                        "type": "float",
                        "default": 0.001,
                        "min": 0.0001,
                        "max": 0.01,
                        "description": "最小利润阈值",
                        "required": True
                    },
                    {
                        "name": "symbol_a",
                        "type": "string",
                        "default": "BTCUSDT",
                        "description": "交易对A",
                        "required": True
                    },
                    {
                        "name": "symbol_b",
                        "type": "string",
                        "default": "ETHUSDT",
                        "description": "交易对B",
                        "required": True
                    },
                    {
                        "name": "symbol_c",
                        "type": "string",
                        "default": "ETHBTC",
                        "description": "交易对C",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "min_profit_threshold": 0.001,
                "symbol_a": "BTCUSDT",
                "symbol_b": "ETHUSDT",
                "symbol_c": "ETHBTC"
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建三角套利策略模板")
    else:
        logger.info("三角套利策略模板已存在")

    # 固定网格策略模板 (网格策略类型)
    fixed_grid_code = """
def initialize(context):
    context.grid_size = {{ grid_size | default(0.02) }}
    context.grid_levels = {{ grid_levels | default(10) }}
    context.base_price = {{ base_price | default(50000) }}
    context.position = 0
    context.grid_positions = {}

def handle_bar(context, data):
    current_price = data['close'].iloc[-1]

    # 计算当前价格所在的网格级别
    price_diff = (current_price - context.base_price) / context.base_price
    grid_level = int(price_diff / context.grid_size)

    # 价格下跌到下一个网格买入
    if grid_level < 0 and grid_level not in context.grid_positions:
        context.grid_positions[grid_level] = current_price
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'网格买入 (级别:{grid_level} 价格:{current_price:.2f})'
        }

    # 价格上涨到上一个网格卖出
    elif grid_level > 0 and (grid_level - 1) in context.grid_positions:
        del context.grid_positions[grid_level - 1]
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'网格卖出 (级别:{grid_level} 价格:{current_price:.2f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("固定网格策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "固定网格策略",
            "grid",
            "fixed_grid",
            "基于固定网格的交易策略，在预设的价格网格上进行买卖",
            fixed_grid_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "grid_size",
                        "type": "float",
                        "default": 0.02,
                        "min": 0.01,
                        "max": 0.1,
                        "description": "网格间距(百分比)",
                        "required": True
                    },
                    {
                        "name": "grid_levels",
                        "type": "integer",
                        "default": 10,
                        "min": 5,
                        "max": 50,
                        "description": "网格级别数",
                        "required": True
                    },
                    {
                        "name": "base_price",
                        "type": "float",
                        "default": 50000,
                        "min": 1,
                        "max": 1000000,
                        "description": "基准价格",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "grid_size": 0.02,
                "grid_levels": 10,
                "base_price": 50000
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建固定网格策略模板")
    else:
        logger.info("固定网格策略模板已存在")

    logger.info("扩展策略模板创建完成")

def main():
    """主函数"""
    logger.info("开始初始化扩展策略模板...")

    # 获取数据库路径
    db_paths = [
        'app.db',
        'backend/app.db',
        'app/database.db',
        'app/db/database.db'
    ]

    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        db_path = 'app.db'  # 使用默认路径

    logger.info(f"使用数据库: {db_path}")

    # 连接数据库
    conn = sqlite3.connect(db_path)

    try:
        # 创建扩展模板
        create_extended_templates(conn)

        # 提交更改
        conn.commit()
        logger.info("扩展策略模板初始化完成")

    except Exception as e:
        conn.rollback()
        logger.error(f"初始化扩展策略模板失败: {str(e)}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    main()
