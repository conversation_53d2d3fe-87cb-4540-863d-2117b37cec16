#!/usr/bin/env python3
"""
调试缓存图表问题
"""

import requests
import json

def debug_cache_chart():
    """调试缓存图表问题"""
    
    print("🔍 调试缓存图表问题...")
    print("=" * 60)
    
    # 获取认证token
    print("\n1. 🔐 获取认证令牌...")
    try:
        auth_response = requests.post('http://localhost:8000/api/v1/auth/token', 
                                     json={'username': 'admin', 'password': 'admin123'})
        
        if auth_response.status_code != 200:
            print(f"❌ 认证失败: {auth_response.status_code}")
            return
        
        token = auth_response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print(f"✅ 认证成功")
        
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return
    
    # 测试缓存统计API
    print("\n2. 📊 测试缓存统计API...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误响应: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 测试缓存历史API
    print("\n3. 📈 测试缓存历史API...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/metrics/cache?limit=10', headers=headers)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('success') and data.get('data'):
                history_data = data['data']
                print(f"\n   📊 历史数据分析:")
                print(f"      记录数量: {len(history_data)}")
                
                if history_data:
                    latest = history_data[-1]
                    print(f"      最新记录:")
                    print(f"         时间戳: {latest.get('timestamp')}")
                    print(f"         命中率: {latest.get('hit_rate')}")
                    print(f"         缓存大小: {latest.get('size')}")
                    print(f"         命中次数: {latest.get('hits')}")
                    print(f"         未命中次数: {latest.get('misses')}")
                else:
                    print(f"      ⚠️ 历史数据为空")
            else:
                print(f"   ❌ API返回失败或数据为空")
        else:
            print(f"   错误响应: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 测试前端API路径
    print("\n4. 🌐 测试前端可能使用的API路径...")
    
    # 测试可能的API路径
    api_paths = [
        '/api/v1/performance/metrics/cache',
        '/api/v1/performance/metrics/cache?limit=50',
        '/performance/metrics/cache',
        '/performance/metrics/cache?limit=50'
    ]
    
    for path in api_paths:
        try:
            url = f'http://localhost:8000{path}'
            response = requests.get(url, headers=headers)
            print(f"   {path}: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    print(f"      ✅ 有数据: {len(data['data'])} 条记录")
                else:
                    print(f"      ⚠️ 无数据或失败: {data.get('message', '未知')}")
            elif response.status_code == 404:
                print(f"      ❌ 路径不存在")
            else:
                print(f"      ❌ 错误: {response.status_code}")
        except Exception as e:
            print(f"      ❌ 异常: {e}")
    
    # 生成一些缓存活动
    print("\n5. 🔄 生成缓存活动...")
    for i in range(3):
        print(f"   第{i+1}次调用...")
        try:
            # 调用性能摘要API
            response = requests.get('http://localhost:8000/api/v1/performance/summary', headers=headers)
            print(f"      性能摘要: {response.status_code}")
            
            # 调用性能分析API
            response = requests.get('http://localhost:8000/api/v1/performance/analysis', headers=headers)
            print(f"      性能分析: {response.status_code}")
            
            # 调用缓存统计API
            response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
            print(f"      缓存统计: {response.status_code}")
            
        except Exception as e:
            print(f"      ❌ 异常: {e}")
    
    # 再次检查缓存历史
    print("\n6. 🔍 再次检查缓存历史...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/metrics/cache?limit=10', headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                history_data = data['data']
                print(f"   ✅ 历史数据: {len(history_data)} 条记录")
                
                if history_data:
                    print(f"   最新3条记录:")
                    for i, record in enumerate(history_data[-3:]):
                        print(f"      记录{i+1}: {record.get('timestamp', 'N/A')[:19]} - 命中率:{record.get('hit_rate', 0):.2%} - 大小:{record.get('size', 0)}")
                else:
                    print(f"   ⚠️ 历史数据仍为空")
            else:
                print(f"   ❌ API返回失败: {data.get('message', '未知错误')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print("\n" + "=" * 60)
    print("🔍 缓存图表调试完成")

if __name__ == "__main__":
    debug_cache_chart()
