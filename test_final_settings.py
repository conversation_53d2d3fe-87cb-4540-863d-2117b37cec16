#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_final_settings():
    """最终的系统设置功能综合测试"""
    
    base_url = "http://localhost:8000/api/v1/unified-config"
    
    print("🚀 系统设置功能最终综合测试")
    print("=" * 60)
    
    test_results = {
        "system_config": False,
        "api_keys": False,
        "system_params": False,
        "cache_management": False,
        "backup_functionality": False,
        "data_maintenance": False
    }
    
    # 1. 测试系统配置
    print("\n1. 🔧 测试系统配置...")
    try:
        # 获取配置
        response = requests.get(f"{base_url}/system")
        assert response.status_code == 200, f"获取失败: {response.status_code}"
        
        # 更新配置
        test_config = {
            "trading": {
                "enable_live_trading": True,
                "risk_level": "high",
                "max_position_size": 0.5
            }
        }
        response = requests.put(f"{base_url}/system", json=test_config)
        assert response.status_code == 200, f"更新失败: {response.status_code}"
        
        # 验证更新
        response = requests.get(f"{base_url}/system")
        result = response.json()
        assert result['data']['trading']['enable_live_trading'] == True
        
        test_results["system_config"] = True
        print("   ✅ 系统配置测试通过")
    except Exception as e:
        print(f"   ❌ 系统配置测试失败: {e}")
    
    # 2. 测试API密钥
    print("\n2. 🔑 测试API密钥...")
    try:
        # 获取API密钥
        response = requests.get(f"{base_url}/api-keys")
        assert response.status_code == 200, f"获取失败: {response.status_code}"
        
        # 更新API密钥
        test_keys = [
            {
                "id": 1,
                "name": "Binance API",
                "api_key": "updated_key_456",
                "secret_key": "updated_secret_789",
                "enabled": True,
                "permissions": ["read", "trade"]
            }
        ]
        response = requests.put(f"{base_url}/api-keys", json=test_keys)
        assert response.status_code == 200, f"更新失败: {response.status_code}"
        
        test_results["api_keys"] = True
        print("   ✅ API密钥测试通过")
    except Exception as e:
        print(f"   ❌ API密钥测试失败: {e}")
    
    # 3. 测试系统参数
    print("\n3. ⚙️ 测试系统参数...")
    try:
        # 获取系统参数
        response = requests.get(f"{base_url}/system-params")
        assert response.status_code == 200, f"获取失败: {response.status_code}"
        
        # 更新系统参数
        test_params = {
            "performance": {
                "maxThreads": 12,
                "cacheSize": 1024
            }
        }
        response = requests.post(f"{base_url}/system-params", json=test_params)
        assert response.status_code == 200, f"更新失败: {response.status_code}"
        
        test_results["system_params"] = True
        print("   ✅ 系统参数测试通过")
    except Exception as e:
        print(f"   ❌ 系统参数测试失败: {e}")
    
    # 4. 测试缓存管理
    print("\n4. 🗂️ 测试缓存管理...")
    try:
        # 获取缓存大小
        response = requests.get(f"{base_url}/cache-size")
        assert response.status_code == 200, f"获取缓存大小失败: {response.status_code}"
        result = response.json()
        assert 'total_size_mb' in result['data']
        
        # 清理缓存
        response = requests.post(f"{base_url}/clear-cache")
        assert response.status_code == 200, f"清理缓存失败: {response.status_code}"
        
        test_results["cache_management"] = True
        print("   ✅ 缓存管理测试通过")
    except Exception as e:
        print(f"   ❌ 缓存管理测试失败: {e}")
    
    # 5. 测试备份功能
    print("\n5. 💾 测试备份功能...")
    try:
        # 获取备份列表
        response = requests.get(f"{base_url}/backups")
        assert response.status_code == 200, f"获取备份列表失败: {response.status_code}"
        
        # 创建备份
        backup_data = {"type": "database"}
        response = requests.post(f"{base_url}/backups", json=backup_data)
        assert response.status_code == 200, f"创建备份失败: {response.status_code}"
        result = response.json()
        assert 'backup_id' in result['data']
        
        test_results["backup_functionality"] = True
        print("   ✅ 备份功能测试通过")
    except Exception as e:
        print(f"   ❌ 备份功能测试失败: {e}")
    
    # 6. 测试数据维护
    print("\n6. 🔧 测试数据维护...")
    try:
        # 获取数据维护配置
        response = requests.get(f"{base_url}/data-maintenance")
        assert response.status_code == 200, f"获取数据维护配置失败: {response.status_code}"
        
        test_results["data_maintenance"] = True
        print("   ✅ 数据维护测试通过")
    except Exception as e:
        print(f"   ❌ 数据维护测试失败: {e}")
    
    # 测试结果汇总
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有系统设置功能测试通过！系统设置模块已完全修复！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = test_final_settings()
    exit(0 if success else 1)
