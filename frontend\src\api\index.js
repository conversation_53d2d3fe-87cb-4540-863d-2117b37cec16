import axios from 'axios';
import apiCache from '@/utils/apiCache';
import service from '@/utils/request'; // 导入封装好的请求工具
import config from './config';
import notificationApi from './notification'; // 导入通知API

// 设置axios默认配置
axios.defaults.baseURL = process.env.VUE_APP_BASE_API || '';
axios.defaults.timeout = 15000;
axios.defaults.withCredentials = false;  // 修改为false，解决CORS问题

// 创建单独的axios实例用于认证相关请求
const authAxios = axios.create({
  baseURL: process.env.VUE_APP_AUTH_API_URL || 'http://localhost:8000/api/v1',
  timeout: 15000,
  withCredentials: true,
});

// 配置服务API基础URLs
const API_URLS = {
  main: process.env.VUE_APP_MAIN_API_URL || 'http://localhost:8000/api/v1',
  market: process.env.VUE_APP_MARKET_API_URL || 'http://localhost:8005/api/v1',
  signal: process.env.VUE_APP_SIGNAL_API_URL || 'http://localhost:8004/api/v1',
  risk: process.env.VUE_APP_RISK_API_URL || 'http://localhost:8002/api/v1',
  backtest: process.env.VUE_APP_BACKTEST_API_URL || 'http://localhost:8001/api/v1',
  trading: process.env.VUE_APP_TRADING_API_URL || 'http://localhost:8003/api/v1',
  auth: process.env.VUE_APP_AUTH_API_URL || 'http://localhost:8000/api/v1'
};

// 获取对应服务的API基础URL
const getApiUrl = (service) => {
  // 返回对应服务的API基础URL
  // 注意：在调用时如果需要移除/api/v1前缀，请使用.replace('/api/v1', '')
  return API_URLS[service] || API_URLS.auth;
};

// 使用request.js中的service作为axiosInstance
const axiosInstance = service;

// 用户认证相关API
export const authAPI = {
  // 用户登录，获取token
  login: async (username, password) => {
    try {
      console.log('开始登录请求，用户名:', username);

      // 尝试使用多种登录方式
      let response;
      let error;

      // 方式1: 使用FormData提交到auth/token
      try {
        const formData = new FormData();
        formData.append('username', username);
        formData.append('password', password);

        const requestUrl = `${authAxios.defaults.baseURL}/auth/token`;
        console.log('尝试登录方式1，发送请求到:', requestUrl);

        response = await authAxios.post('auth/token', formData);
        console.log('登录方式1成功，响应:', response);
      } catch (err) {
        console.log('登录方式1失败，尝试方式2');
        error = err;

        // 方式2: 使用JSON提交到auth/token
        try {
          const requestUrl = `${authAxios.defaults.baseURL}/auth/token`;
          console.log('尝试登录方式2，发送请求到:', requestUrl);

          response = await authAxios.post('auth/token', {
            username: username,
            password: password
          });
          console.log('登录方式2成功，响应:', response);
        } catch (err2) {
          console.log('登录方式2失败，尝试方式3');
          error = err2;

          // 方式3: 使用模拟登录（用于开发测试）
          if (username === 'admin' && password === 'admin123') {
            console.log('使用模拟登录方式');
            response = {
              data: {
                access_token: 'dummy_token_for_development',
                token_type: 'bearer'
              }
            };
            console.log('模拟登录成功');
          } else {
            throw error; // 如果模拟登录也失败，抛出原始错误
          }
        }
      }

      // 确保响应包含token
      if (response.data && response.data.access_token) {
        // 确保token格式正确（以Bearer开头）
        const token = response.data.access_token.startsWith('Bearer ')
          ? response.data.access_token
          : `Bearer ${response.data.access_token}`;

        // 存储token到localStorage
        localStorage.setItem('token', token);

        // 设置默认请求头认证信息
        axios.defaults.headers.common['Authorization'] = token;
        authAxios.defaults.headers.common['Authorization'] = token;

        console.log('登录成功，token已保存');
        return response.data;
      } else {
        console.error('登录响应格式错误：', response);
        throw new Error('登录响应格式错误');
      }
    } catch (error) {
      console.error('登录失败：', error);
      if (error.response) {
        console.error('错误状态码:', error.response.status);
        console.error('错误响应数据:', error.response.data);
      }
      throw error;
    }
  },

  // 退出登录，清除token
  logout: () => {
    // 从localStorage移除token
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');

    // 清除请求头中的认证信息
    delete axios.defaults.headers.common['Authorization'];
    delete authAxios.defaults.headers.common['Authorization'];

    console.log('已退出登录，token已清除');
  },

  // 获取当前用户信息
  getCurrentUser: async () => {
    try {
      // 使用包含认证信息的请求实例
      const response = await service.get('users/me');
      return response;
    } catch (error) {
      console.error('获取用户信息失败：', error);
      throw error;
    }
  }
};

// 策略管理相关API
export const strategyAPI = {
  // 获取所有策略
  getStrategies(params = {}) {
    // 使用缓存包装API请求，缓存30秒
    return apiCache.withCache(
      async (searchParams) => {
        // 构建查询参数
        const queryParams = new URLSearchParams();
        if (searchParams.keyword) queryParams.append('keyword', searchParams.keyword);
        if (searchParams.status) queryParams.append('status', searchParams.status);

        const queryString = queryParams.toString();

        // 直接使用正确的API路径(/api/v1/strategies)
        const correctUrl = queryString ?
          `${getApiUrl('main')}/strategies?${queryString}` :
          `${getApiUrl('main')}/strategies`;

        const response = await axios.get(correctUrl);
        return response;
      },
      { expireTime: 30000 }
    )(params);
  },

  // 获取策略统计数据
  getStats() {
    // 使用缓存包装API请求，缓存30秒
    return apiCache.withCache(
      async () => {
        // 优先使用已修复的API路径 /strategies/stats
        try {
          const response = await axios.get(`${getApiUrl('main')}/strategies/stats`);
          return response;
        } catch (error) {
          // 如果主路径失败，尝试使用备用路径
          console.info('尝试使用备用API路径获取策略统计数据');

          // 记录详细错误日志以便调试
          console.log('原始API错误:', error.message, error.response?.status);

          try {
            // 尝试使用旧的strategy路径（有SQLAlchemy冲突问题）
            const response = await axios.get(`${getApiUrl('main')}/strategy/stats`);
            return response;
          } catch (secondError) {
            // 所有路径均失败，则返回原始错误
            console.error('所有API路径均失败:', secondError.message);
            throw error;
          }
        }
      },
      { expireTime: 30000 }
    )();
  },

  // 获取单个策略详情
  getStrategy(id) {
    // 使用缓存包装API请求，缓存2分钟
    return apiCache.withCache(
      async (id) => {
        // 先尝试使用新API路径
        try {
          const response = await axios.get(`${getApiUrl('main')}/strategy/${id}`);
          return response;
        } catch (error) {
          // 如果新路径失败，尝试使用旧路径
          console.info('尝试使用旧API路径获取策略详情');
          const response = await axios.get(`${getApiUrl('main')}/strategies/${id}`);
          return response;
        }
      },
      { expireTime: 120000 }
    )(id);
  },

  // 创建新策略 - 不缓存修改操作
  createStrategy(strategyData) {
    // 创建后清除策略列表缓存
    const createStrategy = async () => {
      try {
        // 先尝试使用新API路径
        return await axios.post(`${getApiUrl('main')}/strategy`, strategyData);
      } catch (error) {
        // 如果新路径失败，尝试使用旧路径
        console.info('尝试使用旧API路径创建策略');
        return await axios.post(`${getApiUrl('main')}/strategies`, strategyData);
      }
    };

    const result = createStrategy();
    apiCache.clearCache(`${getApiUrl('main')}/strategy`);
    apiCache.clearCache(`${getApiUrl('main')}/strategies`);
    return result;
  },

  // 更新策略 - 不缓存修改操作
  updateStrategy(id, strategyData) {
    // 更新后清除相关缓存
    const updateStrategy = async () => {
      console.log('更新策略:', id, strategyData);
      try {
        // 先尝试使用新API路径
        console.log(`请求更新策略: ${getApiUrl('main')}/strategy/${id}`);
        const response = await axios.put(`${getApiUrl('main')}/strategy/${id}`, strategyData);
        console.log('更新策略成功:', response.data);
        return response;
      } catch (error) {
        // 如果新路径失败，尝试使用旧路径
        console.info('尝试使用旧API路径更新策略');
        console.log(`尝试备用路径: ${getApiUrl('main')}/strategies/${id}`);

        if (error.response) {
          console.error('更新策略失败, 状态码:', error.response.status);
          console.error('错误详情:', error.response.data);
        } else {
          console.error('更新策略失败:', error.message);
        }

        const response = await axios.put(`${getApiUrl('main')}/strategies/${id}`, strategyData);
        console.log('使用备用路径更新策略成功:', response.data);
        return response;
      }
    };

    // 返回Promise以便调用者可以等待结果
    return updateStrategy().then(result => {
      // 更新成功后清除缓存
      apiCache.clearCache(`${getApiUrl('main')}/strategy`);
      apiCache.clearCache(`${getApiUrl('main')}/strategies`);
      apiCache.clearCache(`${getApiUrl('main')}/strategy/${id}`);
      apiCache.clearCache(`${getApiUrl('main')}/strategies/${id}`);
      return result;
    });
  },

  // 删除策略 - 不缓存修改操作
  deleteStrategy(id) {
    // 删除后清除相关缓存
    const deleteStrategy = async () => {
      try {
        // 先尝试使用新API路径
        return await axios.delete(`${getApiUrl('main')}/strategy/${id}`);
      } catch (error) {
        // 如果新路径失败，尝试使用旧路径
        console.info('尝试使用旧API路径删除策略');
        return await axios.delete(`${getApiUrl('main')}/strategies/${id}`);
      }
    };

    const result = deleteStrategy();
    apiCache.clearCache(`${getApiUrl('main')}/strategy`);
    apiCache.clearCache(`${getApiUrl('main')}/strategies`);
    apiCache.clearCache(`${getApiUrl('main')}/strategy/${id}`);
    apiCache.clearCache(`${getApiUrl('main')}/strategies/${id}`);
    return result;
  },

  // 验证策略
  validateStrategy(id) {
    const validateStrategy = async () => {
      try {
        // 先尝试使用新API路径
        return await axios.post(`${getApiUrl('main')}/strategy/${id}/validate`);
      } catch (error) {
        // 如果新路径失败，尝试使用旧路径
        console.info('尝试使用旧API路径验证策略');
        return await axios.post(`${getApiUrl('main')}/strategies/${id}/validate`);
      }
    };

    return validateStrategy();
  }
};

// 回测相关API
export const backtestAPI = {
  // 获取所有回测
  getBacktests() {
    // 使用缓存包装API请求，缓存30秒
    return apiCache.withCache(
      async () => await axios.get(`${getApiUrl('backtest')}/backtest`),
      { expireTime: 30000 }
    )();
  },

  // 获取回测统计数据
  getStats() {
    // 使用缓存包装API请求，缓存30秒
    return apiCache.withCache(
      async () => {
        // 先尝试使用主路径
        try {
          const response = await axios.get(`${getApiUrl('backtest')}/backtest/stats`);
          return response;
        } catch (error) {
          // 如果主路径失败，尝试使用备用路径
          console.info('尝试使用备用API路径获取回测统计数据');

          // 记录详细错误日志以便调试
          console.log('原始API错误:', error.message, error.response?.status);

          try {
            // 尝试使用复数形式路径
            const response = await axios.get(`${getApiUrl('backtest')}/backtests/stats`);
            return response;
          } catch (secondError) {
            // 如果备用路径也失败，则打印错误并返回空数据
            console.error('所有API路径均失败:', secondError.message);

            // 返回默认数据以避免UI崩溃
            return {
              data: {
                total: 0,
                completed: 0,
                running: 0,
                avg_return: 0,
                recent_results: []
              }
            };
          }
        }
      },
      { expireTime: 30000 }
    )();
  },

  // 获取最近回测
  getRecent() {
    // 使用缓存包装API请求，缓存30秒
    return apiCache.withCache(
      async () => {
        // 先尝试使用主路径
        try {
          const response = await axios.get(`${getApiUrl('backtest')}/backtest/recent`);
          return response;
        } catch (error) {
          // 如果主路径失败，尝试使用备用路径
          console.info('尝试使用备用API路径获取最近回测');

          try {
            // 尝试使用复数形式路径
            const response = await axios.get(`${getApiUrl('backtest')}/backtests/recent`);
            return response;
          } catch (secondError) {
            // 另一种可能的API路径
            try {
              const response = await axios.get(`${getApiUrl('backtest')}/backtest?limit=5`);
              return response;
            } catch (thirdError) {
              // 如果所有备用路径都失败，返回空数据
              console.error('所有API路径均失败，返回默认数据');
              return {
                data: []
              };
            }
          }
        }
      },
      { expireTime: 30000 }
    )();
  },

  // 获取单个回测详情
  getBacktest(id) {
    // 使用缓存包装API请求，缓存2分钟
    return apiCache.withCache(
      async (id) => await axios.get(`${getApiUrl('backtest')}/backtest/${id}`),
      { expireTime: 120000 }
    )(id);
  },

  // 创建新回测 - 不缓存修改操作
  createBacktest(backtestData) {
    // 创建后清除回测列表缓存
    const result = axios.post(`${getApiUrl('backtest')}/backtest`, backtestData);
    apiCache.clearCache(`${getApiUrl('backtest')}/backtest`);
    return result;
  },

  // 删除回测 - 不缓存修改操作
  deleteBacktest(id) {
    // 删除后清除相关缓存
    const result = axios.delete(`${getApiUrl('backtest')}/backtest/${id}`);
    apiCache.clearCache(`${getApiUrl('backtest')}/backtest`);
    apiCache.clearCache(`${getApiUrl('backtest')}/backtest/${id}`);
    return result;
  }
};

// 系统配置相关API
export const configAPI = {
  // 获取系统配置
  getSystemConfig: async () => {
    try {
      return await service.get('/api/v1/config/system');
    } catch (error) {
      console.error('获取系统配置失败：', error);
      throw error;
    }
  },

  // 更新系统配置
  updateSystemConfig: async (data) => {
    try {
      return await service.put('/api/v1/config/system', data);
    } catch (error) {
      console.error('更新系统配置失败：', error);
      throw error;
    }
  },

  // 获取所有API密钥配置
  getApiKeys: async () => {
    try {
      return await service.get('/api/v1/config/api-keys');
    } catch (error) {
      console.error('获取API密钥配置失败：', error);
      throw error;
    }
  },

  // 保存API密钥配置
  saveApiKeys: async (data) => {
    try {
      return await service.put('/api/v1/config/api-keys', data);
    } catch (error) {
      console.error('保存API密钥配置失败：', error);
      throw error;
    }
  },

  // 测试API连接
  testApiConnection: async (data) => {
    try {
      return await service.post('/api/v1/config/test-api-connection', data);
    } catch (error) {
      console.error('测试API连接失败：', error);
      throw error;
    }
  },

  // 获取系统参数
  getSystemParams: async () => {
    try {
      return await service.get('/api/v1/config/system-params');
    } catch (error) {
      console.error('获取系统参数失败：', error);
      throw error;
    }
  },

  // 保存系统参数
  saveSystemParams: async (data) => {
    try {
      return await service.post('/api/v1/config/system-params', data);
    } catch (error) {
      console.error('保存系统参数失败：', error);
      throw error;
    }
  },

  // 获取数据维护配置
  getDataMaintenanceConfig: async () => {
    try {
      return await service.get('/api/v1/config/data-maintenance');
    } catch (error) {
      console.error('获取数据维护配置失败：', error);
      throw error;
    }
  },

  // 保存数据维护配置
  saveDataMaintenanceConfig: async (data) => {
    try {
      return await service.put('/api/v1/config/data-maintenance', data);
    } catch (error) {
      console.error('保存数据维护配置失败：', error);
      throw error;
    }
  },

  // 获取备份文件列表
  getBackupFiles: async () => {
    try {
      return await service.get('/api/v1/config/backups');
    } catch (error) {
      console.error('获取备份文件列表失败：', error);
      throw error;
    }
  },

  // 创建备份
  createBackup: async (data) => {
    try {
      return await service.post('/api/v1/config/backups', data);
    } catch (error) {
      console.error('创建备份失败：', error);
      throw error;
    }
  },

  // 恢复备份
  restoreBackup: async (data) => {
    try {
      return await service.post('/api/v1/config/restore-backup', data);
    } catch (error) {
      console.error('恢复备份失败：', error);
      throw error;
    }
  },

  // 清理数据
  cleanupData: async (data) => {
    try {
      return await service.post('/api/v1/config/cleanup-data', data);
    } catch (error) {
      console.error('清理数据失败：', error);
      throw error;
    }
  },

  // 获取缓存大小
  getCacheSize: async () => {
    try {
      return await service.get('/api/v1/config/cache-size');
    } catch (error) {
      console.error('获取缓存大小失败：', error);
      throw error;
    }
  },

  // 清理缓存
  clearCache: async () => {
    try {
      return await service.post('/api/v1/config/clear-cache');
    } catch (error) {
      console.error('清理缓存失败：', error);
      throw error;
    }
  }
};

// 用户设置相关API
export const userSettingsAPI = {
  // 获取用户设置
  getUserSettings: async () => {
    try {
      return await service.get('/api/v1/user/settings');
    } catch (error) {
      console.error('获取用户设置失败：', error);
      throw error;
    }
  },

  // 创建用户设置
  createUserSettings(settingsData) {
    return service.post('/api/v1/user/settings', settingsData);
  },

  // 更新用户设置
  updateUserSettings: async (settings) => {
    try {
      return await service.put('/api/v1/user/settings', settings);
    } catch (error) {
      console.error('更新用户设置失败：', error);
      throw error;
    }
  },

  // 删除用户设置
  deleteUserSettings() {
    return service.delete('/api/v1/user/settings');
  }
};

// 数据管理相关API
export const dataAPI = {
  // 获取所有数据源
  getDataSources(params = {}) {
    return axios.get(`${getApiUrl('market')}/data/sources`, { params });
  },

  // 获取所有可用交易对
  getSymbols() {
    return axios.get(`${getApiUrl('market')}/data/symbols`);
  },

  // 获取所有可用时间周期
  getTimeframes() {
    return axios.get(`${getApiUrl('market')}/data/timeframes`);
  },

  // 获取数据统计信息
  getStats() {
    // 使用市场数据API获取统计信息
    return axios.get(`${getApiUrl('market')}/data/stats`)
      .catch(error => {
        console.warn('获取数据统计信息失败，使用默认值:', error);
        // 返回默认数据
        return {
          data: {
            symbols: 25,
            days: 365,
            lastUpdate: new Date().toISOString().split('T')[0]
          }
        };
      });
  },

  // 更新或创建数据源
  updateData(data) {
    console.log('Sending data to server:', JSON.stringify(data));

    // 确保必要的字段
    if (!data.name || !data.source_type || !data.symbol || !data.timeframe) {
      console.error('Missing required fields', data);
      return Promise.reject(new Error('缺少必填字段'));
    }

    // 如果有ID，执行更新操作，否则执行创建操作
    if (data.id) {
      console.log(`更新数据源 ID: ${data.id}, 请求URL: ${getApiUrl('market')}/data/sources/${data.id}`);
      return axios.put(`${getApiUrl('market')}/data/sources/${data.id}`, data)
        .then(response => {
          console.log('更新数据源响应:', JSON.stringify(response.data));
          return response;
        })
        .catch(error => {
          console.error('更新数据源失败:', error);
          console.error('错误响应:', error.response?.data);
          throw error;
        });
    } else {
      console.log(`创建数据源, 请求URL: ${getApiUrl('market')}/data/sources`);
      return axios.post(`${getApiUrl('market')}/data/sources`, data)
        .then(response => {
          console.log('创建数据源响应:', JSON.stringify(response.data));
          return response;
        })
        .catch(error => {
          console.error('创建数据源失败:', error);
          console.error('错误响应:', error.response?.data);
          throw error;
        });
    }
  },

  // 删除数据源
  deleteData(id) {
    // 使用标准路径
    const path = `${getApiUrl('market')}/data/sources/${id}`;
    console.log(`删除数据源请求路径: ${path}`);
    return axios.delete(path)
      .then(response => {
        console.log('删除数据源成功:', response.data);
        return response;
      })
      .catch(error => {
        console.error('删除数据源失败:', error);
        if (error.response) {
          console.error('错误状态码:', error.response.status);
          console.error('错误详情:', error.response.data);
        }
        throw error;
      });
  },

  // 同步数据源
  syncData(id) {
    // 清除相关缓存，并返回API调用的Promise
    const sync = async () => {
      // 修正 API 路径，添加 /data
      return await axios.post(`${getApiUrl('market')}/data/sources/${id}/sync`);
    }
    // 清除相关缓存
    apiCache.clearCache(`${getApiUrl('market')}/data/sources/${id}`);
    apiCache.clearCache(`${getApiUrl('market')}/sync-tasks`);
    // 调用并返回Promise
    return sync();
  },

  // 一键同步历史数据
  quickSyncData(sourceId, params) {
    return axios.post(`${getApiUrl('market')}/data/sources/${sourceId}/quick-sync`, {
      start_date: params.start_date,
      timeframes: params.timeframes
    });
  },

  // 获取OHLCV数据
  getOHLCV(params) {
    return axios.get(`${getApiUrl('market')}/data/ohlcv`, { params });
  },

  // 获取时间级别配置
  getTimeframesConfig(sourceId) {
    return axios.get(`${getApiUrl('market')}/data/sources/${sourceId}/timeframes`);
  },

  // 更新时间级别配置
  updateTimeframesConfig(sourceId, timeframes) {
    return axios.put(`${getApiUrl('market')}/data/sources/${sourceId}/timeframes`, timeframes);
  },

  // 执行时间级别批量操作
  batchTimeframeAction(sourceId, actionData) {
    // 所有操作类型都使用相同的batch-action端点
    return axios.post(`${getApiUrl('market')}/data/sources/${sourceId}/timeframes/batch-action`, actionData);
  },

  // 获取同步任务列表
  getSyncTasks(params) {
    // 先检查是否需要设置autostart参数（用于轮询时自动启动pending任务）
    const autoStartParams = { ...params };
    if (!autoStartParams.hasOwnProperty('autostart')) {
      autoStartParams.autostart = true; // 默认启用自动启动
    }

    // 调用 market 服务的 /data/sync-tasks
    return axios.get(`${getApiUrl('market')}/data/sync-tasks`, { params: autoStartParams })
      .then(response => {
        // 如果是 [SyncTaskResponse] 格式的标准响应，直接返回
        if (Array.isArray(response.data)) {
          console.log('API返回数组格式数据，直接使用');
          return { data: response.data };
        }
        // 如果是 { data: [SyncTaskResponse] } 格式的嵌套响应
        else if (response.data && Array.isArray(response.data.data)) {
          console.log('API返回嵌套数据结构，提取data字段');
          return response;
        }
        // 如果是 { success: true, data: [SyncTaskResponse] } 格式的嵌套响应
        else if (response.data && response.data.success && Array.isArray(response.data.data)) {
          console.log('API返回success格式数据，提取data字段');
          return response;
        }
        // 未知的响应格式，保持原样返回以便前端处理
        else {
          console.warn('API返回未知格式数据:', response.data);
          return response;
        }
      });
  },

  // 获取同步任务详情
  getSyncTask(taskId) {
    // 添加 /data/ 段
    return axios.get(`${getApiUrl('market')}/data/sync-tasks/${taskId}`);
  },

  // 取消同步任务
  cancelSyncTask(id) {
    // 修正API路径，添加 /data/ 段
    return axios.post(`${getApiUrl('market')}/data/sync-tasks/${id}/cancel`)
      .then(response => {
        console.log('原始取消任务响应:', response.data);

        // 标准化不同格式的响应
        if (response.data && typeof response.data === 'object') {
          // 如果响应是一个对象，确保它有一个success字段
          if (!response.data.hasOwnProperty('success')) {
            // 如果没有success字段但有status字段，并且status是cancelled
            if (response.data.status === 'cancelled') {
              return {
                data: {
                  success: true,
                  task: response.data
                }
              };
            }
            // 如果有id字段，认为是任务对象本身
            else if (response.data.id) {
              return {
                data: {
                  success: true,
                  task: response.data
                }
              };
            }
          }
        }

        // 保持原始响应格式
        return response;
      });
  },

  // 删除同步任务
  deleteSyncTask(id) {
    return axios.delete(`${getApiUrl('market')}/data/sync-tasks/${id}`)
      .then(response => {
        console.log('删除任务响应:', response.data);

        // 标准化不同格式的响应
        if (response.data && typeof response.data === 'object') {
          // 如果响应是一个对象，确保它有一个success字段
          if (!response.data.hasOwnProperty('success')) {
            return {
              data: {
                success: true,
                message: `同步任务 ${id} 已删除`
              }
            };
          }
        }

        // 保持原始响应格式
        return response;
      });
  },

  // 检测数据完整性
  checkDataIntegrity(symbol, timeframe, params) {
    return axios.get(`${getApiUrl('market').replace('/api/v1', '')}/data/integrity/${symbol}/${timeframe}`, { params });
  },

  // 检测数据缺口
  detectDataGaps(sourceId, params = {}) {
    console.log(`API调用 - detectDataGaps: sourceId=${sourceId}, 类型=${typeof sourceId}`);

    // 确保sourceId参数是字符串类型
    if (sourceId === null || sourceId === undefined) {
      console.error('无效的数据源ID');
      return Promise.reject(new Error('无效的数据源ID'));
    }

    // 转换为字符串格式
    const sourceIdStr = String(sourceId);
    console.log(`API调用 - 转换后的sourceId=${sourceIdStr}`);

    // 使用标准的API路径 - 不需要重复的/api/v1前缀
    const path = `${getApiUrl('market').replace('/api/v1', '')}/data/sources/${sourceIdStr}/gaps`;

    console.log('检测数据缺口请求路径:', path);
    return axios.get(path, { params });
  },

  // 修复数据缺口
  repairDataGaps(sourceId, repairData) {
    console.log('API调用: repairDataGaps - 数据源ID:', sourceId, '类型:', typeof sourceId);

    // 处理sourceId可能为对象或null的情况
    if (sourceId === null || sourceId === undefined) {
      console.error('数据源ID为空，无法修复数据缺口');
      return Promise.reject(new Error('数据源ID为空'));
    }

    // 转换sourceId为字符串形式
    const sourceIdStr = String(sourceId);
    console.log('转换后的数据源ID:', sourceIdStr);

    // 使用标准的API路径 - 不需要重复的/api/v1前缀
    const path = `${getApiUrl('market').replace('/api/v1', '')}/data/sources/${sourceIdStr}/repair-gaps`;

    console.log('修复数据缺口请求路径:', path);
    return axios.post(path, repairData)
      .catch(error => {
        console.error('修复数据缺口请求失败:', error);
        throw error;
      });
  },

  // 检查修复任务状态
  checkRepairTaskStatus(taskId) {
    if (!taskId) {
      console.error('任务ID为空，无法检查状态');
      return Promise.reject(new Error('任务ID为空'));
    }

    // 尝试使用不同的API路径格式
    const path1 = `${getApiUrl('market').replace('/api/v1', '')}/data/tasks/${taskId}/status`;
    const path2 = `${getApiUrl('market')}/data/sync-tasks/${taskId}`;

    console.log('尝试检查修复任务状态，路径1:', path1);

    // 先尝试第一个路径
    return axios.get(path1)
      .catch(error1 => {
        console.warn('第一个路径失败，尝试备用路径:', path2);

        // 如果第一个路径失败，尝试第二个路径
        return axios.get(path2)
          .catch(error2 => {
            console.error('所有路径均失败:');
            console.error('第一个路径错误:', error1);
            console.error('第二个路径错误:', error2);
            throw error2;
          });
      });
  },

  // 评估数据质量
  evaluateDataQuality(sourceId, timeframe) {
    console.log('API调用: evaluateDataQuality - 数据源ID:', sourceId, '类型:', typeof sourceId, '时间级别:', timeframe);

    // 处理sourceId可能为对象或null的情况
    if (sourceId === null || sourceId === undefined) {
      console.error('数据源ID为空，无法评估数据质量');
      return Promise.reject(new Error('数据源ID为空'));
    }

    // 转换sourceId为字符串形式
    const sourceIdStr = String(sourceId);
    console.log('转换后的数据源ID:', sourceIdStr);

    // 使用标准的API路径，添加时间级别参数
    // getApiUrl返回的是http://localhost:8005/api/v1，所以正确的路径是/api/v1/data/sources/...
    // 不再移除/api/v1前缀，因为后端API路由已经包含了这个前缀
    const path = `${getApiUrl('market')}/data/sources/${sourceIdStr}/quality${timeframe ? `?timeframe=${timeframe}` : ''}`;

    console.log('评估数据质量请求路径:', path);
    return axios.get(path)
      .catch(error => {
        console.error('评估数据质量请求失败:', error);
        throw error;
      });
  },

  // 获取数据质量统计
  getDataQualityStats(sourceId) {
    const url = sourceId
      ? `${getApiUrl('market')}/data/stats/${sourceId}`
      : `${getApiUrl('market')}/data/stats`;
    return axios.get(url);
  },

  // 获取同步任务检查点
  getSyncCheckpoints(taskId) {
    return axios.get(`${getApiUrl('market')}/data/sync-tasks/${taskId}/checkpoints`);
  },

  // 修复异常
  fixAnomaly(sourceId, anomalyData) {
    console.log('API调用: fixAnomaly - 数据源ID:', sourceId, '类型:', typeof sourceId);

    // 处理sourceId可能为对象或null的情况
    if (sourceId === null || sourceId === undefined) {
      console.error('数据源ID为空，无法修复异常');
      return Promise.reject(new Error('数据源ID为空'));
    }

    // 转换sourceId为字符串形式
    const sourceIdStr = String(sourceId);
    console.log('转换后的数据源ID:', sourceIdStr);

    // 使用标准的API路径
    const path = `${getApiUrl('market')}/data/sources/${sourceIdStr}/fix-anomaly`;

    console.log('修复异常请求路径:', path);
    return axios.post(path, anomalyData)
      .catch(error => {
        console.error('修复异常请求失败:', error);
        throw error;
      });
  },

  // 格式化异常类型
  formatAnomalyType(type) {
    const typeMap = {
      'price_gap': '价格跳空',
      'volume_anomaly': '成交量异常',
      'data_quality': '数据质量问题'
    };
    return typeMap[type] || type;
  },

  // 获取修复任务列表
  getRepairTasks(params) {
    // 可能尚未实现，目前使用同步任务接口作为演示
    return axios.get(`${getApiUrl('market')}/data/repair-tasks`, { params })
      .then(response => {
        if (Array.isArray(response.data)) {
          return { data: response.data };
        } else if (response.data && Array.isArray(response.data.data)) {
          return response;
        } else if (response.data && response.data.success && Array.isArray(response.data.data)) {
          return response;
        } else {
          return { data: [] };
        }
      })
      .catch(() => {
        console.log('修复任务接口可能未实现，返回空数组');
        return { data: [] };
      });
  },

  // 批量处理任务
  batchProcessTasks(taskIds, action) {
    return axios.post(`${getApiUrl('market')}/data/tasks/batch-${action}`, { task_ids: taskIds })
      .catch(error => {
        console.error(`批量${action}任务失败:`, error);
        throw error;
      });
  },

  // 更新任务优先级
  updateTaskPriority(taskId, priority) {
    return axios.post(`${getApiUrl('market')}/data/update-priority`, {
      task_id: taskId,
      new_priority: priority
    }).catch(error => {
      console.error('更新任务优先级失败:', error);
      throw error;
    });
  },

  // 重试任务
  retryTask(taskId) {
    console.log(`重试任务API调用: 任务ID=${taskId}`);
    return axios.post(`${getApiUrl('market')}/data/tasks/${taskId}/retry`)
      .then(response => {
        console.log('重试任务成功:', response.data);
        return response;
      })
      .catch(error => {
        console.error('重试任务API错误:', error);
        if (error.response) {
          console.error('错误状态码:', error.response.status);
          console.error('错误详情:', error.response.data);
        }
        throw error;
      });
  },

  // 归档任务
  archiveTask(taskId) {
    return axios.post(`${getApiUrl('market')}/data/sync-tasks/${taskId}/archive`)
      .catch(error => {
        console.error('归档任务失败:', error);
        throw error;
      });
  },

  // 批量归档任务
  batchArchiveTasks(taskIds) {
    return axios.post(`${getApiUrl('market')}/data/tasks/batch-archive`, { task_ids: taskIds })
      .catch(error => {
        console.error('批量归档任务失败:', error);
        throw error;
      });
  }
};

// 风险管理相关API
export const riskAPI = {
  // 获取风险指标
  getRiskMetrics() {
    return axios.get(`${getApiUrl('risk')}/risk/metrics`);
  },

  // 获取资金配置
  getFundSettings() {
    return axios.get(`${getApiUrl('risk')}/risk/fund-settings`);
  },

  // 保存资金配置
  saveFundSettings(data) {
    return axios.post(`${getApiUrl('risk')}/risk/fund-settings`, data);
  },

  // 获取交易限制
  getTradeLimits() {
    return axios.get(`${getApiUrl('risk')}/risk/trade-limits`);
  },

  // 保存交易限制
  saveTradeLimits(data) {
    return axios.post(`${getApiUrl('risk')}/risk/trade-limits`, data);
  },

  // 获取预警设置
  getAlertSettings() {
    return axios.get(`${getApiUrl('risk')}/risk/alert-settings`);
  },

  // 保存预警设置
  saveAlertSettings(data) {
    return axios.post(`${getApiUrl('risk')}/risk/alert-settings`, data);
  }
};

// 信号交易相关API
export const signalAPI = {
  // 获取所有信号
  getSignals(params) {
    return axios.get(`${getApiUrl('signal')}/signal`, { params });
  },

  // 获取单个信号
  getSignal(id) {
    return axios.get(`${getApiUrl('signal')}/signal/${id}`);
  },

  // 创建新信号
  createSignal(data) {
    return axios.post(`${getApiUrl('signal')}/signal`, data);
  },

  // 更新信号状态
  updateSignal(id, data) {
    return axios.put(`${getApiUrl('signal')}/signal/${id}`, data);
  },

  // 取消信号
  cancelSignal(id) {
    return axios.delete(`${getApiUrl('signal')}/signal/${id}`);
  },

  // 自动生成信号
  generateSignal(params) {
    return axios.post(`${getApiUrl('signal')}/signal/generate`, params);
  },

  // 获取信号订单
  getSignalOrders(signalId) {
    return axios.get(`${getApiUrl('signal')}/signal/${signalId}/orders`);
  }
};

// 交易相关API
export const tradingAPI = {
  // 获取交易订单列表
  getOrders(params = {}) {
    return axios.get(`${getApiUrl('trading')}/trading/orders`, { params });
  },

  // 获取单个交易订单详情
  getOrder(orderId) {
    return axios.get(`${getApiUrl('trading')}/trading/orders/${orderId}`);
  },

  // 获取交易执行记录列表
  getExecutions(params = {}) {
    return axios.get(`${getApiUrl('trading')}/trading/executions`, { params });
  },

  // 获取交易系统状态
  getTradingStatus() {
    return axios.get(`${getApiUrl('trading')}/trading/status`);
  },

  // 取消订单
  cancelOrder(orderId) {
    return axios.post(`${getApiUrl('trading')}/trading/orders/${orderId}/cancel`);
  },

  // 创建新订单
  createOrder(orderData) {
    return axios.post(`${getApiUrl('trading')}/trading/orders`, orderData);
  }
};

// 策略优化模块接口
export const getStrategyPerformance = async (strategyId, startDate = null, endDate = null) => {
  let url = `${getApiUrl('main')}/strategies/${strategyId}/performance`;
  if (startDate && endDate) {
    url += `?start_date=${startDate}&end_date=${endDate}`;
  }
  return await axiosInstance.get(url);
};

export const getStrategyOptimization = async (strategyId) => {
  return await axiosInstance.get(`${getApiUrl('main')}/strategies/${strategyId}/optimization`);
};

export const compareStrategies = async (strategyIds) => {
  const idsParam = strategyIds.join('&strategy_ids=');
  return await axiosInstance.get(`${getApiUrl('main')}/strategies/compare?strategy_ids=${idsParam}`);
};

export const applyOptimization = async (strategyId, optimizationParams) => {
  return await axiosInstance.post(
    `${getApiUrl('main')}/strategies/${strategyId}/optimize`,
    optimizationParams
  );
};

// 市场数据相关API
export const marketDataAPI = {
  // 获取支持的交易所列表
  getExchanges: async () => {
    try {
      return await service.get('/api/v1/market/exchanges');
    } catch (error) {
      console.error('获取交易所列表失败：', error);
      throw error;
    }
  },

  // 获取交易对列表
  getSymbols: async (exchange) => {
    try {
      return await service.get(`/api/v1/market/symbols?exchange=${exchange}`);
    } catch (error) {
      console.error('获取交易对列表失败：', error);
      throw error;
    }
  },

  // 获取K线数据
  getKlines: async (params) => {
    try {
      return await service.get('/api/v1/market/klines', { params });
    } catch (error) {
      console.error('获取K线数据失败：', error);
      throw error;
    }
  }
};

// 用户账户管理API
export const getUsers = (params) => {
  return service.get('/account/users', { params });
};

export const getUser = (userId) => {
  return service.get(`/account/users/${userId}`);
};

export const createUser = (data) => {
  return service.post('/account/users', data);
};

export const updateUser = (userId, data) => {
  return service.put(`/account/users/${userId}`, data);
};

export const deleteUser = (userId) => {
  return service.delete(`/account/users/${userId}`);
};

export const changeUserPassword = (userId, data) => {
  return service.put(`/account/users/${userId}/password`, data);
};

export const lockUser = (userId, data) => {
  return service.post(`/account/users/${userId}/lock`, data);
};

export const unlockUser = (userId) => {
  return service.post(`/account/users/${userId}/unlock`);
};

export const enableUser = (userId) => {
  return service.post(`/account/users/${userId}/enable`);
};

export const disableUser = (userId) => {
  return service.post(`/account/users/${userId}/disable`);
};

// 角色管理API
export const getRoles = (params) => {
  return service.get('/account/roles', { params });
};

export const getRole = (roleId) => {
  return service.get(`/account/roles/${roleId}`);
};

export const createRole = (data) => {
  return service.post('/account/roles', data);
};

export const updateRole = (roleId, data) => {
  return service.put(`/account/roles/${roleId}`, data);
};

export const deleteRole = (roleId) => {
  return service.delete(`/account/roles/${roleId}`);
};

export const getRolePermissions = (roleId) => {
  return service.get(`/account/roles/${roleId}/permissions`);
};

export const updateRolePermissions = (roleId, data) => {
  return service.put(`/account/roles/${roleId}/permissions`, data);
};

// 权限管理API
export const getPermissions = (params) => {
  return service.get('/account/permissions', { params });
};

export const getUserPermissions = (userId) => {
  return service.get(`/account/users/${userId}/permissions`);
};

export const setUserDirectPermissions = (userId, data) => {
  return service.put(`/account/users/${userId}/direct-permissions`, data);
};

export const checkUserPermission = (userId, data) => {
  return service.post(`/account/users/${userId}/check-permission`, data);
};

export const checkUserPermissions = (userId, data) => {
  return service.post(`/account/users/${userId}/check-permissions`, data);
};

// 用户统计API
export const getUserStatistics = () => {
  return service.get('/account/statistics');
};

export const getUserActivity = (userId, params) => {
  return service.get(`/account/users/${userId}/activity`, { params });
};

export const getUserProfile = () => {
  return service.get('/user/profile');
};

export const updateUserProfile = (data) => {
  return service.put('/user/profile', data);
};

export default {
  authAPI,
  strategy: strategyAPI,
  backtest: backtestAPI,
  data: dataAPI,
  config: configAPI,
  risk: riskAPI,
  signal: signalAPI,
  trading: tradingAPI,
  userSettings: userSettingsAPI,
  marketData: marketDataAPI,
  notification: notificationApi,
  user: {
    getUsers,
    getUser,
    createUser,
    updateUser,
    deleteUser,
    getUserProfile,
    updateUserProfile,
    getRoles,
    getPermissions
  }
};