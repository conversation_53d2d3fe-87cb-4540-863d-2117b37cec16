#!/usr/bin/env python3
"""
最终测试缓存图表修复
"""

import requests
import json
import time

def test_cache_chart_final():
    """最终测试缓存图表修复"""
    
    print("🔍 最终测试缓存图表修复...")
    print("=" * 60)
    
    # 获取认证token
    print("\n1. 🔐 获取认证令牌...")
    try:
        auth_response = requests.post('http://localhost:8000/api/v1/auth/token', 
                                     json={'username': 'admin', 'password': 'admin123'})
        
        if auth_response.status_code != 200:
            print(f"❌ 认证失败: {auth_response.status_code}")
            return
        
        token = auth_response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print(f"✅ 认证成功")
        
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return
    
    # 生成大量缓存活动
    print("\n2. 🔄 生成大量缓存活动...")
    for round_num in range(3):
        print(f"   第{round_num+1}轮缓存活动...")
        
        for i in range(10):
            try:
                # 调用各种API生成缓存活动
                requests.get('http://localhost:8000/api/v1/performance/summary', headers=headers)
                requests.get('http://localhost:8000/api/v1/performance/analysis', headers=headers)
                requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
                
                if i % 3 == 0:
                    print(f"      已完成 {i+1}/10 次调用")
                    
            except Exception as e:
                print(f"      ❌ API调用异常: {e}")
        
        # 等待一下让缓存统计更新
        time.sleep(2)
    
    # 检查缓存统计
    print("\n3. 📊 检查最终缓存统计...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/cache/stats', headers=headers)
        if response.status_code == 200:
            stats = response.json()['data']
            print(f"   最终缓存统计:")
            print(f"      大小: {stats['size']}")
            print(f"      命中: {stats['hits']}")
            print(f"      未命中: {stats['misses']}")
            print(f"      命中率: {stats['hit_rate']:.2%}")
            
            if stats['hits'] > 0:
                print(f"   ✅ 缓存系统正常工作，有真实的缓存活动")
            else:
                print(f"   ⚠️ 缓存系统可能没有活动")
        else:
            print(f"   ❌ 获取缓存统计失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 检查缓存历史数据
    print("\n4. 📈 检查缓存历史数据...")
    try:
        response = requests.get('http://localhost:8000/api/v1/performance/metrics/cache?limit=20', headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                history_data = result['data']
                print(f"   ✅ 缓存历史数据API正常")
                print(f"   历史记录数量: {len(history_data)}")
                
                if history_data:
                    print(f"   📊 历史数据样本:")
                    for i, record in enumerate(history_data[-3:]):
                        print(f"      记录{i+1}:")
                        print(f"         时间: {record['timestamp'][:19]}")
                        print(f"         命中率: {record['hit_rate']:.2%}")
                        print(f"         缓存大小: {record['size']}")
                        print(f"         命中次数: {record['hits']}")
                        print(f"         未命中次数: {record['misses']}")
                    
                    print(f"\n   🎯 数据质量验证:")
                    latest = history_data[-1]
                    print(f"      时间戳格式正确: {'✅' if 'T' in latest['timestamp'] else '❌'}")
                    print(f"      命中率合理: {'✅' if 0 <= latest['hit_rate'] <= 1 else '❌'}")
                    print(f"      缓存大小合理: {'✅' if latest['size'] >= 0 else '❌'}")
                    print(f"      统计数据一致: {'✅' if latest['hits'] >= 0 and latest['misses'] >= 0 else '❌'}")
                    
                    # 验证数据是否真实（不是模拟数据）
                    hit_rates = [r['hit_rate'] for r in history_data]
                    sizes = [r['size'] for r in history_data]
                    
                    # 检查是否有重复的精确值（模拟数据的特征）
                    unique_hit_rates = len(set(hit_rates))
                    unique_sizes = len(set(sizes))
                    
                    print(f"      命中率变化多样性: {'✅' if unique_hit_rates > 1 or len(hit_rates) == 1 else '❌'}")
                    print(f"      缓存大小变化多样性: {'✅' if unique_sizes > 1 or len(sizes) == 1 else '❌'}")
                    
                    if len(history_data) >= 2:
                        print(f"      数据时间序列正确: {'✅' if history_data[-1]['timestamp'] > history_data[-2]['timestamp'] else '❌'}")
                    
                else:
                    print(f"   ⚠️ 暂无历史数据（需要时间积累）")
                    print(f"   💡 建议：等待几分钟让系统记录历史数据")
            else:
                print(f"   ❌ API返回失败: {result.get('message', '未知错误')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 前端图表验证指导
    print("\n5. 🌐 前端图表验证指导...")
    print("   请在浏览器中执行以下步骤:")
    print("   1. 访问 http://localhost:8080/#/performance")
    print("   2. 点击'缓存管理'标签页")
    print("   3. 打开浏览器开发者工具(F12)")
    print("   4. 查看控制台(Console)标签页")
    print("   5. 查找以下调试信息:")
    print("      - '🚀 CacheManager 组件已挂载'")
    print("      - '🎯 initChart 被调用'")
    print("      - '✅ ECharts实例创建成功'")
    print("      - '🔍 开始加载缓存历史数据'")
    print("      - '📈 updateChart 被调用'")
    print("   6. 检查'缓存性能趋势'区域是否显示图表")
    print("   7. 如果图表仍然空白，查看控制台是否有错误信息")
    
    print("\n6. 🔧 故障排除建议...")
    print("   如果图表仍然不显示，可能的原因:")
    print("   - ECharts库加载问题：检查网络连接和CDN")
    print("   - DOM元素尺寸问题：检查CSS样式")
    print("   - 数据格式问题：检查控制台的数据转换日志")
    print("   - 异步加载时序问题：查看初始化和数据加载的顺序")
    
    print("\n" + "=" * 60)
    print("🔍 缓存图表最终测试完成")
    print("💡 请按照上述指导检查前端图表显示效果")

if __name__ == "__main__":
    test_cache_chart_final()
