// 浏览器控制台测试脚本
// 在缓存管理页面的浏览器控制台中运行此脚本

console.log('🧪 开始浏览器控制台测试...');

// 1. 检查Vue组件实例
if (window.Vue && window.Vue.prototype) {
    console.log('✅ Vue已加载');
} else {
    console.error('❌ Vue未加载');
}

// 2. 检查ECharts
if (window.echarts) {
    console.log('✅ ECharts已加载');
} else {
    console.error('❌ ECharts未加载');
}

// 3. 查找缓存图表DOM元素
const chartElement = document.querySelector('.cache-chart');
if (chartElement) {
    console.log('✅ 找到图表DOM元素');
    console.log('图表元素尺寸:', {
        width: chartElement.clientWidth,
        height: chartElement.clientHeight,
        offsetWidth: chartElement.offsetWidth,
        offsetHeight: chartElement.offsetHeight
    });
    
    // 4. 手动创建图表实例
    if (window.echarts) {
        console.log('🚀 手动创建ECharts实例...');
        
        try {
            const myChart = echarts.init(chartElement);
            console.log('✅ ECharts实例创建成功');
            
            // 5. 设置测试数据和配置
            const testData = [
                { time: new Date(Date.now() - 4 * 60000), hitRate: 0.3, size: 2 },
                { time: new Date(Date.now() - 3 * 60000), hitRate: 0.5, size: 3 },
                { time: new Date(Date.now() - 2 * 60000), hitRate: 0.7, size: 2 },
                { time: new Date(Date.now() - 1 * 60000), hitRate: 0.6, size: 4 },
                { time: new Date(), hitRate: 0.8, size: 3 }
            ];
            
            const option = {
                title: {
                    text: '缓存性能趋势 (手动测试)',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        const time = new Date(params[0].value[0]).toLocaleString();
                        let result = `${time}<br/>`;
                        
                        params.forEach(param => {
                            if (param.seriesName === '命中率') {
                                result += `${param.seriesName}: ${(param.value[1] * 100).toFixed(1)}%<br/>`;
                            } else {
                                result += `${param.seriesName}: ${param.value[1]}<br/>`;
                            }
                        });
                        
                        return result;
                    }
                },
                legend: {
                    data: ['命中率', '缓存大小'],
                    bottom: 0
                },
                xAxis: {
                    type: 'time',
                    splitLine: {
                        show: false
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '命中率',
                        min: 0,
                        max: 1,
                        axisLabel: {
                            formatter: function(value) {
                                return (value * 100) + '%';
                            }
                        },
                        splitLine: {
                            show: true
                        }
                    },
                    {
                        type: 'value',
                        name: '缓存大小',
                        splitLine: {
                            show: false
                        }
                    }
                ],
                series: [
                    {
                        name: '命中率',
                        type: 'line',
                        data: testData.map(item => [item.time, item.hitRate]),
                        lineStyle: {
                            width: 2
                        },
                        yAxisIndex: 0
                    },
                    {
                        name: '缓存大小',
                        type: 'line',
                        data: testData.map(item => [item.time, item.size]),
                        lineStyle: {
                            width: 2,
                            type: 'dashed'
                        },
                        yAxisIndex: 1
                    }
                ]
            };
            
            // 6. 应用配置
            console.log('📊 应用图表配置...');
            myChart.setOption(option);
            console.log('✅ 图表配置应用成功');
            
            // 7. 检查图表是否可见
            setTimeout(() => {
                const canvas = chartElement.querySelector('canvas');
                if (canvas) {
                    console.log('✅ 图表Canvas元素已创建');
                    console.log('Canvas尺寸:', {
                        width: canvas.width,
                        height: canvas.height
                    });
                } else {
                    console.error('❌ 图表Canvas元素未找到');
                }
            }, 1000);
            
        } catch (error) {
            console.error('❌ 创建图表失败:', error);
        }
    }
} else {
    console.error('❌ 未找到图表DOM元素 (.cache-chart)');
    
    // 检查是否在正确的页面
    const cacheTab = document.querySelector('[data-tab="cache"]') || 
                     document.querySelector('el-tab-pane[name="cache"]') ||
                     document.querySelector('.el-tab-pane');
    
    if (cacheTab) {
        console.log('💡 找到标签页元素，可能需要点击缓存管理标签');
    } else {
        console.log('💡 请确保在性能优化页面的缓存管理标签页中');
    }
}

console.log('🧪 浏览器控制台测试完成');
console.log('💡 如果图表仍然不显示，请检查:');
console.log('   1. 是否在缓存管理标签页');
console.log('   2. DOM元素是否存在');
console.log('   3. ECharts是否正确加载');
console.log('   4. 是否有CSS样式冲突');
