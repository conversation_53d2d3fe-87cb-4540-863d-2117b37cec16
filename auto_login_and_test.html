<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动登录并测试性能优化页面</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .step-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .step-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .big-button {
            font-size: 18px;
            padding: 15px 30px;
            background-color: #28a745;
        }
        .big-button:hover {
            background-color: #218838;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>自动登录并测试性能优化页面</h1>
        <p>此工具将自动完成登录流程并测试性能优化页面的所有功能。</p>

        <div class="progress">
            <div class="progress-bar" id="progress-bar" style="width: 0%"></div>
        </div>

        <button class="big-button" onclick="startAutoProcess()">开始自动修复和测试</button>

        <!-- 步骤1: 登录 -->
        <div class="step">
            <div class="step-title">步骤1: 用户登录</div>
            <div id="step1-result" class="step-result info">等待开始...</div>
        </div>

        <!-- 步骤2: 验证认证 -->
        <div class="step">
            <div class="step-title">步骤2: 验证认证状态</div>
            <div id="step2-result" class="step-result info">等待开始...</div>
        </div>

        <!-- 步骤3: 测试API -->
        <div class="step">
            <div class="step-title">步骤3: 测试性能API</div>
            <div id="step3-result" class="step-result info">等待开始...</div>
        </div>

        <!-- 步骤4: 打开页面 -->
        <div class="step">
            <div class="step-title">步骤4: 打开性能优化页面</div>
            <div id="step4-result" class="step-result info">等待开始...</div>
        </div>

        <!-- 步骤5: 验证页面 -->
        <div class="step">
            <div class="step-title">步骤5: 验证页面功能</div>
            <div id="step5-result" class="step-result info">等待开始...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let authToken = null;

        // 更新进度条
        function updateProgress(percentage) {
            document.getElementById('progress-bar').style.width = percentage + '%';
        }

        // 更新步骤结果
        function updateStepResult(stepId, className, message) {
            const element = document.getElementById(stepId);
            element.className = `step-result ${className}`;
            element.textContent = message;
        }

        // 延迟函数
        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 步骤1: 登录
        async function step1Login() {
            updateStepResult('step1-result', 'info', '正在登录...');
            updateProgress(10);

            try {
                const response = await axios.post(`${API_BASE}/auth/token`, {
                    username: 'admin',
                    password: 'admin123'
                });

                if (response.data.access_token) {
                    authToken = response.data.access_token;
                    
                    // 存储到localStorage
                    localStorage.setItem('token', authToken);
                    localStorage.setItem('userRole', 'admin');
                    localStorage.setItem('userInfo', JSON.stringify({
                        id: 1,
                        username: 'admin',
                        email: '<EMAIL>',
                        role: 'admin'
                    }));
                    
                    updateStepResult('step1-result', 'success', '✓ 登录成功，Token已获取并存储');
                    updateProgress(20);
                    return true;
                } else {
                    throw new Error('未获取到访问令牌');
                }
            } catch (error) {
                updateStepResult('step1-result', 'error', `✗ 登录失败: ${error.message}`);
                return false;
            }
        }

        // 步骤2: 验证认证状态
        async function step2VerifyAuth() {
            updateStepResult('step2-result', 'info', '正在验证认证状态...');
            updateProgress(30);

            try {
                // 检查localStorage中的token
                const storedToken = localStorage.getItem('token');
                if (!storedToken) {
                    throw new Error('localStorage中没有找到token');
                }

                // 验证token是否有效
                const response = await axios.get(`${API_BASE}/performance/summary`, {
                    headers: {
                        'Authorization': `Bearer ${storedToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.status === 200) {
                    updateStepResult('step2-result', 'success', '✓ 认证状态有效，可以访问API');
                    updateProgress(40);
                    return true;
                } else {
                    throw new Error(`API返回状态码: ${response.status}`);
                }
            } catch (error) {
                updateStepResult('step2-result', 'error', `✗ 认证验证失败: ${error.response?.status} ${error.message}`);
                return false;
            }
        }

        // 步骤3: 测试性能API
        async function step3TestAPI() {
            updateStepResult('step3-result', 'info', '正在测试性能API...');
            updateProgress(50);

            const apiEndpoints = [
                '/performance/summary',
                '/performance/analysis',
                '/performance/cache/stats',
                '/performance/api/stats',
                '/performance/memory/usage'
            ];

            let successCount = 0;
            const headers = {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            };

            for (const endpoint of apiEndpoints) {
                try {
                    const response = await axios.get(`${API_BASE}${endpoint}`, { headers });
                    if (response.status === 200) {
                        successCount++;
                    }
                    await delay(200); // 避免请求过快
                } catch (error) {
                    console.error(`API ${endpoint} 测试失败:`, error);
                }
            }

            if (successCount === apiEndpoints.length) {
                updateStepResult('step3-result', 'success', `✓ 所有API测试通过 (${successCount}/${apiEndpoints.length})`);
                updateProgress(70);
                return true;
            } else {
                updateStepResult('step3-result', 'warning', `⚠ 部分API测试通过 (${successCount}/${apiEndpoints.length})`);
                updateProgress(60);
                return successCount > 0;
            }
        }

        // 步骤4: 打开性能优化页面
        async function step4OpenPage() {
            updateStepResult('step4-result', 'info', '正在打开性能优化页面...');
            updateProgress(80);

            try {
                // 检查前端服务是否运行
                const frontendResponse = await fetch('http://localhost:8081/');
                
                if (frontendResponse.ok) {
                    // 打开性能优化页面
                    window.open('http://localhost:8081/#/performance', '_blank');
                    
                    updateStepResult('step4-result', 'success', '✓ 性能优化页面已打开');
                    updateProgress(90);
                    return true;
                } else {
                    throw new Error(`前端服务响应错误: ${frontendResponse.status}`);
                }
            } catch (error) {
                updateStepResult('step4-result', 'error', `✗ 打开页面失败: ${error.message}`);
                return false;
            }
        }

        // 步骤5: 验证页面功能
        async function step5VerifyPage() {
            updateStepResult('step5-result', 'info', '正在验证页面功能...');
            updateProgress(95);

            try {
                // 等待一段时间让页面加载
                await delay(2000);

                // 再次测试API以确保页面能正常调用
                const response = await axios.get(`${API_BASE}/performance/summary`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.data.success) {
                    updateStepResult('step5-result', 'success', '✓ 页面功能验证成功，数据加载正常');
                    updateProgress(100);
                    return true;
                } else {
                    throw new Error('API返回数据格式错误');
                }
            } catch (error) {
                updateStepResult('step5-result', 'error', `✗ 页面功能验证失败: ${error.message}`);
                return false;
            }
        }

        // 开始自动流程
        async function startAutoProcess() {
            try {
                // 步骤1: 登录
                const step1Success = await step1Login();
                if (!step1Success) return;

                await delay(500);

                // 步骤2: 验证认证
                const step2Success = await step2VerifyAuth();
                if (!step2Success) return;

                await delay(500);

                // 步骤3: 测试API
                const step3Success = await step3TestAPI();
                if (!step3Success) return;

                await delay(500);

                // 步骤4: 打开页面
                const step4Success = await step4OpenPage();
                if (!step4Success) return;

                await delay(1000);

                // 步骤5: 验证页面
                const step5Success = await step5VerifyPage();

                if (step5Success) {
                    alert('🎉 所有步骤完成！性能优化页面现在应该能正常工作了。');
                }

            } catch (error) {
                console.error('自动流程出错:', error);
                alert('自动流程出现错误，请查看控制台日志。');
            }
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('自动修复工具已准备就绪');
        });
    </script>
</body>
</html>
