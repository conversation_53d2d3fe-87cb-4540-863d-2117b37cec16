#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_all_settings():
    """测试所有系统设置功能"""
    
    base_url = "http://localhost:8000/api/v1/unified-config"
    
    print("🔧 测试系统设置所有功能...")
    print("=" * 50)
    
    # 1. 测试系统配置
    print("\n1. 测试系统配置...")
    try:
        # 获取系统配置
        response = requests.get(f"{base_url}/system")
        if response.status_code == 200:
            config = response.json()
            print("✅ 获取系统配置成功")
            
            # 更新系统配置
            test_config = {
                "trading": {
                    "enable_live_trading": False,
                    "risk_level": "medium"
                }
            }
            response = requests.put(f"{base_url}/system", json=test_config)
            if response.status_code == 200:
                print("✅ 更新系统配置成功")
            else:
                print(f"❌ 更新系统配置失败: {response.status_code}")
        else:
            print(f"❌ 获取系统配置失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 系统配置测试失败: {e}")
    
    # 2. 测试API密钥
    print("\n2. 测试API密钥...")
    try:
        # 获取API密钥
        response = requests.get(f"{base_url}/api-keys")
        if response.status_code == 200:
            print("✅ 获取API密钥成功")
            
            # 更新API密钥
            test_keys = [
                {
                    "id": 1,
                    "name": "Binance API",
                    "api_key": "test_key_123",
                    "secret_key": "test_secret_456",
                    "enabled": True,
                    "permissions": ["read", "trade"]
                }
            ]
            response = requests.put(f"{base_url}/api-keys", json=test_keys)
            if response.status_code == 200:
                print("✅ 更新API密钥成功")
            else:
                print(f"❌ 更新API密钥失败: {response.status_code}")
        else:
            print(f"❌ 获取API密钥失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API密钥测试失败: {e}")
    
    # 3. 测试系统参数
    print("\n3. 测试系统参数...")
    try:
        # 获取系统参数
        response = requests.get(f"{base_url}/system-params")
        if response.status_code == 200:
            print("✅ 获取系统参数成功")
            
            # 更新系统参数
            test_params = {
                "performance": {
                    "maxThreads": 16,
                    "cacheSize": 1024
                }
            }
            response = requests.post(f"{base_url}/system-params", json=test_params)
            if response.status_code == 200:
                print("✅ 更新系统参数成功")
            else:
                print(f"❌ 更新系统参数失败: {response.status_code}")
        else:
            print(f"❌ 获取系统参数失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 系统参数测试失败: {e}")
    
    # 4. 测试缓存功能
    print("\n4. 测试缓存功能...")
    try:
        # 获取缓存大小
        response = requests.get(f"{base_url}/cache-size")
        if response.status_code == 200:
            print("✅ 获取缓存大小成功")
            
            # 清理缓存
            response = requests.post(f"{base_url}/clear-cache")
            if response.status_code == 200:
                print("✅ 清理缓存成功")
            else:
                print(f"❌ 清理缓存失败: {response.status_code}")
        else:
            print(f"❌ 获取缓存大小失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 缓存功能测试失败: {e}")
    
    # 5. 测试数据维护
    print("\n5. 测试数据维护...")
    try:
        # 获取数据维护配置
        response = requests.get(f"{base_url}/data-maintenance")
        if response.status_code == 200:
            print("✅ 获取数据维护配置成功")
        else:
            print(f"❌ 获取数据维护配置失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 数据维护测试失败: {e}")
    
    # 6. 测试备份功能
    print("\n6. 测试备份功能...")
    try:
        # 获取备份列表
        response = requests.get(f"{base_url}/backups")
        if response.status_code == 200:
            print("✅ 获取备份列表成功")
        else:
            print(f"❌ 获取备份列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 备份功能测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 系统设置功能测试完成！")

if __name__ == "__main__":
    test_all_settings()
