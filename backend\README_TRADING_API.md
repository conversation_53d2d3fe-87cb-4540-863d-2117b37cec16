# 交易API服务

交易API服务提供了与交易所交互的接口，支持查询订单、创建订单、取消订单等功能。

## 功能特点

- 使用真实交易所API获取数据，不再使用模拟数据
- 支持查询订单列表和订单详情
- 支持创建和取消订单
- 支持查询交易执行记录
- 支持查询交易系统状态

## 安装和配置

1. 安装依赖：

```bash
pip install flask flask-cors ccxt
```

2. 配置API密钥：

复制`.env.example`文件为`.env`，并填入您的交易所API密钥：

```bash
cp .env.example .env
```

编辑`.env`文件，填入您的API密钥：

```
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here
```

## 启动服务

```bash
python trading_api.py
```

服务将在端口8003上启动。

## API端点

### 健康检查

```
GET /health
```

### 获取订单列表

```
GET /api/v1/trading/orders
```

查询参数：
- `page`: 页码，默认为1
- `limit`: 每页数量，默认为10

### 获取订单详情

```
GET /api/v1/trading/orders/{order_id}
```

### 创建订单

```
POST /api/v1/trading/orders
```

请求体：
```json
{
  "symbol": "BTC/USDT",
  "side": "buy",
  "type": "limit",
  "amount": 0.001,
  "price": 50000
}
```

### 取消订单

```
POST /api/v1/trading/orders/{order_id}/cancel
```

### 获取交易执行记录

```
GET /api/v1/trading/executions
```

查询参数：
- `page`: 页码，默认为1
- `limit`: 每页数量，默认为10

### 获取交易系统状态

```
GET /api/v1/trading/status
```

## 集成到前端

前端可以通过以下方式调用交易API：

```javascript
// 获取订单列表
axios.get('/api/v1/trading/orders')
  .then(response => {
    console.log(response.data);
  })
  .catch(error => {
    console.error(error);
  });

// 创建订单
axios.post('/api/v1/trading/orders', {
  symbol: 'BTC/USDT',
  side: 'buy',
  type: 'limit',
  amount: 0.001,
  price: 50000
})
  .then(response => {
    console.log(response.data);
  })
  .catch(error => {
    console.error(error);
  });
```

## 注意事项

- 请确保您的API密钥具有足够的权限
- 如果使用测试网络，请在`.env`文件中设置`USE_TESTNET=true`
- 在生产环境中，请确保使用HTTPS保护API通信
